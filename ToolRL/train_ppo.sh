#!/bin/bash

# GPU Configuration
export CUDA_VISIBLE_DEVICES=0,1,2,3
export N_GPUS=4
export ROLLOUT_TP_SIZE=1
export VLLM_ATTENTION_BACKEND=XFORMERS

# Search server configuration
# export SEARCH_SERVER_URL="http://your-server-url:8000/retrieve"  # 替换为你的实际服务器URL

# All the env variables below are set to 0 by default
export WITHLENGTH=0
export REFINEDREWARD=0
export COARSEREWARD=0
export STRICTMATCH=0
export CORRECTMAX1=0
export MAX1STEP30MAX3=0
export SCHEDULEREWARD=0
export SCHEDULELENGTH=0

# Paths and experiment configuration
export DATA_DIR="./dataset/train"
export BASE_MODEL="/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/base_model/Qwen2.5-3B-Instruct"
export EXPERIMENT_NAME="ppo-smartsearch-qwen2.5-3b"
export REWARD_VIS_DIR="./reward_plots/ppo"


# Run PPO training
bash ./examples/ppo_trainer/run_ppo.sh