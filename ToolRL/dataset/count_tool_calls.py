import json
import re

def count_tool_calls():
    # 读取JSON文件
    with open('/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train/strategyqa_processed_records.json', 'r') as f:
        data = json.load(f)
    
    # 统计变量
    total_entries = len(data)
    entries_with_tool_calls = 0
    tool_calls_count = {}  # 用于统计每个条目中tool_call的次数
    
    # 遍历所有条目
    for i, entry in enumerate(data):
        if 'output' in entry:
            # 使用正则表达式查找所有tool_call标签
            tool_calls = re.findall(r'<tool_call>.*?</tool_call>', entry['output'], re.DOTALL)
            if tool_calls:
                entries_with_tool_calls += 1
                tool_calls_count[i] = len(tool_calls)
    
    
    print(f"tool_call使用分布:")
    for entry_idx, count in tool_calls_count.items():
        print(f"条目 {entry_idx}: {count} 次tool_call")
    # 打印统计结果
    print(f"总条目数: {total_entries}")
    print(f"包含tool_call的条目数: {entries_with_tool_calls}")
    print(f"共调用tool_call的次数: {sum(tool_calls_count.values())}")

if __name__ == "__main__":
    count_tool_calls() 