{"id": "1afa02df02c908a558b4036e80242fac", "question": "A revolving door is convenient for two direction travel, but it also serves as a security measure at a what?", "question_concept": "revolving door", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bank", "library", "department store", "mall", "new york"]}, "answerKey": "A"}
{"id": "a7ab086045575bb497933726e4e6ad28", "question": "What do people aim to do at work?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["complete job", "learn from each other", "kill animals", "wear hats", "talk to each other"]}, "answerKey": "A"}
{"id": "b8c0a4703079cf661d7261a60a1bcbff", "question": "Where would you find magazines along side many other printed works?", "question_concept": "magazines", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["doctor", "bookstore", "market", "train station", "mortuary"]}, "answerKey": "B"}
{"id": "e68fb2448fd74e402aae9982aa76e527", "question": "Where are  you likely to find a hamburger?", "question_concept": "hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fast food restaurant", "pizza", "ground up dead cows", "mouth", "cow carcus"]}, "answerKey": "A"}
{"id": "2435de612dd69f2012b9e40d6af4ce38", "question": "James was looking for a good place to buy farmland.  Where might he look?", "question_concept": "farmland", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["midwest", "countryside", "estate", "farming areas", "illinois"]}, "answerKey": "A"}
{"id": "a4892551cb4beb279653ae52d0de4c89", "question": "What island country is ferret popular?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["own home", "north carolina", "great britain", "hutch", "outdoors"]}, "answerKey": "C"}
{"id": "118a9093a30695622363455e4d911866", "question": "In what Spanish speaking North American country can you get a great cup of coffee?", "question_concept": "cup of coffee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mildred's coffee shop", "mexico", "diner", "kitchen", "canteen"]}, "answerKey": "B"}
{"id": "05ea49b82e8ec519e82d6633936ab8bf", "question": "What do animals do when an enemy is approaching?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel pleasure", "procreate", "pass water", "listen to each other", "sing"]}, "answerKey": "D"}
{"id": "c0c07ce781653b2a2c01871ba2bcba93", "question": "Reading newspaper one of many ways to practice your what?", "question_concept": "reading newspaper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["literacy", "knowing how to read", "money", "buying", "money bank"]}, "answerKey": "A"}
{"id": "1d24f406b6828492040b405d3f35119c", "question": "What do people typically do while playing guitar?", "question_concept": "playing guitar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cry", "hear sounds", "singing", "arthritis", "making music"]}, "answerKey": "C"}
{"id": "57f92025d860e32c4e780c0d51c1c20c", "question": "What would vinyl be an odd thing to replace?", "question_concept": "vinyl", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pants", "record albums", "record store", "cheese", "wallpaper"]}, "answerKey": "E"}
{"id": "81eb4b2ee66edd8bc91ee944697c4e9f", "question": "If you want harmony, what is something you should try to do with the world?", "question_concept": "something you", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take time", "make noise", "make war", "make peace", "make haste"]}, "answerKey": "D"}
{"id": "d807e7ae60976324920c8d29eb42dad6", "question": "Where does a heifer's master live?", "question_concept": "heifer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["farm house", "barnyard", "stockyard", "slaughter house", "eat cake"]}, "answerKey": "A"}
{"id": "7ea9f721ffc662918bb0c0937a487f04", "question": "Aside from water and nourishment what does your dog need?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bone", "charm", "petted", "lots of attention", "walked"]}, "answerKey": "D"}
{"id": "fc1d33a2301a30214523c12573f81aba", "question": "Janet was watching the film because she liked what?", "question_concept": "watching film", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["erection", "laughter", "being entertained", "fear", "bordem"]}, "answerKey": "C"}
{"id": "3b8e1d236f5169b6c833a994d6d9c39a", "question": "What are you waiting alongside with when you're in a reception area?", "question_concept": "reception area", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motel", "chair", "hospital", "people", "hotels"]}, "answerKey": "D"}
{"id": "c5c4166f2ed3c2b3517b79e6848e9ae2", "question": "When drinking booze what can you do to stay busy?", "question_concept": "booze", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reach tentative agreement", "stay in bed", "stop bicycle", "examine thing", "suicide"]}, "answerKey": "D"}
{"id": "6dc5b2884737e66543ce65f8dc40c992", "question": "A fencing thrust with a sharp sword towards a person would result in what?", "question_concept": "fencing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["injury", "small cuts", "fever", "competition", "puncture wound"]}, "answerKey": "E"}
{"id": "8af63d58cc35061dec38e5448c325988", "question": "Unlike a spider and his many sight seers, people only have what?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tongues", "names", "brains", "feelings", "two eyes"]}, "answerKey": "E"}
{"id": "768fb09deab56046e1565b6a2556ad5c", "question": "Where do adults use glue sticks?", "question_concept": "glue stick", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "desk drawer", "at school", "office", "kitchen drawer"]}, "answerKey": "D"}
{"id": "cd639cf3ff82f825ace7dd2b087562bd", "question": "What could go on top of wood?", "question_concept": "wood", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lumberyard", "synagogue", "floor", "carpet", "hardware store"]}, "answerKey": "D"}
{"id": "8d79cc5e4eea11f50fab18fdea20fd4f", "question": "The artist was sitting quietly pondering, then suddenly he began to paint when what struck him?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sadness", "anxiety", "inspiration", "discomfort", "insights"]}, "answerKey": "C"}
{"id": "e5ad2184e37ae88b2bf46bf6bc0ed2f4", "question": "Though the thin film seemed fragile, for it's intended purpose it was actually nearly what?", "question_concept": "fragile", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["indestructible", "durable", "undestroyable", "indestructible", "unbreakable"]}, "answerKey": "D"}
{"id": "b8b287b6277fccd4b7c9c72577177328", "question": "Where could you find a toilet that only friends can use?", "question_concept": "toilet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rest area", "school", "stadium", "apartment", "hospital"]}, "answerKey": "D"}
{"id": "f646f3e064f06423fc25b98500796cf0", "question": "What is someone who isn't clever, bright, or competent called?", "question_concept": "clever", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clumsy", "ineffectual", "dull", "clumsy", "stupid"]}, "answerKey": "E"}
{"id": "b0f7d7978ac41c465108a92660d70e84", "question": "When wildlife reproduce we often refer to what comes out as what?", "question_concept": "reproduce", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["raise children", "have children", "photo copy", "offspring", "accidently got pregnant somehow"]}, "answerKey": "D"}
{"id": "54075de8b8b89ecef2e4eb4eaee2713d", "question": "The weasel was becoming a problem, it kept getting into the chicken eggs kept in the what?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forrest", "barn", "public office", "out of doors", "freezer"]}, "answerKey": "B"}
{"id": "65435b996ce9d1685bebb74b49c1ba7f", "question": "Blue read material outside of his comfort zone because he wanted to gain what?", "question_concept": "reading", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new perspective", "entertained", "understanding", "hunger", "tired eyes"]}, "answerKey": "A"}
{"id": "9889e5389917d812c09d6e5d382d333d", "question": "After he got hired he hoped for success at his what?", "question_concept": "success", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vocation", "new job", "michigan", "working hard", "manual"]}, "answerKey": "B"}
{"id": "a651ffa44ac5febf0aede6748899b981", "question": "Committing perjury is a serious what?", "question_concept": "committing perjury", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["indictment", "crime", "violence", "lie", "go to jail"]}, "answerKey": "B"}
{"id": "bdcfbe2132295d437e4c5701085f19c0", "question": "If you are prone to postpone work what will you have to do in order to finish on time?", "question_concept": "postpone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat", "hasten", "antedate", "bring forward", "advance"]}, "answerKey": "B"}
{"id": "8d3dc21a53523850ec80771daaa5ff20", "question": "James wanted to find an old underground map from the 50s.  Where might he look for one?", "question_concept": "underground map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["library", "subway station", "county engineer's office", "super market", "home"]}, "answerKey": "A"}
{"id": "a80ee7775e934c423012fe98e20ba28b", "question": "Sean was in a rush to get home, but the light turned yellow and he was forced to do what?", "question_concept": "rush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take time", "dawdle", "go slowly", "ocean", "slow down"]}, "answerKey": "E"}
{"id": "48a315cfa3ce11f7a9d615bc854331d5", "question": "Where would a person be doing when having to wait their turn?", "question_concept": "wait turn", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have patience", "get in line", "sing", "stand in line", "turn left"]}, "answerKey": "D"}
{"id": "4acd496cc78d96c2431279a5fd87de7c", "question": "She was always helping at the senior center, it brought her what?", "question_concept": "helping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "heart", "feel better", "pay", "happiness"]}, "answerKey": "E"}
{"id": "91e0f4ab62c9d2fd440d73a3f5308d96", "question": "The lock kept the steering wheel from moving, but the thief still took his chances and began to work on the what?", "question_concept": "lock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["keep cloesd", "train", "ignition switch", "drawer", "firearm"]}, "answerKey": "C"}
{"id": "b61e849e44db16a581f0b65e28ab95dc", "question": "Who is a police officer likely to work for?", "question_concept": "police officer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beat", "direct traffic", "city", "street", "president"]}, "answerKey": "C"}
{"id": "ba6bd1bdef02d0ebfe5370f92365ae18", "question": "If you have leftover cake, where would you put it?", "question_concept": "cake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["quandry", "refrigerator", "oven", "night stand", "bakery"]}, "answerKey": "B"}
{"id": "dc55d473c22b04877b11d584f9548194", "question": "A human wants to submerge himself in water, what should he use?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["whirlpool bath", "coffee cup", "cup", "soft drink", "puddle"]}, "answerKey": "A"}
{"id": "113aaea2b1a27a976547f54e531d99bb", "question": "Where is a doormat likely to be in front of?", "question_concept": "doormat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["facade", "front door", "doorway", "entrance porch", "hallway"]}, "answerKey": "B"}
{"id": "ba640b9634ad6b4ad98b17b4f152e562", "question": "Bob the lizard lives in a warm place with lots of water.  Where does he probably live?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rock", "tropical rainforest", "jazz club", "new mexico", "rocky places"]}, "answerKey": "B"}
{"id": "750ebdf36a0b3b407be0fe2163e3700b", "question": "August needed  money because he was afraid that he'd be kicked out of his house.  What did he need money to do?", "question_concept": "money", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["control people", "pay bills", "hurt people", "buy food", "get things"]}, "answerKey": "B"}
{"id": "8f01273422a370a8dbda6bf473a395a0", "question": "He needed more information to fix it, so he consulted the what?", "question_concept": "information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chickens", "google", "newspaper", "online", "manual"]}, "answerKey": "E"}
{"id": "e6586bba9fe96d38792e6e6d4f2703dc", "question": "Where can you put a picture frame when it's not hung vertically?", "question_concept": "picture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["art show", "wall", "newspaper", "car", "table"]}, "answerKey": "E"}
{"id": "6e433471d0e2590b8c73ceef275022b1", "question": "James knew that he shouldn't have been buying beer for minors.  He didn't even get paid for it.  Why was this bad?", "question_concept": "buying beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lose money", "fun", "have no money", "broken law", "relaxation"]}, "answerKey": "D"}
{"id": "1bc986f8aea88d6927d8a45367855a94", "question": "What is the result of applying for  job?", "question_concept": "applying for job", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anxiety and fear", "increased workload", "praise", "less sleep", "being employed"]}, "answerKey": "E"}
{"id": "8d1563697d751a364d688d6701ebdb39", "question": "What must someone do before they shop?", "question_concept": "shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get money", "have money", "bring cash", "go to market", "bring cash"]}, "answerKey": "A"}
{"id": "91f512273a2da7ae796919069b20d6cf", "question": "Because John was first violin, he had to bring something important to work ever day. What did he need to bring to work?", "question_concept": "first violin", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music store", "obesity", "symphony orchestra", "ochestra", "violin case"]}, "answerKey": "E"}
{"id": "49cda7eedbf63b3f38e59ba72f1ee1f9", "question": "What is a place that usually does not have an elevator and that sometimes has a telephone book?", "question_concept": "telephone book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["at hotel", "kitchen", "library", "telephone booth", "house"]}, "answerKey": "E"}
{"id": "a588407ecaecf0f30c2241c30b470fe2", "question": "Who is likely to be excited about a crab?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fish market", "pet shop", "fishmongers", "intertidal zone", "obesity"]}, "answerKey": "C"}
{"id": "011096bcfff30fd38046cf9db3a411c5", "question": "Where can a human find clothes that aren't pants?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pants shop", "on planet earth", "dress shop", "school", "train wreck"]}, "answerKey": "C"}
{"id": "435a728f45d32faa4b3c4553c966fd6b", "question": "If I was getting drunk, and people couldn't understand me, what might I be having?", "question_concept": "getting drunk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["a seizure", "slurred speech", "death", "forgetfulness", "pass out"]}, "answerKey": "B"}
{"id": "e953dee48c70159ad879143a319ec607", "question": "When a person is beginning work, what are they building?", "question_concept": "beginning work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["time", "accomplishing", "working", "momentum", "tiredness"]}, "answerKey": "D"}
{"id": "9c784727afd7176b54764055df7a7927", "question": "A child wants to play, what would they likely want?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fall down", "breathe", "play tag", "be dismembered by a chainsaw", "become adult"]}, "answerKey": "C"}
{"id": "b47d912136e3304cb5e5890b6b879551", "question": "Talking to the same person about the same thing over and over again is something someone can what?", "question_concept": "talking to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["social life", "friendship", "eye contact", "get tired of", "learn lessons from"]}, "answerKey": "D"}
{"id": "49b4c9e1bd7946a819e173ce8fa4c7c9", "question": "The teacher doesn't tolerate noise during a test in their what?", "question_concept": "noise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["movie theatre", "bowling alley", "factory", "store", "classroom"]}, "answerKey": "E"}
{"id": "950af0b765c298960ce3dada66df8db1", "question": "The freeway had no traffic and few buildings, where is it?", "question_concept": "freeway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["california", "countryside", "big town", "florida", "america"]}, "answerKey": "B"}
{"id": "63cf1adb5fe302b9867ead8bc8103d0b", "question": "Where would you go if you wanted to have fun with a few people?", "question_concept": "fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["watching television", "good", "cinema", "friend's house", "fairgrounds"]}, "answerKey": "D"}
{"id": "ede4d302fc2ffe07703158f83c1493f2", "question": "If there is a place that is hot and arid, what could it be?", "question_concept": "hot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bland", "lifeless", "sandy", "neutral", "freezing"]}, "answerKey": "B"}
{"id": "74ad13a03634e79c85382f1b90969b74", "question": "What is likely to satisfy someone's curiosity?", "question_concept": "curiosity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hear news", "read book", "see favorite show", "comedy show", "go somewhere"]}, "answerKey": "A"}
{"id": "49e466b1782aa4837dae53ff891fcdee", "question": "If you are in a bar in a glove shaped state where are you?", "question_concept": "bar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["in my pocket", "michigan", "new york city", "restaurant", "public house"]}, "answerKey": "B"}
{"id": "a8a8ae7792901c7179ff5538c701af1f", "question": "Where would a computer user be using their own computer?", "question_concept": "computer user", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hell", "school", "indoors", "internet cafe", "house"]}, "answerKey": "E"}
{"id": "2ffa3808ce26181926990b454e429c85", "question": "Crabs live in what sort of environment?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["maritime", "bodies of water", "saltwater", "galapagos", "fish market"]}, "answerKey": "C"}
{"id": "4319eaa36d256a92b72445c0392f9c94", "question": "Where can you find a snake in tall grass?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tree", "in a jar", "pet shops", "feild", "tropical forest"]}, "answerKey": "D"}
{"id": "ec79ef747bb89281923edb89ba26786d", "question": "What is a place that has a bench nestled in trees?", "question_concept": "bench", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["state park", "bus stop", "bus depot", "statue", "train station"]}, "answerKey": "A"}
{"id": "2d33cde5e3987adc8fa2bca0af4dd3dd", "question": "Where is a human likely to go as a result of being hungry?", "question_concept": "being hungry", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat in restaurant", "make bread", "have lunch", "cook dinner", "friends house"]}, "answerKey": "A"}
{"id": "cc46d936bf69d69a3863b0cb85d75c17", "question": "He was beginning to regret taking the fight when he saw how what his opponent was?", "question_concept": "regret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fun", "joy", "satisfaction", "confident", "pride"]}, "answerKey": "D"}
{"id": "46bc1a50eeead10509a43a048e01194e", "question": "Where would you find a single shower curtain being used?", "question_concept": "shower curtain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bathtub", "washing area", "hotel", "shower stall", "department store"]}, "answerKey": "A"}
{"id": "4336a8c55b7cb17275d1c60206cd2f18", "question": "Where is a good idea but not required to have a fire extinguisher?", "question_concept": "fire extinguisher", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school bus", "boat", "house", "hospital", "school"]}, "answerKey": "C"}
{"id": "a287575d3ba4b9f958536fc14a1f5b5a", "question": "What continent has the most castles?", "question_concept": "castle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fairy tale", "edinburgh", "germany", "europe", "antarctica"]}, "answerKey": "D"}
{"id": "f481dc35b0a97a20dc5cdfe1a59746e2", "question": "If you have to read a book that is very dry and long you may become what?", "question_concept": "read book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have time", "boring", "learn new", "enjoyable", "bored"]}, "answerKey": "E"}
{"id": "c1c7a9efa379b8a7024a71cf364a144c", "question": "Sally used a clipboard to hold her papers while she read off names at the beginning of the day.  Where might she work?", "question_concept": "clipboard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "windows 95", "office supply store", "see work", "school"]}, "answerKey": "E"}
{"id": "821b32d39f57396979069b948030afe9", "question": "The kids didn't clean up after they had done what?", "question_concept": "kids", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn things", "play games", "disneyland", "play with toys", "talking"]}, "answerKey": "D"}
{"id": "c68b4082a6872cf8198502651d0f3352", "question": "Despite the name a pawn can be quite versatile, all the parts are important in a what?", "question_concept": "pawn", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chess game", "scheme", "chess set", "checkers", "north carolina"]}, "answerKey": "A"}
{"id": "dd11fea36d89aa09f9a6069545ba4c9c", "question": "What would not be true about a basketball if it had a hole in it but it did not lose its general shape?", "question_concept": "basketball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["punctured", "popular in america", "full of air", "gone", "round"]}, "answerKey": "C"}
{"id": "7792b2c6518ecf9775efba6d41253312", "question": "If you are awaking multiple times throughout the night because a lot is on your mind, what is a likely cause?", "question_concept": "awaking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["irritability", "depression", "getting out of bed", "happiness", "discomfort"]}, "answerKey": "B"}
{"id": "1feb4c2a0e8ed638259f5d27b16eae9a", "question": "Where does a wild bird usually live?", "question_concept": "bird", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cage", "sky", "countryside", "desert", "windowsill"]}, "answerKey": "C"}
{"id": "2de08c7a518b7c226e19bdc8fc10ef1d", "question": "Where would you expect to find white mice?", "question_concept": "mice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bell cat", "bush", "attic", "countryside", "laboratory"]}, "answerKey": "E"}
{"id": "ea8664e77205224154f8519f922220e1", "question": "John felt that his actions were fate.   Harry said that he could have always made a different what?", "question_concept": "fate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["free will", "choice", "will", "alcohol", "freedom"]}, "answerKey": "B"}
{"id": "a64d45cecde84fdcf5f0a79805a0c6fe", "question": "What could committing murder prevent someone from doing?", "question_concept": "committing murder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go to jail", "cry", "find god", "guilty conscience", "problems"]}, "answerKey": "C"}
{"id": "60e92cd2f35c345872d1a898e1718d55", "question": "George didn't have a car, but he still had his two feet.   His socks were smelly and his soles were blistered, but that didn't matter.  He could still do what?", "question_concept": "feet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "walk", "stay still", "stink", "hands"]}, "answerKey": "B"}
{"id": "08f3c187908646997b9080c7e9ea7da4", "question": "A crane uses many a steel cable when working a what?", "question_concept": "steel cable", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["abaft", "ship", "winch", "construction site", "building"]}, "answerKey": "D"}
{"id": "9aff72f0c480c2b4edde45bd2e7e4870", "question": "What is the main purpose of farmers?", "question_concept": "farmers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["raise cattle", "grow corn", "farm land", "drive tractors", "supply food"]}, "answerKey": "E"}
{"id": "fd243c96edec5b1b8520d5bfeddc6622", "question": "Where can I put this penny to save for later?", "question_concept": "penny", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["piggy bank", "wallet", "toy", "ground", "pocket"]}, "answerKey": "A"}
{"id": "f5ec4fdfd0e37e733bfc1606b986f1e2", "question": "Where would you put uncooked crab meat?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wharf", "red lobster", "tidepools", "boss's office", "stew pot"]}, "answerKey": "E"}
{"id": "e3c6d147f8a727d314046e70e9579ba0", "question": "The man had a fear of illness, so he never visited friends who were a what?", "question_concept": "illness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sick person", "hospital", "elderly person", "graveyard", "doctor's office"]}, "answerKey": "A"}
{"id": "8ce13c6e08bf38d4cd4af756b661e47c", "question": "Where would you put pans if you want to bring them with you?", "question_concept": "pans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cooking", "cook food", "kitchen", "backpack", "drawer"]}, "answerKey": "D"}
{"id": "0f4159e80f8dbf682819215bbf0f5b5a_1", "question": "If you're remembering something, it's because of your what of it to begin with?", "question_concept": "remembering", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["knowledge", "knowing", "forgetful", "pleasure", "depression"]}, "answerKey": "B"}
{"id": "1a8b3c2a46efabcbd506f9cf70886ed0", "question": "Which large land mass is home to the most monkeys?", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["amazon basin", "friend's house", "lift number 3", "research laboratory", "african continent"]}, "answerKey": "E"}
{"id": "db0cfd52ca6b2bbfcf26d1a898fd929b", "question": "Friday was James's 5th Anniversary.  They planned on going to bed early so that they could spend a long time doing what?", "question_concept": "going to bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rest", "insomnia", "making love", "sleeping in", "texting"]}, "answerKey": "C"}
{"id": "400fb2e196e71abb70e5b3f9aab4b9ee", "question": "The teens were trying to hide that they get drink, but when they walked in the door their what gave it away?", "question_concept": "get drunk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["health", "fall down", "stagger", "get arrested", "vomit"]}, "answerKey": "C"}
{"id": "3fb36127a61903029a363911a1d2b1e9_1", "question": "You'll find a landing at the top of what?", "question_concept": "landing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "apartment building", "stairwell", "airport", "room"]}, "answerKey": "C"}
{"id": "8494b0b95533dcedbd76ae2916c481d4", "question": "Anybody could be hired in the kitchen, what was needed of them?", "question_concept": "anybody", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forget", "oil squeaky hinge", "question authority", "wash dishes", "oik squeaky hinge"]}, "answerKey": "D"}
{"id": "1531f1523f5fd24bbdb42c311dbf90e8", "question": "Where can you find a number of wind instruments together in public?", "question_concept": "wind instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music store", "create music", "zoo", "music room", "symphony"]}, "answerKey": "E"}
{"id": "716ce4404a84b42dd64e561390c4b53b", "question": "A mountie got off at a subway stop.  What city might he be in?", "question_concept": "subway stop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["urban area", "metropolis", "chicago", "new york city", "toronto"]}, "answerKey": "E"}
{"id": "5169f7ae0781b15161551de3a189ebef", "question": "What do you want someone to do when you illustrate point?", "question_concept": "illustrate point", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["did not understand", "accepting", "make clear", "understood", "understanding"]}, "answerKey": "E"}
{"id": "ef22ef7aeec70aaa688720f805c1cf38", "question": "Billy set aside a block of time for having fun after work. Why might he do this?", "question_concept": "having fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["happiness", "stress relief", "pleasure", "ocean", "may laugh"]}, "answerKey": "B"}
{"id": "514310637fb43a252bfadc8cbf79b277", "question": "The man in the white suit was very lazy.  He did nothing useful.  Meanwhile, the ban in the blue had put in effort and was very what?", "question_concept": "lazy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["restless", "active", "lazybutt", "productive", "hard work"]}, "answerKey": "D"}
{"id": "9370b2b0897b796dec4a40f107854c8d", "question": "What would you be unable to do if you have too much greed?", "question_concept": "greed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["keep things", "make friends", "play poker", "conquer opponent", "lie"]}, "answerKey": "B"}
{"id": "49902e768c45aa41a0f9f95be81114e5", "question": "It was a long trip from the farm, so he stayed in a hotel when he arrived at the what?", "question_concept": "hotel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bed away from home", "wwii bunker", "resort", "las vegas", "city"]}, "answerKey": "E"}
{"id": "e1f90cd664a6b150291e6d8444d85c54", "question": "I did not need a servant.  I was not a what?", "question_concept": "servant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["freedom", "rich person", "hired help", "in charge", "busy"]}, "answerKey": "B"}
{"id": "320ec9b68fdefe13d59cc8b628083790", "question": "How would you get from one side of a canal to another?", "question_concept": "canal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "amsterdam", "venice", "bridge", "barges to travel on"]}, "answerKey": "D"}
{"id": "964185aed0e381853332bca1a4d91f46", "question": "When learning about the world and different cultures, what is important if you are committed to eliminating preconceived notions", "question_concept": "learning about world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["newness", "loss of innocence", "enlightenment", "open mind", "smartness"]}, "answerKey": "D"}
{"id": "db8e010754c532d78635e5b7cf81a147", "question": "An underrated thing about computers is how they manage workflow, at one time it was a big deal when they could first do what?", "question_concept": "computers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["share files", "do arithmetic", "turn on", "cost money", "multitask"]}, "answerKey": "E"}
{"id": "998381f854f51da2a6ccde45909e5168", "question": "Obstructing justice is sometimes an excuse used for police brutality which causes what in people?", "question_concept": "obstructing justice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["committing perjury", "prosecution", "attack", "getting hurt", "riot"]}, "answerKey": "D"}
{"id": "bc38ad28e99cff7a65771233f734a007", "question": "While washing clothes they became what when caught on the sharp object?", "question_concept": "washing clothes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["damaged", "wet clothes", "wear out", "torn", "have fun"]}, "answerKey": "D"}
{"id": "e3949997bf9d02048cfa5d8dd0f287aa", "question": "Seafood restaurants are used to draw tourists where?", "question_concept": "seafood restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["maine", "shoe shop", "city", "boston", "coastal cities"]}, "answerKey": "E"}
{"id": "a7d51b753c2113d8b2dbd0ebb5375855", "question": "James's nice asked him about her grandfather. She was interested in learning about what?", "question_concept": "niece", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["family tree", "family reunion", "babysitting", "brother's house", "heirlooms"]}, "answerKey": "A"}
{"id": "********************************", "question": "James looked up and saw the start twinkling in the black yonder.  He marveled the sheer number of them and the size of what?", "question_concept": "stars", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["universe", "orbit", "night sky", "outer space", "his wallet"]}, "answerKey": "A"}
{"id": "5ac83e9e6fa9851ad3cccb0d57c1d88f", "question": "What would encourage someone to continue playing tennis?", "question_concept": "playing tennis", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["becoming tired", "tennis elbow", "exercise", "hunger", "victory"]}, "answerKey": "E"}
{"id": "2c0030cc14a27be2401dcfdaa501f0fc", "question": "James found the sound relaxing.   It was so relaxing he almost did what despite his efforts?", "question_concept": "relaxing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["deep breathing", "worried", "fall asleep", "invigorating", "feeling good"]}, "answerKey": "C"}
{"id": "feb83263e6be392351db0794004efc3f", "question": "What regions of a town would you have found a dime store?", "question_concept": "dime store", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["commercial building", "old movie", "small neighborhood", "past", "mall"]}, "answerKey": "C"}
{"id": "80697d599280d994d8a584c95824ef1f", "question": "Where might an unused chess set be stored?", "question_concept": "chess set", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toy store", "michigan", "living room", "attic", "cupboard"]}, "answerKey": "E"}
{"id": "3c1800e7dd96d37fdd3c51b9fe502342", "question": "james told his son to settle down and be careful.  There were many frogs mating in the area, and James didn't want his son to do what to them?", "question_concept": "settle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wander", "migrate", "scare", "disturb", "agitate"]}, "answerKey": "D"}
{"id": "4da33e6f4b789776acb1bc10195baa83", "question": "A man wants air conditioning while we watches the game on Saturday, where will it likely be installed?", "question_concept": "air conditioning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["car", "house", "offices", "park", "movie theatre"]}, "answerKey": "B"}
{"id": "ae038e9af9d5a511ada7456b5e73b15e", "question": "What could be playing a balailaika?", "question_concept": "balalaika", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["movie dr", "orchestra", "music store", "cat", "symphony"]}, "answerKey": "B"}
{"id": "a400b9fd1e319f901471c4b42d401c52", "question": "Sailors drive many different types of boats, what type of boat involves their namesake.", "question_concept": "sailor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coming home", "row boat", "board ship", "inflatable raft", "sail boat"]}, "answerKey": "E"}
{"id": "9dffd2021771e0ecddb19031acf3701b", "question": "Where could a person avoid the rain?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus stop", "tunnel", "synagogue", "fairy tale", "street corner"]}, "answerKey": "C"}
{"id": "3730c646fdf54472ab873aac9ff7852e", "question": "Why would a person like to have a large house?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have choice", "mentally challenged", "own house", "obesity", "lots of space"]}, "answerKey": "E"}
{"id": "175e7dcdded13d5adafaebf2264c3abd", "question": "Where will a cheap book be found?", "question_concept": "book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bookstore", "classroom", "discount store", "school room", "bedside table"]}, "answerKey": "C"}
{"id": "11d7db1d8e1cff2f40d4184f15cf7ae7", "question": "John and James are idiots. They bought two tickets to the Falcons vs the Jets even though neither wanted to see the what?", "question_concept": "idiots", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["internet cafe", "sporting event", "pressing wrong buttons", "obesity", "hockey game"]}, "answerKey": "B"}
{"id": "08db69edf0ec5848c1a53dca8fc1601a", "question": "James noticed that his penis was bigger. .  How might he act toward his plastic surgeon?", "question_concept": "bigger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["accidental", "detestable", "effusive", "enabled", "apathetic"]}, "answerKey": "C"}
{"id": "855ab6ba47f6311104c4d29e24ef0234", "question": "Who do professors work with?", "question_concept": "professors", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["methods of facts", "teach courses", "wear wrinkled tweed jackets", "school students", "state facts"]}, "answerKey": "D"}
{"id": "7ec11eeca4221795c117943ca2639e86", "question": "Colorful anemone look somewhat like what object you find on window sills?", "question_concept": "anemone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["intertidal zone", "coral sea", "under water", "flower bed", "florida keys"]}, "answerKey": "D"}
{"id": "e9389b08fdd17f14b148d498d6ff4dfe", "question": "From where do aliens arrive?", "question_concept": "aliens", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["outer space", "weekly world news", "roswell", "universe", "mars"]}, "answerKey": "A"}
{"id": "afa2899cc21e204fa64e63e7839e8c1e", "question": "The hikers stopped to have a drink, simply put they what?", "question_concept": "drink", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["had a party", "were thirsty", "refreshment", "getting drunk", "celebrating"]}, "answerKey": "B"}
{"id": "f898eb5b789d2dc6804edba269f051f0", "question": "When you get up in the morning before you begin work you should do what?", "question_concept": "begin work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apply for job", "sleep", "concentrate", "shower", "just do"]}, "answerKey": "D"}
{"id": "7ed7379fc51fd35a47be022f6c56ce51", "question": "The kitten had nothing to dig it's claws into, so when it tried to stop it slid across what?", "question_concept": "kitten", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living room", "floor", "warm place", "carpet", "farmhouse"]}, "answerKey": "B"}
{"id": "15798a23ee6952fedd6d202064069126", "question": "If a person is trying to keep something in their hand what should they do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["complete collection", "own house", "procrastinate", "explode", "have to hold"]}, "answerKey": "E"}
{"id": "273d0134e8ce53d4ebcf41ca7fde02af", "question": "Where could you find hundreds of thousands of home?", "question_concept": "home", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["field", "neighborhood", "star can", "city or town", "apartment building"]}, "answerKey": "D"}
{"id": "2f0931adc3d0d422d9ab6264395e89d8", "question": "Playing baseball is a lot like any other sport, there is always a risk of what?", "question_concept": "playing baseball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sore muscles", "errors", "happiness", "injury", "fun"]}, "answerKey": "D"}
{"id": "d00d3ba777cb3889a45799d72fca0a50", "question": "If I want to watch a movie without leaving my home what might I use?", "question_concept": "movie", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drive in movie", "drive in movie", "television", "video store", "show"]}, "answerKey": "C"}
{"id": "b1f36d1c8ab7e5a28783cb38e8709c27", "question": "The victim was to take stand today, they were going to do what?", "question_concept": "take stand", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["testify", "runaway", "witness", "tell truth", "go home"]}, "answerKey": "A"}
{"id": "a5e76dd088aab4f89e2fe93f6de6e46d", "question": "What does a successful dog grooming session likely to make a owner feel?", "question_concept": "grooming", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cleanliness", "mistakes", "growth", "satisfaction", "late"]}, "answerKey": "D"}
{"id": "ac6f0e24dd6203cda43e1089dcf081d6", "question": "The runner was in third place, but he pushed harder and thought he might be able to reach second.  What was beginning to do?", "question_concept": "runner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["near finish line", "finish", "get tired", "gain ground", "trip over"]}, "answerKey": "D"}
{"id": "1ab746bcd100ccf513055fe93c61010b", "question": "The tourist entered Mammoth cave, what state were they in?", "question_concept": "cave", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["west virginia", "kentucky", "rocky hills", "scotland", "canyon"]}, "answerKey": "B"}
{"id": "af836abc58e0daf36df1d8d6830b70c5", "question": "What does someone typically feel when applying for a job?", "question_concept": "applying for job", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["horror", "anxiety and fear", "rejection", "increased workload", "being employed"]}, "answerKey": "B"}
{"id": "2ed66cfd206723a006b37599b516ad6e", "question": "He was on trial for obstructing justice, during which he made a questionable comment and was also found guilty of what?", "question_concept": "obstructing justice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["prosecution", "getting hurt", "sweat", "steam", "committing perjury"]}, "answerKey": "E"}
{"id": "e89a2762d578cb7bc2cc0a5b2a16d933", "question": "What kind of feelings does buying presents for others create?", "question_concept": "buy presents for others", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tears", "please", "like", "thank", "make happy"]}, "answerKey": "E"}
{"id": "43cec0fff43a976fade9112d02b66021", "question": "What green area is a marmot likely to be found in?", "question_concept": "marmot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["countryside", "great plains", "encyclopedia", "jungle", "north america"]}, "answerKey": "A"}
{"id": "30e66db11e0257a14a17108b90cd69fb", "question": "Jan tested the current, and noticed that it was high.  He thought that the wires might have too much what?", "question_concept": "current", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["later", "updated", "still", "resistance", "now"]}, "answerKey": "D"}
{"id": "f21ef67b31bd36a3174b6b4c7b4bbc7b", "question": "What does a kindergarten teacher do before nap time?", "question_concept": "teacher", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lower expectations", "encourage", "fear", "time test", "tell story"]}, "answerKey": "E"}
{"id": "e476e2c8c278eaecfe1a8b884b6aeb8e", "question": "Sam was a stranger.  Even so, Mark treated him like what?", "question_concept": "stranger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["friend", "family", "known person", "park", "outsider"]}, "answerKey": "B"}
{"id": "191e3c676f05a11d6b2565d8c27d2001", "question": "Bob's only light source was a small bulb.  There were four walls, if there was a door he couldn't see it.  What was Bob in?", "question_concept": "light source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["closed room", "sky", "dard", "his grave", "house"]}, "answerKey": "A"}
{"id": "99098375c7b651d524eebac72e358238", "question": "James thought of criminal justice like a computer program.  It need to work right.   What ideas might James not like?", "question_concept": "computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["manual", "process information", "power down", "control model", "reason exists"]}, "answerKey": "D"}
{"id": "290fac9f881a83d8bfb34355f8e71044", "question": "With the card slot lit up he knew how to get started finding his balance with what?", "question_concept": "card slot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slot machine", "ticket machine", "bank machine", "telephone", "automated teller"]}, "answerKey": "E"}
{"id": "6c36226b23377a0dd0188bf56840e22a", "question": "To play sports professionally you must do what very often?", "question_concept": "play sports", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wash your clothes", "get in shape", "practice", "take off uniform", "stretch"]}, "answerKey": "C"}
{"id": "aa5aa36557a5fbb93391506182f1025c", "question": "Some people prefer releasing energy through work while others prefer to release it through what?", "question_concept": "releasing energy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motion", "stretch", "exercise", "movement", "muscles"]}, "answerKey": "C"}
{"id": "a38df3e750b1edd30f905e17af803c61", "question": "What will a person going for a jog likely be wearing?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grope", "acknowledgment", "comfortable clothes", "ipod", "passionate kisses"]}, "answerKey": "C"}
{"id": "dba51270f789c75a2e38a5201b124d99", "question": "The child pretended he was reading newspaper, he couldn't actually do it without what?", "question_concept": "reading newspaper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["patience", "falling down", "literacy", "buying", "knowing how to read"]}, "answerKey": "E"}
{"id": "1be8ec824eb0c7218b6bc160fd191428", "question": "Jenny enjoyed helping people.  It brought her a great deal of what?", "question_concept": "helping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "complications", "train", "feel good about yourself", "enjoyment"]}, "answerKey": "A"}
{"id": "0e80f2afe5c4f652e8720b52d7c06c87", "question": "What might someone believe in if they are cleaning clothes?", "question_concept": "cleaning clothes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feminism", "sanitation", "ruined", "wrinkles", "buttons to fall off"]}, "answerKey": "B"}
{"id": "b67971747e95ba425a5b81e0ba8d0b28", "question": "Where would you find a basement that can be accessed with an elevator?", "question_concept": "basement", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat cake", "closet", "church", "office building", "own house"]}, "answerKey": "D"}
{"id": "fcd39cfa321728fea069a6ae4285b06f", "question": "In order to learn to program from another person you can do what?", "question_concept": "program", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn how to", "have a friend", "knowledge", "take class", "have computer"]}, "answerKey": "D"}
{"id": "cb6766fb25daee911fc8e9816b98938c", "question": "He was at the gym trying to build muscle, what is it called that he is trying to build muscle on?", "question_concept": "muscle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["body of animal", "arm", "bodybuilder", "body of dog", "human body"]}, "answerKey": "E"}
{"id": "54231f875bb7fe4d3e4afb6eae64387c", "question": "What part of plants is pretty?", "question_concept": "plants", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dirt", "no neurons in", "millions of cells", "flowers on", "roots"]}, "answerKey": "D"}
{"id": "7d7f7d7a8ae3b20ca9fc0da6efe467b4", "question": "The man was going fishing instead of work, what is he seeking?", "question_concept": "going fishing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["food", "relaxation", "killing", "missing morning cartoons", "boredom"]}, "answerKey": "B"}
{"id": "31b72d4e4ae7c672c20e27e42499ec79", "question": "What could you get an unsmooth pit from?", "question_concept": "pit", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["backyard", "rock", "mine", "cherry", "peach"]}, "answerKey": "E"}
{"id": "26ce83b8e9a263079aa8cdbd5258d667", "question": "The man tried to reply to the woman, but he had difficulty keeping track of conversations that he didn't do what to?", "question_concept": "reply", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["initiate", "ignore", "question", "answer", "ask"]}, "answerKey": "A"}
{"id": "30138608d4934a75cf0911a06b021374", "question": "I couldn't find anybody who recalled the event, what were they adroit at doing?", "question_concept": "anybody", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["question authority", "act fool", "wash dishes", "act innocent", "forget"]}, "answerKey": "E"}
{"id": "01abce8c4964371d85a5be2019f75827", "question": "Where would you find a large dining room containing a fancy chandelier?", "question_concept": "dining room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mansion", "every house", "own home", "table", "restaurant"]}, "answerKey": "A"}
{"id": "3e2222c99e11fca2ad4af2d470eb8ea2_1", "question": "The extremely large cargo plane could only land at a specialized runway, these were only located at a what?", "question_concept": "runway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["back yard", "bowling alley", "city", "military base", "fashion show"]}, "answerKey": "D"}
{"id": "847dbf5b73c3e8d49bb9a36491d95e79", "question": "The carpet was smelly and discouraged the league from playing there, where was this smelly carpet installed?", "question_concept": "carpet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bedroom", "movie theater", "bowling alley", "church", "office"]}, "answerKey": "C"}
{"id": "fa031cff8e11e75c68d6a99ef0e5ca3a", "question": "How can someone be let into a brownstone?", "question_concept": "brownstone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["brooklyn", "ring", "subdivision", "bricks", "new york city"]}, "answerKey": "B"}
{"id": "c592258c88295756833e9796e881057b", "question": "Where would someone purchase an upright piano?", "question_concept": "upright piano", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music class", "college", "music store", "music room", "music band"]}, "answerKey": "C"}
{"id": "e1403a7c581bc263aea2ed8d179826d1", "question": "Where would you keep an ottoman near your front door?", "question_concept": "ottoman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living room", "parlor", "furniture store", "basement", "kitchen"]}, "answerKey": "A"}
{"id": "15c38f66e811d6ed68cde931bc31d93c", "question": "Diving into backyard pools can be very dangerous and can lead to serious head and what?", "question_concept": "diving", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["going somewhere", "splats", "cancer", "getting wet", "spinal injuries"]}, "answerKey": "E"}
{"id": "1ac54dbf6b67f27daa3d456416047584", "question": "Where would one find a snake in a cage?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tropical forest", "oregon", "woods", "pet store", "louisiana"]}, "answerKey": "D"}
{"id": "21763a65765b5405c9a54484c2e54a72", "question": "Where are people likely to become impatient?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["end of line", "buildings", "apartment", "neighbor's house", "address"]}, "answerKey": "A"}
{"id": "c492b8b9754a181c924c1df19998cbc7", "question": "When you fail to finish something, you failed at doing what to it", "question_concept": "fail", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["winning", "passing", "completing", "do well", "succeeding"]}, "answerKey": "C"}
{"id": "fff554fffa1a0adc64b8d1e21d55534b", "question": "John didn't care about style.  He felt that form was less important than what?", "question_concept": "form", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shapeless", "quality", "function", "change shape", "chaos"]}, "answerKey": "C"}
{"id": "8ea5720718c0e122efa6277edb511569", "question": "When you get together with friends to watch film, you might do plenty of this?", "question_concept": "watch film", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["see what happens", "enjoy stories", "pass time", "have fun", "interesting"]}, "answerKey": "D"}
{"id": "23e4257a49972efd8a97672f060be1c1", "question": "A supermarket is uncommon in what type of collection of shops?", "question_concept": "supermarket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["strip mall", "city or town", "shoppingcentre", "boutique", "vermont"]}, "answerKey": "A"}
{"id": "a018d65a74b9e77d81014fd8f6d78f77", "question": "Bill puts meat on the scale, where does Bill work?", "question_concept": "scale", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music store", "assay office", "tidal wave", "butcher shop", "bathroom"]}, "answerKey": "D"}
{"id": "24ceaf5c10863e73919b5f1b0f2db38e", "question": "I'm having some food at my party, what will I need to serve it?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["zoo", "pan", "bowl", "kitchen", "spoon"]}, "answerKey": "E"}
{"id": "900492bd731f8f615ed7c08155737d44", "question": "Before racers start to run they must do what at the starting line?", "question_concept": "run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn to walk", "walking", "walk slowly", "breathe", "stand still"]}, "answerKey": "E"}
{"id": "4e3f85dc92eaad4ae6bc6529d62e382c", "question": "What does an actor do when they are bored of their roles?", "question_concept": "actor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mask", "branch out", "wear costume", "pretend", "sing songs"]}, "answerKey": "B"}
{"id": "fa1f17ca535c7e875f4f58510dc2f430", "question": "What is a person called who doesn't have immortality?", "question_concept": "immortality", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mortal", "dying", "death", "dead", "mortal"]}, "answerKey": "E"}
{"id": "76b6f0765a3b2fba71021f902142edc0", "question": "Why would you be watching tv instead of doing something else?", "question_concept": "watching tv", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["headache", "laughter", "laziness", "erections", "wasting time"]}, "answerKey": "C"}
{"id": "f1368ab1d4ee05d72d555474fcd737d7", "question": "If chewing food is difficult for you, what is a possible reason?", "question_concept": "chewing food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["broken jaw", "sore mouth", "eating", "good digestion", "avoiding choking"]}, "answerKey": "B"}
{"id": "3dee8fc7f0a3fbf4de111b6686fca157", "question": "He had to wear a tuxedo while playing the keyboard instrument, so did the other hundred members of the what?", "question_concept": "keyboard instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music store", "band", "medium", "orchestra", "piano store"]}, "answerKey": "D"}
{"id": "ea0e7771afd86a59fd9f7764b77e3fa4", "question": "Where do you find the most amount of leafs?", "question_concept": "leaf", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["floral arrangement", "ground", "forrest", "field", "compost pile"]}, "answerKey": "C"}
{"id": "2c845646032bbf27fb3904330d59d324", "question": "Where can children play with animals?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meadow", "play room", "surface of earth", "zoos", "fairgrounds"]}, "answerKey": "E"}
{"id": "bc08c354e5bead6863ea4a29cb8fa359", "question": "What kind of tale might feature a talking weasel?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mulberry bush", "animated film", "chicken coop", "history book", "children's story"]}, "answerKey": "E"}
{"id": "fb35c7aa5694bab2cde4b7257bfae003", "question": "What kind of status is the bald eagle given?", "question_concept": "bald eagle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["outside", "world", "protection", "colorado", "america"]}, "answerKey": "C"}
{"id": "e2a9f0041d17a9944377a91bef5e0d0d", "question": "Why do most people take a quick rest during the day?", "question_concept": "rest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["need to", "hungry", "feel more energetic", "weak", "regenerate"]}, "answerKey": "C"}
{"id": "ae56eff01d05422ddbcb26be7181356a", "question": "What could suddenly stop someone when he or she is running?", "question_concept": "running", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mushroom", "falling down", "sweating", "exhaustion", "getting tired"]}, "answerKey": "B"}
{"id": "895aa97bb84d874d71b2aed572cebfdd", "question": "Where would you find a monkey in the wild?", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["zoo", "barrel", "research laboratory", "captivity", "thailand"]}, "answerKey": "E"}
{"id": "9d625e948e9c3777e7cc54ed8ffea135", "question": "Where could a sloth live?", "question_concept": "sloth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tropical jungle", "manual", "work", "transit", "countryside"]}, "answerKey": "A"}
{"id": "d107d67d525a686fbd8282314d2ea33c", "question": "A gentleman is carrying equipment for golf, what is he likely to have?", "question_concept": "gentleman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["club", "assembly hall", "meditation center", "meeting", "church"]}, "answerKey": "A"}
{"id": "fee5ff19811750ad019665af7b36b3c4", "question": "If you have a home with a courtyard, what's one thing you probably don't have to care for any longer?", "question_concept": "courtyard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lawn", "kids", "asshole", "spain", "office complex"]}, "answerKey": "A"}
{"id": "e69da59cbcf2a302e4523571eba8186b", "question": "The computer was difficult for he to understand at the store, so what did she sign up for to learn more?", "question_concept": "computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "facebook", "school", "apartment", "demonstration"]}, "answerKey": "E"}
{"id": "2dd138a63b5895cf737ced793cc668e7", "question": "If you take the risk buying a used car, you still hope it can what?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go fast", "start running", "going too fast", "look good", "last several years"]}, "answerKey": "E"}
{"id": "b33047f46db680a9b630c13e8ca115cc", "question": "Dan was ditting quietly on the couch with a book in his hand.  Laurie thought that he was just focused on what he was doing, but he actually did what?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat", "think", "reading", "meditate", "fall asleep"]}, "answerKey": "E"}
{"id": "f20d40bc4af588223e880e0bb58b27b8", "question": "What is the primary purpose of cars?", "question_concept": "cars", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cost money", "slow down", "move people", "turn right", "get girls"]}, "answerKey": "C"}
{"id": "b6b66d4519a84b8331ea55f84767e9df", "question": "Alabama is full of different people, but they are all citizens of what?", "question_concept": "alabama", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["united states", "deep south", "floribama", "gulf states", "florabama"]}, "answerKey": "A"}
{"id": "952cf4b2f7a434b2eeae9f4c7ed89c0a", "question": "They were hoping their campaign would create a rise in awareness of the problem and hopefully do what to its effect?", "question_concept": "rise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["set", "fall", "park", "descend", "reduce"]}, "answerKey": "E"}
{"id": "b63e5cd88bfe75d29ff9fdc6dd97fed6", "question": "What do airplanes do as they are arriving at the gate?", "question_concept": "airplanes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slow down", "crash", "speed up", "land", "carry people"]}, "answerKey": "A"}
{"id": "ec5a336080e37fbe95d72ad5f9c65ba7", "question": "If a person with mental illness stops treatment what will likely happen?", "question_concept": "mental illness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["managed", "dancing", "recur", "effectively treated", "cause suffering"]}, "answerKey": "C"}
{"id": "6386bcf080633bc3eeb3317a5435b7b7", "question": "The gimmicky low brow TV show was about animals when they what?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sick", "mammals", "males", "bite", "attack"]}, "answerKey": "E"}
{"id": "43ab0ff711e60d51f943bbd2cdd6515a", "question": "A loud machine is irritating, but many are expected where?", "question_concept": "machine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["museum", "house", "laboratory", "library", "industrial area"]}, "answerKey": "E"}
{"id": "11c4c78d61e8212f0984fd07eb22b669", "question": "What part of a table would you put a ruler in?", "question_concept": "ruler", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "desk", "the backside", "office", "measure distance"]}, "answerKey": "A"}
{"id": "e61891746aa94ab57aaa754614034aef", "question": "What happens if someone kisses too long?", "question_concept": "kissing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["strong feelings", "herpes", "shortness of breath", "excitement", "arousal"]}, "answerKey": "C"}
{"id": "97da9aa4ea4b22744ec51cba49f35bfc", "question": "If I have a modern light source in my living room, what is it likely to be?", "question_concept": "light source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sky", "house", "lamp", "match", "candle"]}, "answerKey": "C"}
{"id": "46241bc83e8d81196ae5783b2b9854a4", "question": "The person saw the mess his children made, what was his following reaction?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["smell smoke", "cross street", "cry", "bank savings", "look angry"]}, "answerKey": "E"}
{"id": "18844d3aa4e52b331b5382c8244cf4db", "question": "Who might wear dark glasses indoors?", "question_concept": "dark glasses", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["blind person", "glove box", "movie studio", "ray charles", "glove compartment"]}, "answerKey": "A"}
{"id": "056b33c7050c167b0d4348d40d169358", "question": "Where would stones not be arranged in a path?", "question_concept": "stones", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["quarries", "field", "park", "bridge", "made from rocks"]}, "answerKey": "B"}
{"id": "31d7dd1d00aabe411568df3e72d5b5e0", "question": "A bald eagle is likely to be found on what kind of work?", "question_concept": "bald eagle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rural area", "book", "canada", "painting", "aviary"]}, "answerKey": "D"}
{"id": "cbf3dd48b4d591fc872a53cd4b9dd3af", "question": "The hostess was good at her job, she always had a smile when she would what?", "question_concept": "hostess", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["group people", "ready parlor for guests", "welcome guests", "work room", "park"]}, "answerKey": "C"}
{"id": "60e8f1a86d4063895f340cd1e3c55f50", "question": "What is likely to happen to someone who is learning?", "question_concept": "learning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["overconfidence", "effectiveness", "knowing more", "head grows larger", "growth"]}, "answerKey": "C"}
{"id": "eee8cb7a0d806a62d2de24831f82e3e1", "question": "The inspector was agreeing with the factory protocols, what was the status of the factory?", "question_concept": "agreeing with", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["compliance", "eligible", "contract", "harmony", "friendship"]}, "answerKey": "A"}
{"id": "9a23a7f04e63bf9f4c7dfe50c58abfd2", "question": "After standing up I had to sit right back down, why would I feel like this?", "question_concept": "standing up", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["train", "effort", "balance", "feet", "muscles"]}, "answerKey": "C"}
{"id": "e3426e4f60c142aa3d813479f79d6305", "question": "Where do you go on a night out before going to the bar?", "question_concept": "bar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new york city", "las vegas", "restaurant", "nightclub", "park"]}, "answerKey": "C"}
{"id": "3526550b02d9594abd4fc43553010fc6", "question": "The dad wanted to protect his house, where did he put his gun?", "question_concept": "gun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["police station", "crime scene", "restroom", "drawer", "holster"]}, "answerKey": "D"}
{"id": "e567c94d88829fb07a30e3d46c02e664", "question": "What instrument can be played with an air of happiness?", "question_concept": "happiness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jump up and down", "jump up and down", "sing", "play games", "fiddle"]}, "answerKey": "E"}
{"id": "cf5a710c931779fb3dde198e0ace3b6a", "question": "What to kids do for boredom on a ramp?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["watch film", "fire game", "hang out at bar", "go skiing", "skateboard"]}, "answerKey": "E"}
{"id": "0f2377604e628c55ba588366139396b9", "question": "What animal has quills all over it?", "question_concept": "quill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feather", "chicken", "calligraphy", "porcupine", "hedgehog"]}, "answerKey": "E"}
{"id": "ada088b7c97de80336ad043757c2db16", "question": "Why would you go to an office?", "question_concept": "office", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "school building", "paper", "city", "habit"]}, "answerKey": "A"}
{"id": "beef0aa2058297904bb4acc1dc340c85", "question": "When is the worst time for having food?", "question_concept": "having food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["digesting", "not hungry", "gas", "weight gain", "feeling of fullness"]}, "answerKey": "B"}
{"id": "ba9a05bd2086c0d37733e26479d6630f", "question": "If you spend all your time buying and not saving what is is likely to happen?", "question_concept": "buying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["using money", "feel better", "ocean", "losing money", "go broke"]}, "answerKey": "E"}
{"id": "6b0bf501aa68b06ddc5ad72ac5ff68fc", "question": "Though a mouse might prefer your house, you might also see him where?", "question_concept": "mouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tin", "department store", "garden", "small hole", "cupboard"]}, "answerKey": "C"}
{"id": "926298bbdd03ce96acfeb4408b888b61", "question": "What is performing a type of?", "question_concept": "performing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["singing", "act", "feat", "smile", "acting"]}, "answerKey": "B"}
{"id": "faa0aa438b94c19be8ff52ee80d9e298", "question": "The car was going from Alabama to New York, what was its goal?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["head north", "speed up", "heading north", "go fast", "headed south"]}, "answerKey": "A"}
{"id": "9310c39a0752f28640c3a05cba1d5ca7", "question": "What do they call the trash in Australia?", "question_concept": "trash", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dirt", "subway", "state park", "container", "dustbin"]}, "answerKey": "E"}
{"id": "fee5f4e9d8e37f0183e36eb9b8dbcbb9", "question": "Joan wants to cook a potato, where should she place it?", "question_concept": "potato", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boiling water", "paper bag", "restaurants", "underground", "cupboard"]}, "answerKey": "A"}
{"id": "5392af3f1c4665e95ff3354e5115de42", "question": "Writers with a great what can amass a large fortune?", "question_concept": "fortune", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cookie", "bank", "real estate", "imagination", "bank roll"]}, "answerKey": "D"}
{"id": "4c5c74b3287492d6ddb2da4c8c0fd51a", "question": "Where do all animals live?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["the moon", "fairgrounds", "surface of earth", "meadow", "zoos"]}, "answerKey": "C"}
{"id": "52f3eb6c9a6b9671050fc769d465ed03", "question": "How are the conditions for someone who is living in a homeless shelter?", "question_concept": "living", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sometimes bad", "happy", "respiration", "growing older", "death"]}, "answerKey": "A"}
{"id": "03ee30b5801b61aee791a551a9d9a49f", "question": "You can do knitting to get the feeling of what?", "question_concept": "knitting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relaxation", "arthritis", "adrenaline", "your", "sweater may produced"]}, "answerKey": "A"}
{"id": "6d1d483745bc0aae0f4dd04e851ceffb", "question": "What might a very large table be?", "question_concept": "table", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dining room", "conference", "kitchen", "in a lake", "demonstration"]}, "answerKey": "B"}
{"id": "bf10bfda7328c8671e15adf8546b64d7", "question": "John got his tax refund back.  He treated it like it was what?", "question_concept": "tax", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["candy", "death and", "free money", "discount", "credit"]}, "answerKey": "C"}
{"id": "0b3a3ee40dd25be9735ac5e3342ca4dd", "question": "A person with an allergy might be doing what if they awake suddenly?", "question_concept": "awake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have fun", "enjoy with friends", "stretch", "yawn", "sneezing"]}, "answerKey": "E"}
{"id": "77e2a0b469b56bea81921a4a945ffcb5", "question": "Where is a ferret unlikely to be?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "outdoors", "aquarium", "north carolina", "great britain"]}, "answerKey": "A"}
{"id": "dc964e4f6df6b70815e81e466d0ff717", "question": "If you jump in any of the oceans you will get?", "question_concept": "oceans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tanned", "wet", "wide", "very deep", "fish"]}, "answerKey": "B"}
{"id": "6b9221c1af583ffb43580857d6fde38a", "question": "Immediately after peeing, a person's bladder is what?", "question_concept": "bladder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["collapsed", "empty", "full", "filled", "stretchable"]}, "answerKey": "B"}
{"id": "4dc2c4596b08e9bfd893174e67bff40a", "question": "The lady would eat and eat, and because of mental issues would then make herself what?", "question_concept": "eat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wash dishes", "throwing up", "drinking", "throw up", "turn inside out"]}, "answerKey": "D"}
{"id": "8ae24d3ff199077a59e0d970feb665b7", "question": "A car was hailed to chauffeur someone to the opera house, where was it heading?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go downtown", "appear suddenly", "go fast", "bottom out", "east"]}, "answerKey": "A"}
{"id": "d64a676e9d22e7edd12e7f4ce267a9f0", "question": "What do you go to see for live entertainment?", "question_concept": "entertainment", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["movie", "show", "concert venue", "casino", "theatre"]}, "answerKey": "B"}
{"id": "54ecb521df1d0f5b130a393c42b4126d", "question": "The teacher thought that a ferret can be very mischievous and probably wouldn't make a great pet for the entire what?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bad mood", "hutch", "classroom", "pair of trousers", "year"]}, "answerKey": "C"}
{"id": "b7276bb9139ec25c98c7e3822404eb6c", "question": "A creek is a body of water found in what low land?", "question_concept": "creek", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forest", "valley", "outdoors", "countryside", "woods"]}, "answerKey": "B"}
{"id": "ecb8758b0d088f9aedc182a516dd1190", "question": "If I have a pet bird, what does it likely live in?", "question_concept": "bird", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forest", "bathroom", "windowsill", "countryside", "cage"]}, "answerKey": "E"}
{"id": "f2645d0ee8662b6553954cee7e77979e", "question": "Joe and Mac were playing basketball. They did it every day in their back yard.  Why were they playing basketball?", "question_concept": "playing basketball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["study", "have fun", "pain", "cheers", "knee injury"]}, "answerKey": "B"}
{"id": "ea6d1a739ea841be282e13789270651e", "question": "What makes someone a nomad?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unpleasant things", "hangnail", "have no home", "have no car", "schizophrenia"]}, "answerKey": "C"}
{"id": "c82ed0c2a2e115452b4d596c5faafbcf", "question": "What is a treat that you dog will enjoy?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["salad", "petted", "affection", "bone", "lots of attention"]}, "answerKey": "D"}
{"id": "163d83851ecd4a4144b31b8738e4c335", "question": "Women used to be expected to wear a dress but it's now acceptable for them to wear what?", "question_concept": "dress", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["man suit", "pants", "naked", "action", "long skirt"]}, "answerKey": "B"}
{"id": "095767956c500ca1af7cf7671556de5b", "question": "The fact that Joe was able to memorize the list in spite of his apparent  state proved that part of his brain was what?", "question_concept": "memorize", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["awake", "repeat", "sleeping", "concentrate", "read aloud"]}, "answerKey": "A"}
{"id": "d31ee38f67d1173275e120b8ad36039c", "question": "What is a wet person likely to do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gain weight", "thank god", "catch cold", "suicide", "cross street"]}, "answerKey": "C"}
{"id": "c410a4626dfce4b4cfd3e5937602cd77", "question": "After recovering from the disease, what did the doctor call the patient?", "question_concept": "disease", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["healthy", "passing around", "cure", "wellness", "healthy"]}, "answerKey": "A"}
{"id": "14d760e43728e9e4643c414627f2b596", "question": "The painter started to edge the room with tape, he always took extra care to make the lines clean and crisp when working with an what?", "question_concept": "edge", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["triangle", "middle", "corner", "center", "interior"]}, "answerKey": "E"}
{"id": "abcf1b550b4d44f46d4f68b8e1d98ec8", "question": "After high tide, where on the coast can you look to find a sea anemone?", "question_concept": "anemone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nursery", "museum", "gulf of mexico", "tide pool", "intertidal zone"]}, "answerKey": "D"}
{"id": "5b8af6f26335dbd501b0104c71e26d9e", "question": "What could a driving car do to a pedestrian?", "question_concept": "driving car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["say hello", "wreak", "pollution", "smoke", "relaxation"]}, "answerKey": "B"}
{"id": "4364b4b342fb7b44434bd6694bf8fd51", "question": "People do many things to alleviate boredom.  If you can't get out of the house you might decide to do what?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play cards", "skateboard", "meet interesting people", "listen to music", "go to a concert"]}, "answerKey": "D"}
{"id": "3ffe67fb009529d9b0c49ccd7141ee4a", "question": "At a grocery store they sell individual potatoes, where does the grocery clerk likely put the potato?", "question_concept": "potato", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boiling water", "root cellar", "rocket ship", "paper bag", "underground"]}, "answerKey": "D"}
{"id": "f372587fa4c99d5bebf0d0eb987c44e2", "question": "What room is a rubber bath mat usually kept?", "question_concept": "mat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["doorway", "living room", "sand", "floors", "bathroom"]}, "answerKey": "E"}
{"id": "d35a8a3bd560fdd651ecf314878ed30f", "question": "What would you put meat on top of to cook it?", "question_concept": "meat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["oil", "freezer", "ham sandwich", "oven", "frying pan"]}, "answerKey": "E"}
{"id": "0542414710025f56b0c26e1bae5c4d06", "question": "Minerals can be obtained in what way for a person who avoids leafy greens?", "question_concept": "mineral", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["multivitamin", "farm", "michigan", "earth", "ore"]}, "answerKey": "A"}
{"id": "1875f70cf736c68c7a9df3ef870224a1", "question": "What could you be a few hours after you finish cashing in due to your cash?", "question_concept": "cashing in", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["happy", "receiving money", "getting paid", "spending money", "selling out"]}, "answerKey": "A"}
{"id": "83250ae2dfeb2e3886ead4cde8e1290f", "question": "The smelly man was having a bath, but what is he pursuing?", "question_concept": "having bath", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hydration", "being clear", "personal cleanliness", "will drown", "use of water"]}, "answerKey": "C"}
{"id": "70c39372c0d50566554fd72c768b75f6", "question": "What might a couple have a lot of when they are deciding on stopping being married to each other?", "question_concept": "stopping being married to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pleasure", "detachment", "exercise", "bankruptcy", "fights"]}, "answerKey": "E"}
{"id": "c21ec5b367f409a0288d616f626555ae", "question": "If a person is working a lot, what are they likely trying to earn?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["much money", "own house", "creativity", "new car", "caregiver"]}, "answerKey": "A"}
{"id": "a2cd03ed068f6d613e85f3a60f4db0a1", "question": "The traveling business man was glad his credit card had perks, it offset the high prices for travel from a what?", "question_concept": "high prices", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["car", "theatre", "airport", "hotel", "disneyland"]}, "answerKey": "C"}
{"id": "d2871dc28c82471e5d7f71f79e49c257", "question": "Billy hated using other people's toilets. He was only comfortable on his own.  So whenever he needed to poop, he would go back to his what?", "question_concept": "toilet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bathroom", "motel room", "nearest public restroom", "house", "apartment"]}, "answerKey": "D"}
{"id": "94770e75c4e2000e717b4218ddff19e8", "question": "The forest experienced a cold winter, where is it located?", "question_concept": "forest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["earth", "south america", "amazon basin", "temperate zone", "national park"]}, "answerKey": "D"}
{"id": "08ad17d3ca1838b8724d21cf5921ec52", "question": "How can you let someone know about your anger without hurting him or her?", "question_concept": "anger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["release energy", "destroy enemy", "punch", "write letter", "lose your temper"]}, "answerKey": "D"}
{"id": "21fb76bd8349628b441c76f47c33e77b", "question": "Where is one likely to find a brownstone?", "question_concept": "brownstone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new york city", "subdivision", "ring", "hazleton", "live in"]}, "answerKey": "A"}
{"id": "e151b44e0a7bf08a1dd3c861eef09161", "question": "What may I place the telephone on?", "question_concept": "telephone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bar", "friend's house", "desktop", "party", "office"]}, "answerKey": "C"}
{"id": "46351b3a6beb694c5f623583a3b1473d", "question": "What language type is someone from Iran likely to use?", "question_concept": "light source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["books", "dard", "sky", "closed room", "television"]}, "answerKey": "B"}
{"id": "db75e16788cf56d5dfb9773eaf91fe7e", "question": "John went to a party that lasted all night.  Because of this, he didn't have time for what?", "question_concept": "party", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meeting", "blowing off steam", "stay home", "partying hard", "studying"]}, "answerKey": "E"}
{"id": "ffd89796a9b09bef56c5803f188764c6", "question": "The child wasn't allowed in the kitchen but still wanted to help, what could it do to help in the dining room?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["set table", "form opinions", "make honey", "become adult", "gather flowers"]}, "answerKey": "A"}
{"id": "5622e49306bb82ec1cec817ad0506c60", "question": "He was having a hard time expressing himself in a healthy way, the psychologist said he was mentally what?", "question_concept": "expressing yourself", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slow", "understood", "suffering", "embarrassment", "fun"]}, "answerKey": "C"}
{"id": "6efaeb796307036719635242fa5ad0f3", "question": "When someone is physically competing what does their body do?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tension", "perform better", "releases heat", "winning or losing", "sweat"]}, "answerKey": "E"}
{"id": "114d310d1198abffaf8b88dab5a55aa7", "question": "How would you express information to a deaf person?", "question_concept": "express information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["summarize main points", "close mouth", "write down", "may disagree", "talk"]}, "answerKey": "C"}
{"id": "0f79faf5337706f2e0e39c15bbd2e99a", "question": "Printing on a printer can get expensive because it does what?", "question_concept": "printing on printer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["explode", "use paper", "store information", "queue", "noise"]}, "answerKey": "B"}
{"id": "b62d7d1b5eec31be0b65146a9fc069e0", "question": "What will god never do according to religion?", "question_concept": "god", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anything", "judge people", "work miracles", "judge men", "everywhere"]}, "answerKey": "B"}
{"id": "1342c6aec9f5179d6ea6fa5fefbe5188", "question": "One of the potential hazards of attending school is what?", "question_concept": "attending school", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cooties", "get smart", "boredom", "colds and flu", "taking tests"]}, "answerKey": "D"}
{"id": "c74ae684ba6c76e2a913493483678c9d", "question": "What has a surface with many sides?", "question_concept": "surface", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tetrahedron", "object", "geometry problem", "lake", "triangle"]}, "answerKey": "A"}
{"id": "411e50225637b76187cc36b24fe3127c", "question": "What could bring a container from one place to another?", "question_concept": "container", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["food", "refrigerator", "cargo ship", "port", "fuel"]}, "answerKey": "C"}
{"id": "2a0e82bbf1471290c93c8f2a11af197f", "question": "The old style pop ups literally let you see the story when you did what?", "question_concept": "see story", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["giggle", "visualize", "open book", "reading", "go to movies"]}, "answerKey": "C"}
{"id": "eaadd7a4b18cb48c00f85c3975750fe7", "question": "What is it called when you are talking to someone?", "question_concept": "talking to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["communication", "quiet", "boredom", "persuaded", "learn"]}, "answerKey": "A"}
{"id": "403c9b067ef7363efffa822bb08c5426", "question": "The family finished dinner, the child's chore was to load the dirty dishes where?", "question_concept": "dirty dishes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["restaurant kitchen", "dishwasher", "son's room", "cabinet", "party"]}, "answerKey": "B"}
{"id": "adf228312401c9ff421a4da1b46bb70a", "question": "Where could you find a bureau as well as many politicians?", "question_concept": "bureau", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["each city", "office building", "a zoo", "french government", "washington dc"]}, "answerKey": "E"}
{"id": "57c85e4c7ea2501ef9d8f304b524e2e4", "question": "Dad wanted to hide the check in his office, where did he put it?", "question_concept": "check", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cash register", "desk drawer", "fish tank", "bank", "pay envelope"]}, "answerKey": "B"}
{"id": "c22f30eee57f7191ee07e9a916460f68", "question": "For some reason she was devoid of regular emotions, buying products was the only way she could feel what?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pleasure", "owning", "debt", "spending money", "smart"]}, "answerKey": "A"}
{"id": "026cb9c07a583ec933f2c4c67ae73836", "question": "Where are horses judged on appearance?", "question_concept": "horses", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["race track", "fair", "raised by humans", "in a field", "countryside"]}, "answerKey": "B"}
{"id": "c57ed32566a2db1ec3d6e4fd595b9d05", "question": "Why do people read non fiction?", "question_concept": "read", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["having fun", "it's more relatable", "learn new things", "becoming absorbed", "falling asleep"]}, "answerKey": "C"}
{"id": "93b52e7ea1acf10db891e9355e234123", "question": "While knitting you can do what using a radio?", "question_concept": "knitting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listen to music", "watch television", "making blankets", "eat", "watching tv"]}, "answerKey": "A"}
{"id": "dbdad44029098d4b1d202d6d857d6092", "question": "Where are you likely to set papers while working on them?", "question_concept": "papers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "meeting", "drawer", "toilet", "garage"]}, "answerKey": "A"}
{"id": "69d0f70c173dda17934836d618ca7093", "question": "John had a massive debt to 50 million dollars.  Compared to that, Leo's 2000 dollar debt seemed what?", "question_concept": "massive", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dwarf", "inconsequential", "insubstantial", "lame", "tiny"]}, "answerKey": "C"}
{"id": "e5697a25935c5249d2108f55e245f3e4", "question": "The man flew his airplane over the city and saw pollution visibly in the sky, what was polluted?", "question_concept": "pollution", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forest", "street", "air", "caused by humans", "car show"]}, "answerKey": "C"}
{"id": "99af85081085e6228c6d78c95be01968", "question": "What is a very unlikely side effect of becoming inebriated?", "question_concept": "becoming inebriated", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fights", "drunkenness", "staggering", "puke", "paralysis"]}, "answerKey": "E"}
{"id": "235094c966bcbdc94701b41b969f9c75", "question": "when communicating with my boss what should i do", "question_concept": "communicating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["misunderstandings", "transfer of information", "learning", "confusion", "silence"]}, "answerKey": "B"}
{"id": "99789083502af9bf111876a00fae44ac", "question": "If not in a stream but in a market where will you find fish?", "question_concept": "fish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stream", "aquarium", "refrigerator", "boat ride", "market"]}, "answerKey": "C"}
{"id": "1d44fb5f4b7f1e23ff6c1c083db81ba1", "question": "What are people likely to want to do with their friends?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["own land", "own home", "talk to each other", "believe in god", "spend time"]}, "answerKey": "E"}
{"id": "194b66240f6fab75749c1e30ed09ea09", "question": "During a shark filled tornado where should you not be?", "question_concept": "shark", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["marine museum", "pool hall", "noodle house", "bad movie", "outside"]}, "answerKey": "E"}
{"id": "83dad4fe630fddbdcd5b18ef890c66f2", "question": "What is the likely result of buying products in excess?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["running out of money", "spending money", "poverty", "comparison shopping", "overstocking"]}, "answerKey": "E"}
{"id": "3ebc5ddd2e97fe37fcb52aa2a9e2e1a7", "question": "What is a person trying to accomplish when taking analgesics?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["acceptance", "avoid pain", "acknowledgment", "passing grade", "intellectual challenge"]}, "answerKey": "B"}
{"id": "9ed019338a48216de9eadf64faaf1ce0", "question": "Where would you put a glass after drinking from it?", "question_concept": "glass", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "water cooler", "cabinet", "dishwasher", "dining room"]}, "answerKey": "D"}
{"id": "d1d2585e0ba1160948b7c5822a99b7a1", "question": "Where would you buy food?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["freezer", "store", "home", "hatred", "kitchen"]}, "answerKey": "B"}
{"id": "e34a0d1331c6bd4574ffe308e3fbd389", "question": "When a person admits his mistakes, what are they doing?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["accident", "learn to swim", "thank god", "feel relieved", "act responsibly"]}, "answerKey": "E"}
{"id": "4858669d0193e5d9384dc37d4bb5c00c", "question": "Where do play a game for money?", "question_concept": "game", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["casino", "football ground", "ballpark", "family room", "toy store"]}, "answerKey": "A"}
{"id": "8fd82cdc253835814153fe7222e9967c", "question": "When you travel you should what in case of unexpected costs?", "question_concept": "travel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go somewhere", "energy", "spend frivilously", "fly in airplane", "have money"]}, "answerKey": "E"}
{"id": "66458bf8599c3ef1e7b50fa527531882", "question": "Donald is a prominent figure for the federal government, so in what city does he likely spend a lot of time?", "question_concept": "government", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["everything", "capitol building", "tourist sites", "canada", "washington d.c"]}, "answerKey": "E"}
{"id": "879239b8a788f3c9e3dfdd0862f3d7c5", "question": "There was more than one bum asking for change or a ticket, it was the cheapest way to travel so it was no surprise sight at the what?", "question_concept": "bum", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["train station", "beach", "bus depot", "bridge", "stumblebum"]}, "answerKey": "C"}
{"id": "8a69e6df5e8ad6c9e6828aa66c59d046", "question": "John and Joe like planning games but Joe  was hit by a ball and fell down. What might have happened to Joe.", "question_concept": "playing game", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anger", "good natured ribbing.", "enjoying", "injury", "enjoyment"]}, "answerKey": "D"}
{"id": "8d275acea05fd16295c659c504576a9b", "question": "Where can you buy jeans at one of may indoor merchants?", "question_concept": "jeans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gap", "shopping mall", "bedroom", "laundromat", "bathroom"]}, "answerKey": "B"}
{"id": "91629c6f9e4af3e6acf385eb23fd8068", "question": "What do you write letter in in America?", "question_concept": "letter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["syllable", "post office", "envelope", "english alphabet", "word"]}, "answerKey": "D"}
{"id": "59eb56f366407ac7db72996be265883b", "question": "Joe owned back taxes as well as what other type of taxes?", "question_concept": "back", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anterior", "front", "main", "front", "current"]}, "answerKey": "E"}
{"id": "4ab069f2e979d51f2c5929f590d09982", "question": "Where is a broadcast studio likely to be heard?", "question_concept": "broadcast studio", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["microphone", "arena", "radio station", "trees", "town"]}, "answerKey": "C"}
{"id": "d6bb990e8c409d2b3af37a2da198e01f", "question": "Kramer wrote a self-referential book.  What might that book be about?", "question_concept": "book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["counter", "coffee table", "school room", "backpack", "bedside table"]}, "answerKey": "B"}
{"id": "c5ad166ab5c5f5f067aa02b20f482523", "question": "Of all the sports, Billy enjoys football, but what does his concerned mother think of the sport?", "question_concept": "sports", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["very entertaining", "fun", "slow", "competitive", "violent"]}, "answerKey": "E"}
{"id": "ceafca2445b1b974d085a8cce38e8e44", "question": "What city will likely have many parking structures?", "question_concept": "parking structure", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chicago", "big city", "large city building", "environment", "college campus"]}, "answerKey": "A"}
{"id": "2ef2ae21a2d3a9ecbd5c45ff378d10e3", "question": "Sally was afraid of danger and always double checked what?", "question_concept": "danger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fight enemy", "secure", "being safe", "safety", "vicinity"]}, "answerKey": "D"}
{"id": "793672da43fbc609e8c5760630c7e239", "question": "What is the habitat of the fox?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hen house", "burrow", "california", "england", "mountains"]}, "answerKey": "E"}
{"id": "558cb0bc25387ce38d71f64ef6f1fa57", "question": "People are very much like the animals, but one thing has secured or dominance over the planet.  We're better at doing what?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat eggs", "make tools", "eat dosa", "talk to each other", "smoke pot"]}, "answerKey": "B"}
{"id": "2c9f4a98ce774cd734b6e384d95051a7", "question": "They children loved having a back yard, and the parents loved that it was a safe what?", "question_concept": "back yard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["suburb", "neighborhood", "back of house", "roundabout", "property"]}, "answerKey": "B"}
{"id": "33c84708785f88c19737ef5b0e31a64b", "question": "While people just throw coins down them now, what originally had a pail to be lowered for it's intended use?", "question_concept": "pail", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garage", "utility room", "slide", "wishing well", "garden"]}, "answerKey": "D"}
{"id": "d867f76d000bdb59b9b4cb982bd7f0a0", "question": "Joe was thrown from his boat into the water.  The water was cold because it was the middle of winter and he cried out to his crew for help.  They couldn't hear him over the sound of the what?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["surface of earth", "teardrops", "snowflake", "typhoon", "motor"]}, "answerKey": "D"}
{"id": "8c607d2e2e897d74048fcc794137b683", "question": "When a human is earning money, where are they often found?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["deep thought", "park", "friend's house", "place of work", "school"]}, "answerKey": "D"}
{"id": "5215e26c99b2a9b376fb1c70096a388a", "question": "They passed a apple tree on their way to the racetrack, the were going to watch the biggest motorsport spectacle in the world where?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["maryland", "indiana", "on tv", "park", "new jersey"]}, "answerKey": "B"}
{"id": "668dc6bce771b10cbf6336f3ec76520a", "question": "Why do people play chess on the weekends?", "question_concept": "play chess", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "have fun", "thrilling", "made", "smart"]}, "answerKey": "B"}
{"id": "a339fe08f1f50463ee180b797e99ebcc", "question": "What do you need energy to do in gym class?", "question_concept": "energy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "tacos", "mass", "play sports", "wrestle"]}, "answerKey": "D"}
{"id": "526cd34f5b2afefbbb7830434785f298", "question": "Sarah dropped the marble because she wanted to do what?", "question_concept": "marble", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["game", "pouch", "home", "store", "jar"]}, "answerKey": "A"}
{"id": "6c1c1c282cebe8917f607f0dbc1c102e", "question": "We are all human, and we all what?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["write", "eat cake", "smile", "think critically", "die"]}, "answerKey": "E"}
{"id": "b5baf77d3855935c87f01f5fb2216667", "question": "If a person were going to bed, what would be their goal?", "question_concept": "going to bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lazy", "insomnia", "rest", "falling asleep", "dreaming of"]}, "answerKey": "D"}
{"id": "83808e92381b2e5f4cdf55d1391645ae", "question": "What are candles good for eliminating?", "question_concept": "candle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelf", "board", "church", "table", "dark"]}, "answerKey": "E"}
{"id": "1a86310d7279097205a3403752c3b914", "question": "WHat leads to an early death?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["poisonous gas", "homicide", "cinder", "nuclear weapons", "cyanide"]}, "answerKey": "B"}
{"id": "b4130d1790948134f3aeab9d3d79c181", "question": "What room would you find many bookcases and is used for contemplation?", "question_concept": "bookcase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["study", "house", "homw", "kitchen", "den"]}, "answerKey": "A"}
{"id": "a5097b7f56d20217679f28201801476f", "question": "Where do you head to travel to a star?", "question_concept": "star", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["night sky", "galaxy", "outer space", "hollywood", "eat cake"]}, "answerKey": "C"}
{"id": "bcc5dd6292a64d8fa17cd07c360b335d", "question": "The player lifted his cornet and walked in rhythm, what was the player a member of?", "question_concept": "cornet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["museum", "high school band", "marching band", "orchestra", "band"]}, "answerKey": "C"}
{"id": "cfc7fccb8449a2a950c9d2a50991420e", "question": "What happens at soon as a living being is born?", "question_concept": "living", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["expiration", "growing older", "sometimes bad", "death", "start reproduction"]}, "answerKey": "B"}
{"id": "2e83c5989a018bec6d5f5ac7d3b72f49", "question": "When someone is talking and you missed something, what can you do to get them to repeat it?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["walking", "ask question", "think", "write question in crayon", "sneeze"]}, "answerKey": "B"}
{"id": "34b2d6aecdb5af8efacf0b0aa7e3989f", "question": "Where does one store fabric in their own home?", "question_concept": "fabric", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sewing room", "clothing store", "tailor shop", "clothes store", "cotton mill"]}, "answerKey": "A"}
{"id": "2ec7f8fe7948f9997e73f9bff7ba6e05", "question": "What do most companies not want to have relative to demand?", "question_concept": "want", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["oversupply", "plentitude", "stockpile", "superabundance", "busy"]}, "answerKey": "A"}
{"id": "651785ed4f7b0bd2e7ca9f70a42acea5", "question": "What is happening while he's playing basketball for such a long time?", "question_concept": "playing basketball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sweating", "pain", "having fun", "medium", "knee injury"]}, "answerKey": "A"}
{"id": "ee46995407eb6357bb5410d49d378629", "question": "A traveler laments the fact that mass transit is limited in his city when his groceries get soaked by the rain as he waits where?", "question_concept": "traveller", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus stop", "library", "motel", "airport", "subway"]}, "answerKey": "A"}
{"id": "303aedda3a5ab8d853cbe4edc4b914c6", "question": "The person was in physical distress, where should he go?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["synagogue", "for help", "hospital", "bus stop", "building"]}, "answerKey": "C"}
{"id": "720b98fbc365736597147c984f6bd301", "question": "The cancer patient was expecting to die, so he made out his what?", "question_concept": "die", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["not to live", "write will", "never want", "seek help", "go to hell"]}, "answerKey": "B"}
{"id": "c611875b43b67b91030b889b267bbcb3", "question": "There was a toll road that meandered from Maine to New Hampshire, where was it?", "question_concept": "toll road", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["massachusetts", "new england", "my house", "new jersey", "connecticut"]}, "answerKey": "B"}
{"id": "0547da29ffab9b441bae8870cd0f9dab", "question": "If you partied all night you could find yourself already what, even when just beginning work?", "question_concept": "beginning work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting tired", "working", "procrastination", "jumping", "sitting down"]}, "answerKey": "A"}
{"id": "21e312c7fd1a52341ce35b66457eab36", "question": "The cat carefully navigated the area, they do everything they can to avoid what?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get wet", "eat vegetables", "falling", "wool sweater", "sharp claws"]}, "answerKey": "A"}
{"id": "82e26bc22af89c38d54aa2d00dcb8a2b", "question": "What is someone usually doing if someone else is talking to him or her?", "question_concept": "talking to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listening", "language", "looking at eyes", "planning the perfect murder", "voice"]}, "answerKey": "A"}
{"id": "f75357e48c3026cfa4da3dba9f91bb21", "question": "What does the sky do before a rain?", "question_concept": "sky", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appear beautiful", "appear blue", "shows a rainbow", "rain water", "cloud over"]}, "answerKey": "E"}
{"id": "64931f9097155672bfe3e16f03b2c195", "question": "Pens, computers, text books and paper clips can all be found where?", "question_concept": "paper clips", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desktop", "university", "drawer", "table", "work"]}, "answerKey": "B"}
{"id": "5de3248caa2e5ed83dd0ec45a15eae18", "question": "What geographic area is a lizard likely to be?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ball stopped", "west texas", "arid regions", "garden", "warm place"]}, "answerKey": "B"}
{"id": "0611dfbf5114084723d75f59b4f67412", "question": "What do you use to carry your briefcase?", "question_concept": "briefcase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office building", "school", "courtroom", "airport", "hand"]}, "answerKey": "E"}
{"id": "5b8d76889510384b38b72945e8d28f53", "question": "He picked up his pace to a run, he wanted to do what?", "question_concept": "run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn to walk", "frightened", "get away from", "exercise", "go faster"]}, "answerKey": "E"}
{"id": "d81f5c49bc060dc799681bf4cacac73a", "question": "What would a person do if they do not have any friends?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talk to people", "try again", "fall asleep", "stand alone", "thank god"]}, "answerKey": "D"}
{"id": "aaf4fa38433c84b3bd0a86551259ce62", "question": "As a result of dying, what happens to organic material?", "question_concept": "dying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["change of color", "stop breathing", "wake up", "death and decay", "getting cold"]}, "answerKey": "D"}
{"id": "33ea932a876ac0361c9eefeff1d24e92", "question": "What does everyone have in relation to other people?", "question_concept": "everyone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feelings", "food", "unique personality", "different standards", "values"]}, "answerKey": "A"}
{"id": "aead08289ca9abfcd169f935ea228ee5", "question": "What do you ask a child to do when you first meet her?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ask questions", "count to ten", "costume", "state name", "dress herself"]}, "answerKey": "D"}
{"id": "adbddc80b10bf25f09c6c2bee4e3c59b", "question": "Where can you buy a clock, clothing and wrenches?", "question_concept": "clock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["counter", "train station", "school room", "desk", "department store"]}, "answerKey": "E"}
{"id": "1caf93d6a22dc8190e19c14bbe1fafda", "question": "What do you do when you're in a new place and want to see new things?", "question_concept": "see new", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["interesting", "look around", "take pictures", "change of surroundings", "new experience"]}, "answerKey": "B"}
{"id": "0bf4d64ad0eee7224acb3a4eb85accb2", "question": "What happens when to ice when it is in the sun?", "question_concept": "ice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["carved", "melted", "ice cream", "antarctica", "sculptured"]}, "answerKey": "B"}
{"id": "b93532cae23e505628dd88568da3337e", "question": "Where can you store your dishes in your dwelling?", "question_concept": "dishes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "shelf", "pantry", "apartment", "cabinet"]}, "answerKey": "B"}
{"id": "2d3c9d3dff1a7a8253180cb3de1ceeea", "question": "The man laid on the soft moss and looked up at the trees, where was the man?", "question_concept": "moss", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["niagra falls", "forest", "waterfall", "ground", "tree"]}, "answerKey": "B"}
{"id": "70701f5d1d62e58d5c74e2e303bb4065", "question": "What is someone doing if he or she is sitting quietly and his or her eyes are moving?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reading", "meditate", "fall asleep", "bunk", "think"]}, "answerKey": "A"}
{"id": "eacd87f297193033669a93160ae3776f", "question": "Where can I find a stapler in many places?", "question_concept": "stapler", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk drawer", "office building", "manual", "office supply store", "desktop"]}, "answerKey": "B"}
{"id": "8e1b0792e441a5d54ae47a4b24f48977", "question": "A man takes a seat at a museum outside of Barcelona, where is he likely?", "question_concept": "seat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["in cinema", "martorell", "falling down", "show", "airplane"]}, "answerKey": "B"}
{"id": "b4cde6a56fb19afc84876ebf2fb9e71a", "question": "Where would you find a toy soldier that is being played with?", "question_concept": "toy soldier", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toy box", "movies", "child's hand", "toybos", "child park"]}, "answerKey": "C"}
{"id": "095c5bc5fbaf12b384e9f7df47fdec16", "question": "Where are you when you're about to use your plane ticket?", "question_concept": "plane ticket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pocket", "terrorists hands", "airport", "sea ship", "briefcase"]}, "answerKey": "C"}
{"id": "494c501dbbfd36c602aae9e5b8e0cfff", "question": "Flowers make a good center focal point, just one of many arrangements that look good on a what?", "question_concept": "flowers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["market", "table", "countryside", "anthology", "vase"]}, "answerKey": "B"}
{"id": "5a7f6fd97b2c9ad05f773bc8b2ecf441", "question": "How can a human cross a river and not mess up their hair?", "question_concept": "river", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wisconsin", "waterfall", "hatred", "bridge", "valley"]}, "answerKey": "D"}
{"id": "5279a2ea333ba8a5bf3a7637a7279da1", "question": "Batman bought beer.  There were no bottles available.  He had to settle for what?.", "question_concept": "beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelf", "soccer game", "keg", "can", "refrigerator"]}, "answerKey": "D"}
{"id": "42c46e28baf0fc617a07419286178c0a", "question": "You can find a monkey in what West African region on the Gulf of Guinea", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["south american country", "rain forest", "pay debts", "works", "nigeria"]}, "answerKey": "E"}
{"id": "c76304b4962f94ab9f20f09cf4a1a7c1", "question": "Surprising an angry person could lead to what?", "question_concept": "surprising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["humor", "fight", "jocose", "laughter", "accidents"]}, "answerKey": "B"}
{"id": "8b23cd355ffc8b6e7aa5459ffb21b4e0", "question": "Where is a dining area likely to be small?", "question_concept": "dining area", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cruise ship", "home", "mall", "restaurant", "dark cave"]}, "answerKey": "B"}
{"id": "c35f7de9e9005fcf654cb0b23f17acd6", "question": "Killing people should not cause what emotion?", "question_concept": "killing people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vengeance", "going to prison", "joy", "afraid", "terrible"]}, "answerKey": "C"}
{"id": "d910859b9d1acae40456dbeaa8334bc0", "question": "James slamed into someone playing football, and not for the first time.  He was concerned about the consequences of many what?", "question_concept": "playing football", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exhilaration", "interactions", "head injuries", "death", "having fun"]}, "answerKey": "C"}
{"id": "6ca8439d062de4d43d7d471c508b78db", "question": "More people should lower the guard and just have fun, we don't got long just what?", "question_concept": "have fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enjoy living", "happy", "enjoyable", "get laid", "do enjoy"]}, "answerKey": "A"}
{"id": "ddd8c62ec94b4f94eeefdd05b9208a71", "question": "Where can you get a lizard to keep in your home?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desert country", "dessert", "pet shop", "tropical areas", "zoo"]}, "answerKey": "C"}
{"id": "72b638200414a526b598de0e01a044df", "question": "What would use a musical instrument?", "question_concept": "musical instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["guitar", "music room", "orchestra", "case", "movie"]}, "answerKey": "C"}
{"id": "c770870c88f35f9d110217049c5a7334", "question": "She was in an affair, what did that end upon discovery by her husband?", "question_concept": "affair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relationship", "marriage", "fidelity", "love", "divorce"]}, "answerKey": "B"}
{"id": "1d8d9e3504c8c58a3b923ddc155c19b0", "question": "What is the most famous constellation out of earth?", "question_concept": "earth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["one moon", "milky way", "god's creation", "stars", "universe"]}, "answerKey": "B"}
{"id": "95acebea992a26c3a7c3bfb45845fa83", "question": "If a reception is held with hotel guests walking by, what is the likely venue?", "question_concept": "reception", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["room service", "church basement", "lobby", "large room", "country club"]}, "answerKey": "C"}
{"id": "c2c2a387fd9a6a26cff636008de21f71", "question": "What is a place that is far away from your house and where you could consume beer?", "question_concept": "beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refrigerator", "friend's house", "keg", "neighbor's house", "kitchen"]}, "answerKey": "B"}
{"id": "57e96118fee6e2bbac5f59790fc833c0", "question": "If a court case is dismissed after hearing testimony, what would be a likely cause?", "question_concept": "hearing testimony", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["change of heart", "anguish", "anger", "boredom", "anxiety"]}, "answerKey": "A"}
{"id": "b9b82aa4c236cd342ff95455b8516a42", "question": "Sitting down quickly after eating beans could lead to what?", "question_concept": "sitting down", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["flatulence", "happiness", "laziness", "fall asleep", "comfort"]}, "answerKey": "A"}
{"id": "41fac392c6a5827c1b6682d5d3798e59", "question": "John was my neighbor, it was easy to talk to him. He was never what?", "question_concept": "neighbour", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["away", "distant", "remote person", "bore", "foe"]}, "answerKey": "B"}
{"id": "5c224410a40c9269b1e542cfcb430d35", "question": "Where do people want to have a lot of coffee?", "question_concept": "cup of coffee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "office", "desk", "kitchen", "ocean"]}, "answerKey": "B"}
{"id": "0b90c6710a65eb55fea4cc92895bf601", "question": "You stop and have food all around you, what are you?", "question_concept": "have food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stay alive", "wanted to survive", "nutrition", "grew", "full"]}, "answerKey": "E"}
{"id": "70af2b5df22ec96901350dfa3c9ee74f", "question": "James was meeting a friend.  They had planed a slow day. They didn't want to do much.  They just wanted what?", "question_concept": "meeting friend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relaxation", "panic", "alarm", "joy", "cheer"]}, "answerKey": "A"}
{"id": "f9243ef9f0037657c337d3c6a9832f05", "question": "The car's steering seem quite loose, but he still considered purchasing it because he needed something small and what?", "question_concept": "loose", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sturdy", "faithful", "bound", "compact", "packaged"]}, "answerKey": "D"}
{"id": "27f2074270ea8a5e8f5ec2a017ec4a50", "question": "Dan was a farmer with just one heifer.  But that was okay, he only kept her for milk, and he didn't think he'd find good farmland in a place as cold as where?", "question_concept": "heifer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arizona", "farm yard", "michigan", "german field", "dairy farm"]}, "answerKey": "C"}
{"id": "63b3652d54c8c0e571f6bb50de318bf0", "question": "It's Friday night and Alice puts off going to bed because she plans on doing what Saturday?", "question_concept": "going to bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hatred", "sleeping in", "rest", "making love", "insomnia"]}, "answerKey": "B"}
{"id": "0843c51212a3c2eee660fab5648c9e19", "question": "His phone was dead and they couldn't find the expressway, he opened up the glove compartment and handed his passenger the what to navigate?", "question_concept": "expressway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eastern united states", "michigan", "map", "choppers", "american city"]}, "answerKey": "C"}
{"id": "1b3d286458a7e7f069222de0376d06da", "question": "What would someone use a personal key for?", "question_concept": "key", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["car stand", "at hotel", "own home", "front door", "bus depot"]}, "answerKey": "C"}
{"id": "86e2aabfb9d401567f04d87a648ff776", "question": "The cat kept pestering it's owner, it was that time of the day and it was what?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["litter tray", "whiskers", "hungry", "feline", "thirsty"]}, "answerKey": "C"}
{"id": "092c24369367b3c7457198f3ce160fe3", "question": "Her voice lent her to the alto section, what group did she join?", "question_concept": "alto", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["symphony", "concerto", "choir", "theater troupe", "marching band"]}, "answerKey": "C"}
{"id": "cab9eea2a91b1bd5c0a01b11f594f154", "question": "Where are you likely to find a Japanese restaurant not run by people from Japan?", "question_concept": "japanese restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["california", "downtown", "large town", "tokio", "china town"]}, "answerKey": "A"}
{"id": "6e77de03bee86d6c20780e14f00944d0", "question": "Animals who have hair and don't lay eggs are what?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reproduce asexually", "males", "mammals", "attack", "ocean"]}, "answerKey": "C"}
{"id": "7f25dbab26165b3c8800c2733ca759d6", "question": "John was an aristocratic fox hunter.  Where might he live?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["england", "new hampshire", "street", "arkansas", "north dakota"]}, "answerKey": "B"}
{"id": "9024493a3edbaf555fda5b477e835bf5", "question": "Where is a grape likely to be being fed to someone else?", "question_concept": "grape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["field", "bathroom", "michigan", "minnesota", "painting"]}, "answerKey": "E"}
{"id": "fc59ab1a9e6d2b51126dd828d30e9167", "question": "Some food can be stored at room temperature until you open it, then you should keep it in what?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shop", "bookcase", "shelf", "refrigerators", "kitchen"]}, "answerKey": "D"}
{"id": "5a50ea4bb2d13dc4f620ebd45025d445", "question": "Sam couldn't get back to sleep because of a dream he had.  It was a what?", "question_concept": "dream", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["awake", "horror", "dreamworker", "reality", "nightmare"]}, "answerKey": "E"}
{"id": "8becd2ee4e86258566a9c2b0e6d9544e", "question": "If you're going to a party in a new town what are you hoping to make?", "question_concept": "going to party", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting drunk", "making new friends", "new contacts", "doing drugs", "set home"]}, "answerKey": "B"}
{"id": "2a21820a135e1a49883525c055c74a0b", "question": "How is riding a bike getting it to move?", "question_concept": "riding bike", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["practice", "sense of balance", "driving", "good balance", "pedalling"]}, "answerKey": "E"}
{"id": "e5adfec0b5ba691ec752f9b5e0fb8084", "question": "Where does one usually keep literature?", "question_concept": "literature", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["books and magazines", "own home", "kitchen", "shelf", "meeting"]}, "answerKey": "D"}
{"id": "406e15b76269d20b5448a91648094291", "question": "WHat type of keyboard is made up of one or more pipe divisions?", "question_concept": "keyboard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["killing", "typewriter", "office", "terminal", "organ"]}, "answerKey": "E"}
{"id": "9c596382ea15768f95b5ef9ceec191dc", "question": "The bell rang, and the congregation began to what in to the church?", "question_concept": "bell", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["run away", "wind instrument", "funnel", "blunderbuss", "associated with telephones"]}, "answerKey": "C"}
{"id": "7a3d0c94438a5c8a09364aaebb848a2c", "question": "James needed smooth sandpaper, but instead he got what type?", "question_concept": "smooth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rough", "non smooth", "uneven", "plastic", "bumpy"]}, "answerKey": "A"}
{"id": "1ef68db97654f30cd3701b942fadc934", "question": "Where would you borrow furniture if you do not have any?", "question_concept": "furniture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sewer", "neighbor's house", "apartment", "room", "floor"]}, "answerKey": "B"}
{"id": "abb090bbc572be1016bcd5f261f28e76", "question": "What must happen for an animal to and it's offspring to continue livng?", "question_concept": "living", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["death", "flying", "reproducing", "food consumed", "eventually die"]}, "answerKey": "C"}
{"id": "91f2532a832a35cba1b08a3c767be6da", "question": "I want my wine stored in darkness, where should it go?", "question_concept": "darkness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["movies", "bed", "moon", "vault", "cellar"]}, "answerKey": "E"}
{"id": "f8544c9679d27b747dfad3b8d7aac87a", "question": "If I want to open a steakhouse, what should I get first?", "question_concept": "steakhouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "florida", "wine", "texas", "building"]}, "answerKey": "E"}
{"id": "a7f423c1636ba9e36d18e381928c5dcc", "question": "Sarah didn't like to play but she didn't want to be sedentary and bored, either, so she took up what?", "question_concept": "play", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["serious", "longplay", "musical", "eat cake", "doing nothing"]}, "answerKey": "C"}
{"id": "e1d354cbfcd620e5dacf83c17746c4b3", "question": "Joe found spiders while checking something outside.  What might that be?", "question_concept": "spiders", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cupboard", "closet", "storage bag", "mail box", "garage"]}, "answerKey": "D"}
{"id": "53e1e50d204f6ad5a0f69429eadae82e", "question": "What would you do if your date does not show up?", "question_concept": "date", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wait for", "bathe", "go for haircut", "plan revenge", "dress nice"]}, "answerKey": "A"}
{"id": "48205cc84aab5e455b22e17c3cc7277d", "question": "What did the adult do before the job interview?", "question_concept": "adult", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "dress himself", "marry", "dress herself", "drive train"]}, "answerKey": "B"}
{"id": "0f7419d25337e0a75503a015ae777905", "question": "Most items in retail stores are what even when they are on sale?", "question_concept": "sale", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["overpriced", "purchase", "expensive", "park", "buying"]}, "answerKey": "A"}
{"id": "5cac4da628f0a58db980649079bd5784", "question": "John farms anemone in what type of facility?", "question_concept": "anemone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "swimming pool", "atlantic ocean", "nursery", "gulf of mexico"]}, "answerKey": "D"}
{"id": "78d1218aeff70a70904767349e3c4c53", "question": "Brawn opened the curtains so that the sun could do what?", "question_concept": "sun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dry clothes", "warm house", "warm room", "shine brightly", "get dark"]}, "answerKey": "C"}
{"id": "cce13a32fedb997c017d3fac87c34912", "question": "How might releasing energy that has built up feel?", "question_concept": "releasing energy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["damage", "wonderful", "exhaustion", "orgasm", "lazy"]}, "answerKey": "B"}
{"id": "6714487b839f648e348ac972ed114af3", "question": "What would you do if you have curiosity but are blind and paralyzed?", "question_concept": "curiosity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hear news", "analyse", "go somewhere", "examine thing", "see favorite show"]}, "answerKey": "B"}
{"id": "3e536d9253bfac45de83e8ee291ca143", "question": "Where might it be hard to get furniture to?", "question_concept": "furniture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment", "loft", "store", "rug", "stairs"]}, "answerKey": "B"}
{"id": "9f830faa0f8e3d7fb3a658c15a5fbe63", "question": "A great teacher can be what when you are attending school?", "question_concept": "attending school", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["detention", "graduate", "follower", "inspiration", "boredom"]}, "answerKey": "D"}
{"id": "bbcef409e0acb71b515acc144d5b402c_1", "question": "Where would you get jeans and other wearable items to take home with you?", "question_concept": "jeans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shopping mall", "museum", "laundromat", "clothing store", "bedroom"]}, "answerKey": "D"}
{"id": "cbb0c9a69ca0922371a48177087ef407", "question": "In what substance do clouds float?", "question_concept": "clouds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sky", "top of mountain", "air", "ground level", "outer space"]}, "answerKey": "C"}
{"id": "b92f786638796fc028947ac0e9a44fef", "question": "Where is the large area location of the empire state building?", "question_concept": "empire state building", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["manhattan", "office", "the city", "fifth avenue", "new york city"]}, "answerKey": "E"}
{"id": "5abeb4a2126597d4ef7b5a32e9e22abf", "question": "Where do most people make coffee?", "question_concept": "cup of coffee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coffee shop", "office", "table", "washing", "kitchen"]}, "answerKey": "E"}
{"id": "8d4b0312f02be445e09a9462873d02bb", "question": "What kind of service is my body a part of when I'm no longer here?", "question_concept": "body", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bodycam", "home", "coffin", "funeral", "graveyard"]}, "answerKey": "D"}
{"id": "f7140f00ddd8d1c5d93b05ea32ad1fff", "question": "Many people wanted to leave their country estates for row houses, what did they need to move to?", "question_concept": "row house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living less expensively", "england", "prison", "city", "town"]}, "answerKey": "D"}
{"id": "8b3b598a647dfd2d63fcedce5f461040", "question": "Where can someone get a new saw?", "question_concept": "saw", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hardware store", "toolbox", "logging camp", "tool kit", "auger"]}, "answerKey": "A"}
{"id": "7a900bc3a373806b6c56f0e19534005f", "question": "What would you do to a crime scene before asking a question?", "question_concept": "question", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["express information", "touch everything", "think", "give clue", "analyse"]}, "answerKey": "E"}
{"id": "3d79c10ddf26a5ed7dc0bb168fb0b3ed", "question": "The man didn't do great in college, all his best memories were late night with his brothers at the what?", "question_concept": "college", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["big city", "fraternity house", "school", "building", "big town"]}, "answerKey": "B"}
{"id": "b7091d2bfcea421d787ce9e7982f104a", "question": "In a horror movie victims usually trip when the run in order to do what in regards to the killer?", "question_concept": "run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["frightened", "run up stairs", "get away from", "go quickly", "go faster"]}, "answerKey": "C"}
{"id": "d060ab71d0efff3cab5960089a6bb3a2", "question": "The coach decided to make a lineup change, the team's effort was suffering from what?", "question_concept": "change", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stagnant", "stagnation", "tradition", "hunger", "paper money"]}, "answerKey": "B"}
{"id": "b399f6008d90dbd92bcce5abed4c1fd1", "question": "Where would you go if you want to buy some clothes?", "question_concept": "goods", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mall", "grocery store", "grocery store", "shop", "supermarket"]}, "answerKey": "A"}
{"id": "80c19c62338edae0e8a1f5c6fec0d29a", "question": "Where is food likely to stay dry?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["etna", "cupboard", "oven", "stomach", "fridge"]}, "answerKey": "B"}
{"id": "1a4e83b433620cb2d7d806882f8d57e4", "question": "What is it called when a person with mental illness is able to lead a relatively normal life?", "question_concept": "mental illness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["managed", "effectively treated", "recur", "cause delusion", "illusion"]}, "answerKey": "A"}
{"id": "b9e04a53c0ee7325b901de4d12d56884", "question": "Where do you keep musical instrument so it doesn't get scratched?", "question_concept": "musical instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bank", "orchestra", "case", "music room", "movie"]}, "answerKey": "C"}
{"id": "7490aa460f66000555a8a94008179cbb", "question": "The woman is watching television and trying to forget her day, what is her goal?", "question_concept": "watching television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["entertainment", "falling asleep", "getting fat", "crying", "relaxation"]}, "answerKey": "E"}
{"id": "ad8ee2965a33ff4b0e3d2ac732676594", "question": "While John Candy and Dan Aykroyd didn't run into a gazelle, you'd have to go where to see one?", "question_concept": "gazelle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eastern hemisphere", "the city", "open plain", "television program", "great outdoors"]}, "answerKey": "E"}
{"id": "64d2310eff6b661baeb41b4ccc392e35", "question": "When we are running what are we doing?", "question_concept": "run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stretches", "running from police", "learn to walk", "go quickly", "get out of bed"]}, "answerKey": "D"}
{"id": "6b1f5ebd9d0dbc7e34a598456a6091a8", "question": "It's dangerous to let pet birds free so it's better to keep them what?", "question_concept": "free", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slavery", "caught", "caged in", "topfree", "prisoner"]}, "answerKey": "C"}
{"id": "080ef6941410139d6869e78122bc741e", "question": "A beaver is know for building prowess, their supplies come from where?", "question_concept": "beaver", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["british columbia", "body of water", "wooded area", "pay debts", "zoo"]}, "answerKey": "C"}
{"id": "6c70d98cfb8e97fda8caefcee761a229", "question": "Zane doesn't like answering questions.  He's not good at it because he suffers from what?", "question_concept": "answering questions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["panic", "discussion", "attention", "confusion", "satisfaction"]}, "answerKey": "D"}
{"id": "75ac594b4fdbfba006e61315d1b2c815", "question": "Going public about a common problem can gain what for a celebrity?", "question_concept": "going public", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wide acceptance", "a degree", "pain", "getting high", "press coverage"]}, "answerKey": "A"}
{"id": "5a8e7d2f97f76adb23fbd59a009d16f0", "question": "The electricity went out and everyone was shrouded in darkness.  They all remained in their seats, because it would have been dangerous to try to find there way out.  Where mihgt they have been?", "question_concept": "electricity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["opera", "concert", "basement", "bedroom", "grand canyon"]}, "answerKey": "A"}
{"id": "178cb8153123716aa94f286b615149d4", "question": "Where could you find hundreds of beauty salon?", "question_concept": "beauty salon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clerk", "mall", "strip mall", "city", "neighborhood"]}, "answerKey": "D"}
{"id": "cc917ca0e03c91a5141920f5a902a36c", "question": "If it is Chrismas time what came most recently before?", "question_concept": "christmas", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["halloween", "summer", "easter", "kwaanza", "give gift"]}, "answerKey": "A"}
{"id": "a7d51b753c2113d8b2dbd0ebb5375855_1", "question": "If someone found out their brother was having a daughter, they would have to add a niece limb to the what?", "question_concept": "niece", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["family picture book", "family reunion", "brother's house", "family tree", "baby shower"]}, "answerKey": "D"}
{"id": "e71da9e95b321763c86e879a47bbd327", "question": "The criminal insisted he must do the crime to the bank teller, but she tried to convince him there were other ways in life and this was what?", "question_concept": "must", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["willing", "optional", "should not", "have to", "unnecessary"]}, "answerKey": "E"}
{"id": "ec86900559a0faf2aef066e511a4cfa6", "question": "what do you fill with ink to write?", "question_concept": "ink", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["squid", "fountain pen", "pencil case", "newspaper", "printer"]}, "answerKey": "B"}
{"id": "d312741df1b14bcbe358f4f30aff3994", "question": "He walked into the room and had a great shock, his friends had what him?", "question_concept": "shock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["expected", "wanting", "calm", "thundershock", "surprised"]}, "answerKey": "E"}
{"id": "0df3f58645b4bc306093845fb297a50e", "question": "He wasn't the hugging type, even when he meet friend he'd just do what?", "question_concept": "meet friend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have sex", "smile", "hug each other", "conversation", "handshake"]}, "answerKey": "E"}
{"id": "27d9b4df2ca50112d282331df4923e96", "question": "If you were lost you might need a map, the best place to find one on the road is at any what?", "question_concept": "map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["truck stop", "amusement park", "atlas", "mall", "gas station"]}, "answerKey": "E"}
{"id": "ab755203f41a2e241f0ee8a53c54f287", "question": "Where would you put a net if you wanted to use it?", "question_concept": "net", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sports", "fishing gear", "soccer game", "fishing boat", "badminton"]}, "answerKey": "D"}
{"id": "f13efb91090dd28fd2b3c1f4dde680fd", "question": "Sage loved communicating  He liked doing what with his peers?", "question_concept": "communicating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exchanging ideas", "confusion", "peer pressure", "response", "learning"]}, "answerKey": "A"}
{"id": "e98031901c815e55040d9fe28c4d9387", "question": "Where would a cat snuggle up with their human?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["floor", "humane society", "bed", "comfortable chair", "window sill"]}, "answerKey": "D"}
{"id": "fb64149cf01c5b496d986f56852273e9", "question": "What is a place that has large cable hanging overhead?", "question_concept": "cable", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["radio shack", "electrical device", "shower", "substation", "television"]}, "answerKey": "D"}
{"id": "2ac72eaf30a633c410b1bd658bbef0ba", "question": "Where do cars usually travel at very high speeds?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["freeway", "road", "race track", "alley", "parking lot"]}, "answerKey": "C"}
{"id": "22fc45d9e6d0baea4a5b0526504225b8", "question": "What might a person be watching if they see a man with a suitcase full of money?", "question_concept": "suitcase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["baggage compartment", "movie", "subway", "airplane", "cargo hold"]}, "answerKey": "B"}
{"id": "4ef3d70648ee3cea028bc5ed0fdfda28", "question": "Eating breakfast in bed while seeing a homeless person shivering outside your window may cause you to what?", "question_concept": "eating breakfast in bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mess", "hungry", "feel guilty", "indigestion", "spills"]}, "answerKey": "C"}
{"id": "059155c50d1b04da7373e309868e67d2", "question": "If I put in my key and open a hinged door, where am I likely entering?", "question_concept": "hinged door", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "safe", "own house", "building", "pantry"]}, "answerKey": "C"}
{"id": "33d023a6806390eb8195380331e17404_1", "question": "If somebody is working at a reception desk, they are located at the front entrance of the what?", "question_concept": "reception desk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motel", "hostel", "building", "lobby", "office park"]}, "answerKey": "C"}
{"id": "63f7ad481a63fc8c6dffe00519d4a167", "question": "If you're reading a newspaper from another country what are you doing?", "question_concept": "reading newspaper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learning about world", "education", "get angry", "concern", "eat cake"]}, "answerKey": "A"}
{"id": "a2daf73d33541af0846673afd8e49abe", "question": "They wanted to recognize his accomplishment, where should they put his name?", "question_concept": "name", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["certificate", "directory", "phone book", "lineup", "roster"]}, "answerKey": "A"}
{"id": "7d70208061ae3185bcfc9e912ee9e141", "question": "What is it called when a person tends to leave things to the last minute?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["procrastinate", "complete collection", "headache", "good time management", "have to hold"]}, "answerKey": "A"}
{"id": "9003c4748b08d5a734747e499599ff20", "question": "What will you do if you do not want to settle in one place?", "question_concept": "settle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["walk", "agitate", "wander", "remove", "disturb"]}, "answerKey": "C"}
{"id": "28aac6d39cdd270d2a6a28e1985484cb", "question": "Where would a person live that isn't in the metro area but still has good schools?", "question_concept": "bungalow", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["woods", "bed", "suburbs", "rural", "neighborhood"]}, "answerKey": "C"}
{"id": "8bdbb8caefcc607a9ec7579aa0c87cba", "question": "Jane works for the government as a senator, where does she spend a lot of time?", "question_concept": "government", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["capitol building", "everything", "washington d.c", "russia", "canada"]}, "answerKey": "A"}
{"id": "95a85df48902d23eb3fda25a99fca1a0", "question": "What is it called when two people in love have children?", "question_concept": "love", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take oath", "procreate", "matrimony", "please parents", "live life"]}, "answerKey": "B"}
{"id": "79c3378b7660d328902d7c0ad442a37f", "question": "What did the policemen do when they heard a cry from a distance?", "question_concept": "policemen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["run away", "hurry along", "fine motorists", "direct traffic", "help"]}, "answerKey": "E"}
{"id": "8c12e5864463cfcd03f4d0ab67949d01", "question": "It takes ambition to complete a job, but the first step is to what?", "question_concept": "ambition", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take care of proposals", "begin work", "in charge of project", "eat cake", "go to school"]}, "answerKey": "B"}
{"id": "e145618c2062eb9ea8928fdb0d42185e", "question": "Where would I not want a fox?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hen house", "english hunt", "mountains", "outside bedroom window", "england"]}, "answerKey": "A"}
{"id": "35872be88df5f6c4a6600020266a5458", "question": "What type of building has the most top floor?", "question_concept": "top floor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go down", "apartment building", "tall building", "office building", "cabin"]}, "answerKey": "C"}
{"id": "055817d8d703d3c2802545e3fccdcde3", "question": "What do humans do to other humans after death?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["celebrate", "burial", "life", "rebirth", "decomposition"]}, "answerKey": "B"}
{"id": "5ef6cdb85468df482e3aa6fa339d6e41", "question": "Where can you find a restaurant's phone number?", "question_concept": "restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["yellow pages", "town", "business sector", "town", "at hotel"]}, "answerKey": "A"}
{"id": "1e939cc6fef999953d692b57caab254b", "question": "What would you put coins into to make it work?", "question_concept": "coins", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stove", "water fountain", "desk", "purse", "jar"]}, "answerKey": "B"}
{"id": "3a3b5d4a517ef70d25eb558f1a622937", "question": "A patriotic guy with a camera is looking for a bald eagle, what is he likely to do with the eagle if he finds one?", "question_concept": "bald eagle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "canada", "minnesota", "thermal", "photograph"]}, "answerKey": "E"}
{"id": "a943522f7d407cef369d5d3f1bf48589", "question": "Where can you go to use a piano in your neighborhood if you don't have one?", "question_concept": "piano", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music school", "music store", "neighbor's house", "lunch", "drawing room"]}, "answerKey": "C"}
{"id": "57a343d72031b668e5eb91868420e915", "question": "Where would you get a shower curtain if you do not have one?", "question_concept": "shower curtain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["department store", "restaurant", "hotel", "dime store", "bathtub"]}, "answerKey": "A"}
{"id": "c4b1a57e7880b9cb367f9c67abf5605f", "question": "Kissing is normally an activity reserved for your romantic what?", "question_concept": "kissing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anus", "partner", "arousal", "trust", "cooperation"]}, "answerKey": "B"}
{"id": "e313d7967f72c2b880213daaaf4b7181", "question": "What does a child learn to do before school?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["count to ten", "state name", "dress herself", "clean room", "socialize"]}, "answerKey": "C"}
{"id": "3c7992df7fda23bcdeacb1f1f6b73448", "question": "He was getting advice for the job interview, they told him when talking to the interviewer always make what?", "question_concept": "talking to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get tired of", "small talk", "eye contact", "friendship", "social life"]}, "answerKey": "C"}
{"id": "d6644eacdb543a60545d2eb1ac7e6dbd", "question": "According to what book did an apple tree lead to the downfall of man?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bible", "spain", "harry potter", "new york", "woods"]}, "answerKey": "A"}
{"id": "d1ad9b79f54205b6b9ac19a27f9c2be5", "question": "The neighborhood had a great sense of community, there was always a crowd at the landing of the what?", "question_concept": "landing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stairwell", "arena", "ocean", "airport", "apartment building"]}, "answerKey": "E"}
{"id": "f116ee6620c0f171e5db54bc03a5f2e2", "question": "What might a kind person do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cross street", "talk to themselves", "open doors", "throw away", "study greek"]}, "answerKey": "C"}
{"id": "ea82f9e938cbfce85fb498ce46264253", "question": "What will a person do at work?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cross street", "draw attention to themselves", "make money", "falling down", "come home"]}, "answerKey": "C"}
{"id": "edbb57ac2f476679ae547f75ec2bef3e", "question": "John saw a fox running along the beach and was glad to be on the east coast.  Where might he have been?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tennessee", "south carolina", "louisiana", "oklahoma", "mountains"]}, "answerKey": "B"}
{"id": "07a99d5f2ca7028febeb9f09604b36c8", "question": "Name a location where you would not want to find mice.", "question_concept": "mice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["loft", "attic", "bell cat", "countryside", "laboratory"]}, "answerKey": "B"}
{"id": "b42ef8be1748c19fa5938de5396f8fad", "question": "The man started to learn jogging, what was he hoping to do?", "question_concept": "jogging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exhaustion", "getting in shape", "fitness", "injure himself", "fatigue"]}, "answerKey": "B"}
{"id": "236691d38665d7bcdd0c9b9834252a51", "question": "Where do most people turn to get information on their phones?", "question_concept": "information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["internet", "book", "online", "google", "manual"]}, "answerKey": "D"}
{"id": "8ef78abb86fc282ccb02bbc495f13030", "question": "What happens to a body after death?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rebirth", "human experience", "sadness", "decomposition", "obesity"]}, "answerKey": "D"}
{"id": "313d033c33ec475e04e628f87c5686bd", "question": "What type of non-vegetarian soup is one likely to find a potato?", "question_concept": "potato", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beef stew", "own kitchen", "clam chowder", "kitchen cabinet", "pantry"]}, "answerKey": "C"}
{"id": "d581e0ad6a4c89465dc1a527bd2d3f77", "question": "Though she had a disability, what did her encouraging and positive coach see in her?", "question_concept": "disability", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["qualification", "strength", "pity", "competence", "potential"]}, "answerKey": "E"}
{"id": "f232bfea2a7611999688a252e476c040", "question": "They had a theory of what they could do in t he big game, so over and over they would what?", "question_concept": "theory", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "practice", "fact", "practical", "practise"]}, "answerKey": "B"}
{"id": "91756d8e475d8d59fa0a4e35f408e366", "question": "When you see something rise, you are where in relation to it?", "question_concept": "rise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sun set", "near", "fall", "below", "lower"]}, "answerKey": "D"}
{"id": "866ea9c668c0b42df19fa20865e31f77", "question": "They were getting ready for a really long hike, he put the food can in his what?", "question_concept": "food can", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabinet", "house", "recycling center", "backpack", "make person sick"]}, "answerKey": "D"}
{"id": "22015315e7ff79386877828b4fa27799", "question": "Where would you keep a rug near your front door?", "question_concept": "rug", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["persia", "desk", "table", "living room", "hall"]}, "answerKey": "D"}
{"id": "484f6e4fb8e6431b010c299490b72e3c", "question": "When you slip from a ladder propped on anything what will you do?", "question_concept": "anything", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["obesity", "fall down", "matter to", "whatever", "surprise"]}, "answerKey": "B"}
{"id": "7322d0dcf2e27c7032626a3639f5696b", "question": "What do you do when you need to get food?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "disneyland", "refrigerators", "pantry", "shop"]}, "answerKey": "E"}
{"id": "0519b0b0869681c2884f53dbfa43e538", "question": "Brad tried to arise from bed but he could not.  Instead, he just continued to do what?", "question_concept": "arise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go down", "fall down", "lie down", "lie to himself", "sit down"]}, "answerKey": "C"}
{"id": "1ab04c0501b815b2a48f2581f04215a8", "question": "If a heifer is really high quality, you might take her where?", "question_concept": "heifer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home", "dairy farm", "cattle show", "dairy barn", "corral"]}, "answerKey": "C"}
{"id": "7776b10c7bb96f3fe5e026678673634d", "question": "What do people want to acquire from opening business?", "question_concept": "opening business", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home", "wealth", "bankruptcy", "lose money", "get rich"]}, "answerKey": "B"}
{"id": "f7c005244d406b9bde48dc8c22003af1", "question": "What has someone who had finished their undergraduate done?", "question_concept": "undergraduate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["graduated", "masters", "postgraduate", "phd", "professor"]}, "answerKey": "A"}
{"id": "88501d528c855e2b533b3fea2f86183d", "question": "Where are bus stops more common in what parts?", "question_concept": "bus stop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "getting off of bus", "airport", "urban area", "towns"]}, "answerKey": "D"}
{"id": "3d9c3253e24fb108cea9083e8a853cf2", "question": "Bill wanted to pick up a stranger, preferably a responsible one with kids.  Where might he look for one?", "question_concept": "stranger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus station", "paradise", "train station", "park", "sea"]}, "answerKey": "D"}
{"id": "9808782b2e2e1bfbfa27c41e605bfffe", "question": "Where might a lemur frolic in the market?", "question_concept": "lemur", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desert", "hole", "india", "cage", "rain forest"]}, "answerKey": "C"}
{"id": "c432b860fcd7297751ff5254ec4a7956", "question": "What might I place under the furniture?", "question_concept": "furniture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rug", "room", "toy", "friend's house", "building"]}, "answerKey": "A"}
{"id": "732af155f677a51d05d0c9e080d598b6", "question": "Everybody began performing once their director stated what?", "question_concept": "performing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fear", "injury", "happiness", "action", "cut"]}, "answerKey": "D"}
{"id": "48abc2c113623fd72f758502529f93a5", "question": "By learning about the world, many poor college students gain what?", "question_concept": "learning about world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pleasure", "greater mobility", "desire to travel", "global warming", "increased security"]}, "answerKey": "C"}
{"id": "03f06f77aaf80b5f5e296ffbd11e9d82", "question": "Where are required to carry books all day?", "question_concept": "books", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["friend's house", "university", "large city", "storage", "table"]}, "answerKey": "B"}
{"id": "e7084c166ec67d0f983a26e055e845c6", "question": "where is seaweed from?", "question_concept": "seaweed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beach", "sea", "ocean", "water", "sea plant"]}, "answerKey": "C"}
{"id": "c55c31b5a2aa996f3b75ad88c017a6b9", "question": "how can i store cooked steak?", "question_concept": "steak", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["oven", "freezer", "plate", "tupperware", "grill"]}, "answerKey": "B"}
{"id": "463521a93ae71e93bea8b97cdf7a6792", "question": "John wanted to clean all of the dust out of his place before settling down to watch his favorite shows.  What might he hardest do dust?", "question_concept": "dust", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["closet", "under the bed", "television", "attic", "most buildings"]}, "answerKey": "D"}
{"id": "c036ce033bc429ac1aba0a6ac8d057e1", "question": "Something had the nerve to break into the garbage last night, what did it?", "question_concept": "nerve", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eyes", "animal", "fingertips", "brainstem", "human body"]}, "answerKey": "B"}
{"id": "db7f2bfdabcf53d6778fd7af80b603d2", "question": "Where would you go to get some pamphlets if you want to own them?", "question_concept": "pamphlets", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bookstore", "drawer", "health department", "mail box", "library"]}, "answerKey": "A"}
{"id": "8605fd2affc796d79073d0f3ef0761c9", "question": "The audience cheered when a goal was scored, what were they spectating?", "question_concept": "audience", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "sporting event", "concert hall", "show", "television"]}, "answerKey": "B"}
{"id": "ad37795fd9e3a65553683ff305b5113d", "question": "What western state has thousands of miles of shore?", "question_concept": "shore", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["picture of sea side", "seaside town", "beach", "california", "see side picture"]}, "answerKey": "D"}
{"id": "bcd51af35d691f5c3b6b548096ab1559", "question": "Everybody seemed to be crying at the holy site, the tour guide explained that this was what?", "question_concept": "holy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["profane", "halibut", "damaged", "common", "halibut"]}, "answerKey": "D"}
{"id": "b5345f15d5b451562ab9e0851e7f394f", "question": "The smile gave away that the what was one of happiness?", "question_concept": "smile", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["manual", "rainbow", "cry", "frown", "make others happy too"]}, "answerKey": "C"}
{"id": "6a884d5d8febfdd86fcf68ff1a904d9b", "question": "Where is a public monument likely to be erected by a city?", "question_concept": "monument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["municipal park", "office", "state park", "cemetary", "public gardens"]}, "answerKey": "A"}
{"id": "a1303b5177df0a5b653c9abd7d5f5e08", "question": "Where would a person live if they wanted no neighbors?", "question_concept": "bungalow", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["housing estate", "neighborhood", "mars", "woods", "suburbs"]}, "answerKey": "D"}
{"id": "315baf79f8dd3673f67a90de0758240e", "question": "Where is the control room that controls a PWR located?", "question_concept": "control room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "factory", "window", "prison", "nuclear power plant"]}, "answerKey": "E"}
{"id": "01f01cc3ad152773ef42b30e926912bf", "question": "What happens to a dog before someone puts up posters of them?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get lost", "require water", "trained", "bark", "roll over"]}, "answerKey": "A"}
{"id": "f192cfacbaa2f7e0e879f673c8e076a7", "question": "Where are the most famous BBQ steakhouses in america?", "question_concept": "steakhouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["texas", "building", "kansas city", "maine", "falling down"]}, "answerKey": "A"}
{"id": "ab8d5e21a2cf34b60a04768b01f1f8e9", "question": "He kept plugging away in his cubicle, it seemed he was the only person not called into the what?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "conference", "assessment", "demonstration", "garage"]}, "answerKey": "B"}
{"id": "5d1df1daa886efb78db2103ddc1398eb", "question": "If you're attending school and are falling asleep you're likely experiencing what?", "question_concept": "attending school", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boredom", "malaria", "graduate", "inspiration", "detention"]}, "answerKey": "A"}
{"id": "2f8b35d352097cc9277599be49fab0b3", "question": "I want to buy a gong, where should I look for one?", "question_concept": "gong", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["orchestra", "church", "chinese temple", "chinatown", "music store"]}, "answerKey": "E"}
{"id": "18eb6a3b54ccf4989e268cfb9ea90f9c", "question": "What would friends do if they need each others' help?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["call each other", "group together", "understand each other", "meet for lunch", "part company"]}, "answerKey": "B"}
{"id": "3e12400bc5a2038a747edf2605787fe8", "question": "When people are playing a game, what is their motivation to play?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["believe in god", "dance", "desire to win", "destroy each other", "run amok"]}, "answerKey": "C"}
{"id": "72baf6ca5c4daa01c2cc7fda22183db8", "question": "Where could there be a battle that involves words?", "question_concept": "battle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["court room", "war", "video game", "iraq", "church"]}, "answerKey": "A"}
{"id": "9bac07574c966cae34c85e9f25538cba", "question": "John didn't mind getting in line.  It was what game after that he hated.  The time, the sore feet. He did not like doing what?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have to wait for", "standing in line", "eat cake", "less confusion", "being ordered"]}, "answerKey": "B"}
{"id": "fe2a21ddb1bde76025a961126044a9a3", "question": "What is the process of going somewhere?", "question_concept": "going somewhere", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fire", "energy", "car", "transporting", "staying in place"]}, "answerKey": "D"}
{"id": "d03e09b22927542d6b0d5ebe233e467c", "question": "The old man needed to have rest multiple times a day, he would do what on the couch and catnap?", "question_concept": "have rest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lay in bed", "lay in bed", "go to bed", "relax", "lie down"]}, "answerKey": "E"}
{"id": "e63a210053cf7f961ca0b5a7e6eb355d", "question": "The end of the barrel of what primitive firearm is bell shaped?", "question_concept": "bell", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["barbell", "funnel", "blunderbuss", "wind instrument", "kettlebell"]}, "answerKey": "C"}
{"id": "a4b4242fab25e86a9d7ffedcaecdcdbe", "question": "Where is a good place to store pamphlets in your home or office?", "question_concept": "pamphlets", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["library", "health department", "mail box", "drawer", "bookstore"]}, "answerKey": "D"}
{"id": "ec8797b12e3c6666ebe70b2a7680b66f", "question": "Many humans enjoy fishing and enjoy another relaxing activity at the same time, what activity is it?", "question_concept": "fishing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting tied up lure.", "looking for information", "get wet", "drink beer", "sit quietly"]}, "answerKey": "D"}
{"id": "4536489e5d8e02aadc3fcc7a55effe20", "question": "Where would you get some maps that you own?", "question_concept": "maps", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bookstore", "library", "electrical circuit", "cabinet", "important when traveling"]}, "answerKey": "D"}
{"id": "0854478d174c9127064f0d4b58df7e62", "question": "Where is a good place to put a hamburger?", "question_concept": "hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["resturant", "fast food restaurant", "mouth", "kitchen", "pizza"]}, "answerKey": "C"}
{"id": "4b7d1b70060cd1f1a7321795f62a7325", "question": "Where is a handy place to store a steel pen in your office?", "question_concept": "steel pen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["car shop", "desk drawer", "car.", "warehouse", "hand"]}, "answerKey": "B"}
{"id": "0e6a005eec5e6746f3facf4d608bfd8b", "question": "A story about World War II would be set when?", "question_concept": "story", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["book or library", "book or magazine", "newspaper", "past", "future"]}, "answerKey": "D"}
{"id": "2d2b69ad187b7c40273ab13caab7dc19", "question": "What type of geographic area will you find a marmot?", "question_concept": "marmot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mountainous area", "wood pile", "jungle", "petting zoo", "animals"]}, "answerKey": "A"}
{"id": "fde1f9bfc33da302449c0b950d16c0ea", "question": "Most people make stupid assumptions that are based on their prejudices.  What might they do instead to achieve better outcomes?", "question_concept": "most people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["set table", "think", "read books", "play games", "lie"]}, "answerKey": "B"}
{"id": "3c90a632f46aeab11fbb73aa59a33892", "question": "What is something children can do while traveling in a car?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listen to music", "watch television", "play chess", "walk", "play basketball"]}, "answerKey": "A"}
{"id": "1f3ccb722600da7d862531416934949a", "question": "Where would you hear a trumpet along with other instruments made from the same material?", "question_concept": "trumpet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music store", "bass", "brass band", "orchestra", "marching band"]}, "answerKey": "C"}
{"id": "46ba5d2b8cfc6708e5e2618568d8730e", "question": "The audience listened to the orchestra play, where were they watching the performance?", "question_concept": "audience", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert hall", "museum", "school", "hockey game", "sporting event"]}, "answerKey": "A"}
{"id": "f8a2cbc7189b92a809ce9cd857030621", "question": "Stabbing to death of a person is what sort of way to die?", "question_concept": "stabbing to death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pool of blood", "gruesome", "charming", "being arrested", "killing"]}, "answerKey": "B"}
{"id": "225287e06c993feee34e0f06b25f6ba8", "question": "What are you getting from you boss at the end of the week?", "question_concept": "getting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["asking for", "money", "food", "work", "energy"]}, "answerKey": "B"}
{"id": "e211b1a3f3401d164c8b0bfc10160caa", "question": "If you have a ticket and you are planning to eat hot dogs, where would you go?", "question_concept": "ticket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lottery", "person's hand", "baseball stadium", "movie", "kitchen"]}, "answerKey": "C"}
{"id": "fce1c5d069758aea57a787fc98dcf7a9", "question": "Where is a great place to buy fresh fruit?", "question_concept": "fruit", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["san francisco", "refrigerator", "big box retailer", "tree", "market"]}, "answerKey": "E"}
{"id": "c0d75f9fbf30aa3a612f16edb20d6b8d", "question": "The man took paperwork to other people to consult over it, where was he heading?", "question_concept": "paperwork", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "meeting", "office", "table", "work"]}, "answerKey": "B"}
{"id": "d07f149d8d953dcc45dda432194c375e", "question": "Stark was just having fun, and he wasn't hurting anyone.  What might have he been doing?", "question_concept": "having fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["painting his nails", "playing marbles", "constructing", "need for rest", "wild ride"]}, "answerKey": "B"}
{"id": "080a9cf2d6447a9a4d98b0af311e10da", "question": "The church was giving assistance, what were they hoping to accomplish?", "question_concept": "giving assistance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exhilliration", "hardship", "risk taking", "helping others", "happiness"]}, "answerKey": "D"}
{"id": "111501a49dd41ceed9c2073eed5d2b72", "question": "I you believe in god, where will you go when you die?", "question_concept": "god", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["heaven", "church", "imagination", "synagogue", "monastery"]}, "answerKey": "A"}
{"id": "7bb87c6d8eab57d4e983f60025b1f0dc", "question": "What can eating hamburger cause immediately after eating it?", "question_concept": "eating hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tasty", "health problems", "eat cake", "indigestion", "gain weight"]}, "answerKey": "D"}
{"id": "5c2bc4335c8860342ec2d568ceb6ac6b", "question": "Where is a shelf likely to be hidden behind a door?", "question_concept": "shelf", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refrigerator", "bookstore", "cupboard", "school building", "wardrobe"]}, "answerKey": "C"}
{"id": "083861fc5ebb9226fff70544f3f83d2b", "question": "The man got a pail to catch the draining motor oil, where was he likely doing this at home?", "question_concept": "pail", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garage", "hardware store", "utility room", "wishing well", "laundry"]}, "answerKey": "A"}
{"id": "520b0eea9148e3cb4d45aa69a55491eb", "question": "What kind of cold storage could you find in your house?", "question_concept": "cold storage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ice pack", "freezer", "laboratory", "warehouse", "refrigerator"]}, "answerKey": "E"}
{"id": "ef6ede0af827ddd1dc7bbeb36a6fdd22", "question": "Where could you go to between 1000 and 10000 restaurant?", "question_concept": "restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["big city", "town", "small town", "canada", "yellow pages"]}, "answerKey": "A"}
{"id": "d47986deb91d64b2b15d385da3d2f483", "question": "The pitcher stepped on the mound ready to throw, where was he located specifically?", "question_concept": "mound", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hell", "baseball stadium", "golf course", "africa", "baseball diamond"]}, "answerKey": "E"}
{"id": "c3b7f4196b12714940ac1b9417194df4", "question": "Where is a statute found on a platform?", "question_concept": "platform", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["below", "arena", "concert hall", "museum", "building"]}, "answerKey": "D"}
{"id": "5d03ad171fd661a28da5b6eb79967a6b", "question": "If it's not used for hair a round brush is an example of what?", "question_concept": "round brush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hair brush", "ladies bathroom", "art supplies", "shower", "hair salon"]}, "answerKey": "C"}
{"id": "7c95d753943c58757fe6e1ccff8aea14", "question": "His parents thought he was suffering from boredom, but the teen loved to lay in bed and just do what?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meet interesting people", "lift weights", "listen to music", "play chess", "entertain"]}, "answerKey": "C"}
{"id": "88d8bfb9dc8e77ef642acbe1a129f3db", "question": "At the picnic she was stuck eating hamburger, she was worried because she forgot her chewables to prevent what?", "question_concept": "eating hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat cake", "have fun", "food poisoning", "heartburn", "gain weight"]}, "answerKey": "D"}
{"id": "b1a9b20793b46e46e1beedadbf852f84", "question": "The electrode wouldn't spark, it turned out that the what hadn't been connected?", "question_concept": "electrode", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["battery", "electronic equipment", "electrolytic cell", "charge", "tube"]}, "answerKey": "A"}
{"id": "81e016974d33fe383c848b6c819791cd", "question": "For what entity should the government work?", "question_concept": "government", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["country", "democracy", "canada", "civilization", "tax office"]}, "answerKey": "A"}
{"id": "7cf54544d54818d53e7088c0749a3eca", "question": "What must a student in engineering do?", "question_concept": "student", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["answer question", "learn language", "do mathematics", "be able to count", "begin to study"]}, "answerKey": "C"}
{"id": "6acd88b9b5dd15e23bbcc3fd679100a8", "question": "The teacher knew her students understood division, what was she hoping they would learn next?", "question_concept": "division", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["multiplication", "multiply", "putting together", "unity", "pay debts"]}, "answerKey": "A"}
{"id": "c96a86957a9ab1d8ca0aeeb7f040d87a_1", "question": "There were times where kids wanted to know a definition, so there was a nice big dictionary in the what?", "question_concept": "dictionary", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pocket", "classroom", "table", "library", "shelf"]}, "answerKey": "B"}
{"id": "6a1bf527af9ed0685ac5e2bf0bd76647", "question": "Riding a bike for a long time can cause what?", "question_concept": "riding bike", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enjoyment", "fatigue", "falling down", "getting lost", "thirst"]}, "answerKey": "B"}
{"id": "094fe91b20b03c647325fa2ee94470b3", "question": "What could happen to a cat other than wanting food?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feline", "thirsty", "sharp claws", "pussycat", "hungry"]}, "answerKey": "B"}
{"id": "bee2a6eadfaf7a4fa0a214e341ddbe5b", "question": "If you turn off the music in a room with no other noise that room would be what?", "question_concept": "music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["silent", "opera", "silence", "television", "elevator"]}, "answerKey": "A"}
{"id": "2f97a77d155cb99092e8a7c055737b03_1", "question": "In what country are the most fast food restaurants?", "question_concept": "fast food restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new york", "blocks of flats", "center of town", "america", "big cities"]}, "answerKey": "D"}
{"id": "bc268cd19e2c95c78967fd6b9092fb90", "question": "I want to use string to keep something from moving, how should I do it?", "question_concept": "string", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tie around", "wind around", "weave", "stringbed", "ball up"]}, "answerKey": "A"}
{"id": "060cad0d3c007ceb151db9907bfcb214", "question": "Where would walk through a central passage to catch an elevator?", "question_concept": "central passage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tomb", "arena", "access rooms", "public building", "house"]}, "answerKey": "D"}
{"id": "29c2cc0ba85b4afb9c9d29801469a68f", "question": "A potato is kept in the cellar, where is likely to be stored?", "question_concept": "potato", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["farmer's market", "grocery bag", "pantry", "bushel basket", "fridge"]}, "answerKey": "D"}
{"id": "6cb895ce89995f6be422f7c4167c7638", "question": "What do people do when networking?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["build trust", "hurry home", "ignore people", "believe in god", "jump to conclusions"]}, "answerKey": "A"}
{"id": "839f3c37622c1ed5eebc9cd0b9d658e8", "question": "Where can you store you spare linens near your socks?", "question_concept": "linen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospital", "chest", "home", "dresser drawers", "cabinet"]}, "answerKey": "D"}
{"id": "3957ac6bab96fc9d4f173ada4692d16b", "question": "What do people do when they think too quickly?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jump to conclusions", "hurry home", "build trust", "pay bills", "sing"]}, "answerKey": "A"}
{"id": "a4f5e5412f0f8ac9190db1730db07a90", "question": "What is someone likely to want as a result of sex?", "question_concept": "sex", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sexploiter", "chicken", "reproductive cycle", "procreation", "human experience"]}, "answerKey": "D"}
{"id": "cb5b39878be0e05a3ffe783801adbc3b", "question": "What might someone do after they finish creating art?", "question_concept": "creating art", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["frustration", "relax", "eat", "enlightenment", "communication"]}, "answerKey": "B"}
{"id": "985a4f1a3f31f1ba6654f4fc48f504df", "question": "To get clean clothes you to what to them?", "question_concept": "clean clothes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get dirty", "writing", "use water", "launder", "soap"]}, "answerKey": "D"}
{"id": "5d687fe9c95436ce84230c996d34382d", "question": "The person tried to reduce his weight with a shrink ray, but he got it backwards and only did what?", "question_concept": "reduce", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grow", "gain weight", "make larger", "augment", "get bigger"]}, "answerKey": "C"}
{"id": "af11faa29097b71141fe192ad019d1dd", "question": "Christine couldn't be having a baby at her age, she thought to herself. What was Christine?", "question_concept": "baby", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["old person", "begin to talk", "adult", "old man", "girl"]}, "answerKey": "A"}
{"id": "07fd8b0aed06406fedb137d11b07a890", "question": "Joe plays a percussion instrument in something.  What might be play in?", "question_concept": "percussion instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["own home", "music store", "marching band", "orchestra", "party"]}, "answerKey": "D"}
{"id": "7044d82a456d0fa6f0210abb03cbf2c4", "question": "If I'm playing ball, I'm mostly trying to do what?", "question_concept": "playing ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["losing", "competition", "having fun", "win", "injury"]}, "answerKey": "C"}
{"id": "e53ba4c7d2a818bdb6001e6924bc8896", "question": "What do the terms need to be in order to compete against someone?", "question_concept": "compete against", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cheat", "fair", "in competition", "practice", "sabotage"]}, "answerKey": "B"}
{"id": "ecbc1ab06ad1ed6c53e5293d7a90ebd3", "question": "If you wanted to show off silk, what item could it be on?", "question_concept": "silk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jean", "mulberry tree", "garments", "expensive clothing", "parachutes"]}, "answerKey": "D"}
{"id": "9a356ff463c042d04ba45bfd627bac20", "question": "Where is known to be a wealth of information?", "question_concept": "information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "internet", "meeting", "library", "book"]}, "answerKey": "D"}
{"id": "0a5c069836784c3d574828d85a20a074", "question": "I saw the receptionist carelessly toss my resume into the drawer, where did I want it to end up?", "question_concept": "drawer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["file cabinet", "nightstand", "kitchen cabinet", "office desk", "the floor"]}, "answerKey": "D"}
{"id": "f996430ce208606452868fd2e739d409", "question": "What will happen if you inject water into yourself?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dilute", "thin blood", "take several forms", "wet clothes", "move mountains"]}, "answerKey": "B"}
{"id": "26c854d933d2115e7636fdcde57eb463", "question": "Athletes soak in hot tubs to relieve what after playing baseball?", "question_concept": "playing baseball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fame", "errors", "pain", "strikes", "sore muscles"]}, "answerKey": "E"}
{"id": "83c25b9a5db5f9b3fd1ff6c7453d23d0", "question": "What does a gambler do that causes him or her to be unhappy?", "question_concept": "gambler", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play cards", "double winnings", "lose money", "play poker", "to win the prize"]}, "answerKey": "C"}
{"id": "a0d02fc32878efdf0b0d420972943492", "question": "There's one obvious reason to eat vegetables, they're plain what you?", "question_concept": "eat vegetables", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lose weight", "good for", "bland", "chewing", "fibre"]}, "answerKey": "B"}
{"id": "73fbd2caac2c3786ca810adfe7030273", "question": "John was a bit think in the head, but he knew that he never saw the lady before.  They were what?", "question_concept": "thick", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pay debts", "slender", "unacquainted", "free flowing", "sparse"]}, "answerKey": "C"}
{"id": "6c515b068b4d3aa88a5382224d9b866d", "question": "Where would you hear a violin along side many string and wind instruments?", "question_concept": "violin", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "string quartet", "orchestra", "kitchen", "music room"]}, "answerKey": "C"}
{"id": "0af371b94fb414860b13eea6009ccc31", "question": "What is the sun ultimately responsible for?", "question_concept": "sun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["earth warming", "sun tan", "light", "life on earth", "heat"]}, "answerKey": "D"}
{"id": "38e61d4be0da46b3cbbd76dc20bce677", "question": "Mandy lived in a train station.  She longed to see distant places. Where might she imagine going?", "question_concept": "train station", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["downtown area", "centre of town", "bedroom", "europe", "big city"]}, "answerKey": "D"}
{"id": "cebc07bd5080cc72862cb333b10d782d", "question": "Joe is a  squirrel, which is an animal. He probably lives in what sort of place.", "question_concept": "animal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pet store", "outside", "woodland", "ocean", "cafe"]}, "answerKey": "C"}
{"id": "de0386024f32cdf277a785a851b97544", "question": "Where could a personal ficus live?", "question_concept": "ficus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabin in the woods", "california", "front yard", "conservatory", "tropical forest"]}, "answerKey": "C"}
{"id": "9b62cd7f89716f393239e6c6ff3e11d5", "question": "The shark actually counted as evidence, so where did the legal team bring it?", "question_concept": "shark", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["court room", "shallow waters", "poker game", "sea world", "pond arena"]}, "answerKey": "A"}
{"id": "8b25332de2894ab38784235838d38cec", "question": "If the president wanted to ban snakes, where would he issue such a decree?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["street", "tropical forest", "garden of eden", "new mexico", "white house"]}, "answerKey": "E"}
{"id": "dd4a811d18549f1ae1954cf938b28536", "question": "They were searching for rocks, so they missed the birds overhead as they stared at the what?", "question_concept": "rocks", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ground", "drawer", "surface of earth", "pizza", "waterfall"]}, "answerKey": "A"}
{"id": "e2ff952c17faf1c56a970502630d4c86", "question": "Her son scraped his knee, she fetched a bottle of peroxide from the what?", "question_concept": "bottle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "diaper bag", "liquor store", "hollow log", "medicine cabinet"]}, "answerKey": "E"}
{"id": "3a6140e475cbbd3ee1da5ba9a6953597_1", "question": "Where would you expect to find a dictionary along side other writings you can borrow?", "question_concept": "dictionary", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "shelf", "explain meaning of words", "table", "library"]}, "answerKey": "E"}
{"id": "e75e0c11e2d5a7b634455a1b4b76856c", "question": "What would be necessary for getting in shape?", "question_concept": "getting in shape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["good health", "exercise", "muscle tone", "sweat", "feel better"]}, "answerKey": "B"}
{"id": "3b9ccdcb1c932c46a38e040d3e6c7f5b", "question": "A statue that shoots liquid is called a what?", "question_concept": "statue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water fountain", "large city", "museum", "pool", "central park"]}, "answerKey": "A"}
{"id": "6a29b657b29e1506284d8328dffbbd21", "question": "If you have a child who gets in trouble for being hyperactive you may need to teach them how to what down?", "question_concept": "trouble", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "calm", "being good", "good behavior", "safe"]}, "answerKey": "B"}
{"id": "96cb628fb7ed2f53245598f707ed2b80", "question": "John loved to paint houses.  How did he usually do it?", "question_concept": "paint", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clothes get stained", "with brush", "wallpaper", "electrical circuit", "draw"]}, "answerKey": "B"}
{"id": "bd4e80fa6642a76c064d0bc924411fb0", "question": "When you wipe you feet on the door mat and walk through the door where do you enter?", "question_concept": "mat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["a chair", "school", "living room", "doorway", "bathroom"]}, "answerKey": "C"}
{"id": "05490e6c191fbc3c2fe0033ed0bd8aa0", "question": "What can you use to store a book while traveling?", "question_concept": "book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["library of congress", "pocket", "backpack", "suitcase", "synagogue"]}, "answerKey": "D"}
{"id": "6abd34442438509b4a00c69d6fd24764", "question": "Where would you find gazelle under a G?", "question_concept": "gazelle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["open field", "ivory coast", "dictionary", "steppe", "encyclopedia"]}, "answerKey": "E"}
{"id": "e58eb0ec4197c29e961a7bdd4d67de4e", "question": "Competing can lead to great highs, and also great lows when suffering what?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["winning or losing", "aggression", "gain", "defeat", "sweat"]}, "answerKey": "D"}
{"id": "597d2a1c9df7962218d8b807df1f8212", "question": "What blocks sunshine?", "question_concept": "sunshine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["summer", "park", "desktop", "sea", "moon"]}, "answerKey": "E"}
{"id": "68f6ac445cc008d93f931b999b44b0ba", "question": "When you feel too much heat in your home you can turn on what?", "question_concept": "heat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coolness", "fan", "get wet", "coldness", "air conditioning"]}, "answerKey": "E"}
{"id": "aa4c5d2d348796b8d7fa324f27f4c34f", "question": "Where would you store a pillow case that is not in use?", "question_concept": "pillow case", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen cupboard", "bedding store", "england", "drawer", "bedroom"]}, "answerKey": "D"}
{"id": "7400e9c4a2c8e600a0f7e2d162a07837", "question": "If the kitten was going to grow up to be a mouser like it's mother, where should it spend most of it's time?", "question_concept": "kitten", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelter", "floor", "warm place", "farmhouse", "living room"]}, "answerKey": "D"}
{"id": "fad197409a977126c9587eccd240ceea", "question": "Where is that man buying silk from?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["space shuttle", "theater", "china", "indian resteraunt", "bar"]}, "answerKey": "C"}
{"id": "f09038444aeb1a048f04dedd5b97b769", "question": "Where is a teacher likely to keep her clavichord?", "question_concept": "clavichord", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living room", "parlor", "music hall", "music room", "museum"]}, "answerKey": "D"}
{"id": "0aa23ad1ba9f28bc3e0185237a7ce1cc", "question": "Where are you if your bieifcase is going through an x-ray machine?", "question_concept": "briefcase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["luggage store", "courtroom", "airport", "office building", "hand"]}, "answerKey": "C"}
{"id": "06be29539ad3e1fbd7b53b05243f4bd7", "question": "They were kissing each other good bye, they had no worries because their relationship had a strong foundation of what?", "question_concept": "kissing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["partner", "trust", "cooperation", "bricks", "herpes"]}, "answerKey": "B"}
{"id": "bbe0a1ad733e5699f991ff91b3712a6f", "question": "Why would you take a bus to work?", "question_concept": "take bus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["commute", "flying", "get somewhere", "travel", "go home"]}, "answerKey": "A"}
{"id": "9e5ce2b7d9eb404cdf8c7317dd0b5a59", "question": "If you are hungry and going fishing, why would you be going fishing?", "question_concept": "going fishing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["to see the fish", "have fun", "catching fish", "wet clothes", "killing"]}, "answerKey": "C"}
{"id": "ffde211723f55e9744f94cbc14488a23", "question": "Dogs are very loyal if they have a good owner, they will always what them?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fleas", "eat cake", "attack", "defend", "run fast"]}, "answerKey": "D"}
{"id": "5ff8b0deed53b9ff91d58bd5b6f85bdf", "question": "What does a farmer need to do to make  a maze on his farm in the fall?", "question_concept": "farmer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["seed plants", "plant seeds", "garden", "grow corn", "produce food"]}, "answerKey": "D"}
{"id": "36f1ceeecde7abf99dab635239e12442", "question": "For many males hair is a concern as they get older, it begins to what, causing a receding hairline?", "question_concept": "hair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["thin out", "grow in ear", "fall out", "bulge", "composted"]}, "answerKey": "C"}
{"id": "e3c9e83c0c62d842de2dfe229f5e6d41", "question": "What happens someone who is bad play poker?", "question_concept": "play poker", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["think", "ante up", "drink", "win money", "losing money"]}, "answerKey": "E"}
{"id": "c0e4d0118c9cdfe2edc49ef954572b31", "question": "John loved his snake.  It was the only ting he loved. He hated everyone else and was abrasive to most people, but he loved his snake.   How might you describe the snake?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sun itself", "tropical forest", "pet", "rude", "sharp"]}, "answerKey": "C"}
{"id": "4423c006f2a43f222d4c4e97360c25d3", "question": "The fresh herbs, flowers, and vegetables will shrivel up if people don't do this?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water plants", "believe in god", "drive to the nearest pool", "speaking english", "raise children"]}, "answerKey": "A"}
{"id": "9382bc51ba092f55a494eff8615899de", "question": "I picked from an apple tree outside of Fort Wayne, where am I?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["woods", "illinois", "indiana", "washington state", "tampa"]}, "answerKey": "C"}
{"id": "dec1c42628a7448aa364cdada6e82f98", "question": "The janitor never had much to clean after services, but there was still always a paper or two to pick up where?", "question_concept": "paper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["synagogue", "front porch", "classroom", "obesity", "grocery store"]}, "answerKey": "A"}
{"id": "07ea8ff6ee916f2bf9aceab1e19ff99a", "question": "If you're celebrating with too many cocktails what may you have in the morning?", "question_concept": "celebrating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drunkenness", "have fun", "headache", "hang over", "intimacy"]}, "answerKey": "D"}
{"id": "a328285c6212c899e335c45db3c49ffd", "question": "Danny found an old film in a sealed what?", "question_concept": "film", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clingfilm", "disneyland", "cave", "cabinet", "movie"]}, "answerKey": "D"}
{"id": "e248968fec422e1fab0f0561fedff76e", "question": "Where are you likely to find much more than a drop of blood on the floor?", "question_concept": "drop of blood", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["crime scene", "vein", "blood bank", "slaughter house", "needle"]}, "answerKey": "D"}
{"id": "2067720531fc03c017af941cec2f6f40", "question": "Where is the first place someone leaving the planet ends up?", "question_concept": "planet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pay debts", "galaxy", "outer space", "orbit", "universe"]}, "answerKey": "C"}
{"id": "70d3ebc00b165d9d08f9491a1dd85034", "question": "The town house went right to the curb, a slot effectively made a mailbox of the what?", "question_concept": "mailbox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment building", "front door", "back door", "street corner", "porch"]}, "answerKey": "B"}
{"id": "41bab71fea3fa04e5a4e10a2f86996df", "question": "The architect thought that a mezzanine would look good, but the planning committee rejected it.  They told the architect that they felt it was a potential hazard given the ages of the people who would be using it.  What might they be designing?", "question_concept": "mezzanine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["actors", "theater", "concert hall", "floors", "school"]}, "answerKey": "E"}
{"id": "e18dd9ffc7b7934c39f2b5e9dee5a8c2", "question": "The person wasn't bothered by the weather, she had remembered to bring her what?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["read book", "own house", "apartment", "more rice", "warm coat"]}, "answerKey": "E"}
{"id": "449de58e919975867255218484a9fc89", "question": "If you want to learn about the world and understand the real reasons behind cultural norms and mores, you have achieved a sense of what?", "question_concept": "learning about world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enlightenment", "open mind", "confusion", "smartness", "anger"]}, "answerKey": "A"}
{"id": "9698232e3599157431c9dc8f2fe179cd", "question": "What is the hopeful result of going to see a play?", "question_concept": "going to play", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sit", "being entertained", "jobless", "meet", "laugh"]}, "answerKey": "B"}
{"id": "0b5d0c3bafbe06dd5334c20cd8ea7fe2", "question": "A person would join a trade school for finding information related to what?", "question_concept": "finding information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ulcers", "degree", "understanding of", "gaining knowledge", "happiness"]}, "answerKey": "D"}
{"id": "7fe53bf68ec57a52a508611acf5b279e", "question": "Joan was a baby, so there were many things she couldn't do, which caused problems for her parents.  Name one thing that makes raising a baby difficult.", "question_concept": "baby", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arrive early", "learn to walk", "boy or girl", "bring joy", "talk nonsense"]}, "answerKey": "E"}
{"id": "68c41ec8415eab50620eb9ecf6f35a6a", "question": "Where would you put some ham if you want to cook it?", "question_concept": "ham", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hamshackle", "pizza", "fridge", "refrigerator", "part of meal"]}, "answerKey": "B"}
{"id": "6c4b2c93a4bdafb6cbf2b2ef2439b06f", "question": "Running errands with screaming kids will likely cause what?", "question_concept": "running errands", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["efficiency", "insanity", "aggravation", "tiredness", "stress"]}, "answerKey": "C"}
{"id": "51e2da7396ab7045533e885dbb98a424", "question": "Sam wasn't lying, but he left out important details. He was being what?", "question_concept": "lying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dishonesty", "deceitful", "imagination", "deceptive", "poker face"]}, "answerKey": "B"}
{"id": "3f6157968fcf50d257ec3d8c729b7443", "question": "what does someone have that causes them committing murder?", "question_concept": "committing murder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["problems", "distress", "fear", "go to jail", "killer"]}, "answerKey": "A"}
{"id": "4768aa28fa14569d830f8947565296c1", "question": "What kind of place has a leader?", "question_concept": "leader", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["army", "battle", "wildlife", "country", "organization"]}, "answerKey": "D"}
{"id": "5516b1c93f94aaa0bf9a4c7b124788d4", "question": "How is a person likely to communicatewith others?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["say words", "meet friends", "open mouth", "thank god", "die of cancer"]}, "answerKey": "A"}
{"id": "96ea2c3174229c4a6a0e2ffaed2df378", "question": "Where may you be if you're buying pork chops at a corner shop?", "question_concept": "corner shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["england", "town", "desert", "kentucky", "iowa"]}, "answerKey": "E"}
{"id": "7905b9f4ba503b0ce13b576808e99c42", "question": "Where is a well used toy car likely to be found?", "question_concept": "toy car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["child's room", "boy's bedroom", "own home", "toy store", "house"]}, "answerKey": "A"}
{"id": "e0a7d1df3ce14b27888e785e6636d5f0", "question": "Where can fisherman store their rods when on a fishing trip?", "question_concept": "rod", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hardware store", "engine", "fishing camp", "lake", "sporting goods store"]}, "answerKey": "C"}
{"id": "3eb397b96b6c3a245c81ab30205943f1", "question": "Danny is having fun just dancing and singing with his friends. He wasn't concerned with things that weren't fun. For him having fun is the same as what?", "question_concept": "having fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["injuries", "smiling", "being happy", "glee", "jump"]}, "answerKey": "C"}
{"id": "536c9af0fae0aa75b32874dfcac66353", "question": "Where would you find an office worker gossiping with their colleagues?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water cooler", "space shuttle", "baby shower", "bus stop", "family"]}, "answerKey": "A"}
{"id": "dc36293f603cf230f8059fc6f2e5660d", "question": "Where would you put nails if they are already packaged?", "question_concept": "nails", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pocket", "container", "cabinet", "jar", "store"]}, "answerKey": "C"}
{"id": "1510f5183095466e4fe41b82501a9dd0", "question": "What is a person who is good at sports considered?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lazy", "own house", "talented", "affluent", "reproduce"]}, "answerKey": "C"}
{"id": "1fcc547e4e6813afc1a66717248d6c62", "question": "The man acted ridiculous at the funeral, what attitude should he have taken?", "question_concept": "ridiculous", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["straightforward", "serious", "solemn", "somber", "funny"]}, "answerKey": "C"}
{"id": "68a911b64dc943b5f81c0f8dec7faed7", "question": "The pencil sharpener was broken in the classroom, where did the teacher recommend the student go?", "question_concept": "pencil sharpener", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home", "library", "stationery store", "cabinet", "desk drawer"]}, "answerKey": "B"}
{"id": "92f423de9a556a66c3eb73e9ddf9399a", "question": "Where does a child likely sit at a desk?", "question_concept": "desk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["furniture store", "schoolroom", "patio", "office building", "library"]}, "answerKey": "B"}
{"id": "1cd94405124031e8681cd12bd25e2d61", "question": "He was trying to procreate with many individuals, this led to a what?", "question_concept": "procreate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["moaning", "die", "kiss", "std", "sanity"]}, "answerKey": "D"}
{"id": "64ab884bd870f6f68146636b4cce921c", "question": "What does playing soccer and winning lead to?", "question_concept": "playing soccer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["excitement", "getting tired", "overtime", "anger", "fights"]}, "answerKey": "A"}
{"id": "66275550d64d16339c944e6a6d63eb5b", "question": "What attraction is sometimes so large that you need a map to find your way around?", "question_concept": "map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["amusement park", "truck stop", "mcdonalds", "backpack", "classroom"]}, "answerKey": "A"}
{"id": "9b26329d74a6159ab9af4f899303de39", "question": "If my husband never helps me doing housework, what might that lead to?", "question_concept": "doing housework", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boredom", "arguments", "headache", "exhaustion", "park"]}, "answerKey": "B"}
{"id": "f74b7f268d3c190a13f99ede6d2359e1", "question": "The advertisement came in the form of a pop-up, where did it appear?", "question_concept": "advertisement", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["web page", "store", "la ville", "bus", "email"]}, "answerKey": "A"}
{"id": "22458fdcead20e2def0df0d92d5806f6", "question": "WHere do people live?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment", "eat cake", "bus depot", "football stadium", "surface of earth"]}, "answerKey": "E"}
{"id": "f7b96f195a7adfe0c74924a165cfd055", "question": "People are what when you're a stranger?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["train", "strange", "human", "stupid", "dangerous"]}, "answerKey": "B"}
{"id": "9b631734e72a0e559da153492c1e7894", "question": "The juror was quite bored and zoning out but wanted to convey he was hearing testimony, so he just sat there doing what?", "question_concept": "hearing testimony", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take notes", "nodding", "change of heart", "writing down", "listening"]}, "answerKey": "B"}
{"id": "caccaa51ee960a92d44e5b949fc35a66", "question": "They wanted to try blowfish, so they went to get some where?", "question_concept": "blowfish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["atlantic ocean", "books", "france", "aquarium", "fish market"]}, "answerKey": "E"}
{"id": "def936fda9f6ccee01f57c0f804fabd0", "question": "When a main artery is used to expedite travel what would it be referred to as?", "question_concept": "main artery", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neck", "busy city", "own brain", "thruway", "food"]}, "answerKey": "D"}
{"id": "761b0f6c68b1540949b70f76a9e67c78", "question": "If someone rules the universe of what are they in charge?", "question_concept": "rule", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "football game", "everything", "text book", "lawbook"]}, "answerKey": "C"}
{"id": "8c11546468a2595b29a1297e73334fc4", "question": "The butt was bare, and Sam couldn't stop staring at it.  It was very what?", "question_concept": "bare", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["full", "ample", "covered", "bareword", "ample"]}, "answerKey": "B"}
{"id": "a5dcac512870e79f5aa2b22dbd662404", "question": "Where can many stores with clothing be found?", "question_concept": "clothing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shop", "mall", "department store", "drawer", "library"]}, "answerKey": "B"}
{"id": "870b07a1c5af2e956673a9680da99852", "question": "After working on the car, what did it end up doing?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["going too fast", "last several years", "honk the horn", "go fast", "start running"]}, "answerKey": "E"}
{"id": "f48528156632b9c5b18af9ce2095509b", "question": "When an elderly person needs help performing daily tasks, who might they call?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["creativity", "hatred", "caregiver", "own house", "much money"]}, "answerKey": "C"}
{"id": "5496c7293f653120e5a5213db2d7b103", "question": "Where is beer drank by people watching sticks and pucks?", "question_concept": "beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bottle", "refrigerator", "hockey game", "casino", "bar"]}, "answerKey": "C"}
{"id": "9d97e2bb458d93a8bafe4380b08727e3", "question": "Where is there a telephone book in almost every room?", "question_concept": "telephone book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["at hotel", "house", "library", "bedsit", "closet"]}, "answerKey": "A"}
{"id": "26d7d59ef7b9f2e0c2d47419fa5bca91", "question": "Where might you see a green field while driving?", "question_concept": "field", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kansas", "meadow", "farmland", "countryside", "rural area"]}, "answerKey": "D"}
{"id": "c6f10fd07348bf2cf5488b0d9f38d806", "question": "Some people got escorted out of the library, they were probably what?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["state facts", "talking loudly", "making money", "amount to nothing", "believe in god"]}, "answerKey": "B"}
{"id": "8ebf9d24719649a0b041aea02a6e46af", "question": "If there is a pond with trees around it, where it it likely located?", "question_concept": "pond", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ground", "bathroom", "forest", "countryside", "rural area"]}, "answerKey": "C"}
{"id": "c961578f4c5768b67b843e5d2ce18452", "question": "Blowfish require what specific thing to live?", "question_concept": "blowfish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sea water", "hatred", "fish market", "body of water", "jungle"]}, "answerKey": "A"}
{"id": "cce1b59f7c4f540a84a1a7d6d88548c4", "question": "What is the least likely immediate side effect of eating hamburger?", "question_concept": "eating hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nausea", "death", "illness", "health problems", "gain weight"]}, "answerKey": "B"}
{"id": "60848ce50295fc745756fbe960e78b88", "question": "What would I be doing while going to work and walking?", "question_concept": "going to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listen to radio", "solve problems", "driving", "walk", "being late"]}, "answerKey": "A"}
{"id": "3fdc0c422c524c994b9911a17f1f1834", "question": "A showroom feature washers and refrigerators, where is this showroom located?", "question_concept": "showroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appliance store", "vegas", "electronics store", "car dealership", "kitchen"]}, "answerKey": "A"}
{"id": "cc8eac9956f645533b8d7b99702e3507", "question": "The man often made smart remarks, like that any restaurant is a mexican restaurant where?", "question_concept": "mexican restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "mexica", "san diego", "spain", "mexico"]}, "answerKey": "E"}
{"id": "c0e7fa3e39a2d9af2c323416015729dc", "question": "I am looking for honey right from the source, where should I look?", "question_concept": "honey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["last all night", "beehive", "farmer's market", "jar", "honeyful"]}, "answerKey": "B"}
{"id": "335b51bd3a8ada014bbe6754dcbd425f", "question": "Where are there likely to be a variety of flats to choose from?", "question_concept": "flat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["london", "apartment building", "city", "falling down", "town"]}, "answerKey": "C"}
{"id": "c7327a1a7d12b6cc0740fc9446270e02", "question": "A weasel has a thin body and short legs to easier burrow after prey in a what?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tree", "mulberry bush", "chicken coop", "viking ship", "rabbit warren"]}, "answerKey": "E"}
{"id": "2729d8502208c25d8e9293cd4e8ecbb5", "question": "What can disease destroy?", "question_concept": "disease", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rug", "third world country", "human body", "hospital", "building"]}, "answerKey": "C"}
{"id": "7ea57ee4580042b0a6a40479c8ace3e4", "question": "What does a person from Avalon live in?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pain", "meaningful work", "english house", "cotton candy", "headache"]}, "answerKey": "C"}
{"id": "65432eb6e617514d863a465f38865fde", "question": "Where is one likely to find a fan for their stove?", "question_concept": "fan", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appliance store", "sports stadium", "dress emporium", "hot room", "football stadium"]}, "answerKey": "A"}
{"id": "316a8dee8a4dde7d95cf503a715104be", "question": "Jodie felt a tightness in her chest. She was worried but didn't want to go to the hospital. Where might she go instead?", "question_concept": "chest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["istanbul", "concert", "bedroom", "antique shop", "human being"]}, "answerKey": "C"}
{"id": "520972425aed0e532fa28a91c9b55b30", "question": "If you're buying beer for a float trip what are you preparing to do?", "question_concept": "buying beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get arrested", "have fun", "get sick", "spend money", "stupidity"]}, "answerKey": "B"}
{"id": "4d67cdb4ba1b0058e383c212303a9f4e", "question": "Piece of land in Canada where you can find marmot?", "question_concept": "marmot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["north america", "united states", "vancouver island", "american", "cage"]}, "answerKey": "C"}
{"id": "95d1d968ee66b6054cbb16b58a7c6455", "question": "The surgeon's clients had begun to reduce, it seemed girls no longer want to what?", "question_concept": "reduce", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reduction", "make larger", "augment", "gain weight", "expand"]}, "answerKey": "C"}
{"id": "c43b60be106662de1863097ee3ddb4d2", "question": "While waiting for this appointment, people often read magazines.", "question_concept": "magazines", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["doctor", "train station", "newsagent", "market", "table"]}, "answerKey": "A"}
{"id": "456f2fb41cac8c028dcfe2f48637e473", "question": "Where would you find a fox that is made up?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["storybook", "woods", "hen house", "natural habitat", "back yard"]}, "answerKey": "A"}
{"id": "a5d853d1c2fb3ef160218fb91110fbe5", "question": "In basic training they build you up only to do what, all in hopes of building you up even stronger the next time?", "question_concept": "build", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["destroying", "tear down", "raze", "mutilate", "demolition"]}, "answerKey": "B"}
{"id": "3df1b88da6a90c9526be2c8a6cc736dc", "question": "Billy saw a dog running from him and did his best to get away from it.  The leaped up to where the dog couldn't reach and was stuck.  Where might he have been trapped?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kennel", "table", "porch", "backyard", "park"]}, "answerKey": "B"}
{"id": "f912bcd7479b76db9b1c57a612b90f00", "question": "John and Judy were parents.  They had two wonderful kids who weren't always well behaved.  They were light tough, though.  They felt it was a parent's job to do what?", "question_concept": "parents", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["control children", "guide children", "speak freely", "cry", "understand children"]}, "answerKey": "B"}
{"id": "94f34cc1e6aa9eefe06563cce8225658", "question": "What are you playing if you're fiddling on a violin?", "question_concept": "fiddling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bluegrass music", "make music", "drop", "string instrument", "troubles"]}, "answerKey": "A"}
{"id": "bb503ece4eac41dfe608a1dcb654e6bf", "question": "If somebody buys something and gives it to me as a free gift, what is the cost status of the gift?", "question_concept": "free", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["deadly", "imprisoned", "paid for", "expensive", "in prison"]}, "answerKey": "C"}
{"id": "5502dc807d4921679ae1abd0dc9570d6", "question": "Why does someone flirt with many people at once?", "question_concept": "flirt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have sex", "get laid", "were lonely", "attract attention", "dance"]}, "answerKey": "D"}
{"id": "a7e3de0719fe30e7048f67426e29fdd1", "question": "James tore the antenna off of his boat due to bad reception as he was crossing the channel from France.  Where was he going?", "question_concept": "channel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["river", "television", "india", "england", "europe"]}, "answerKey": "D"}
{"id": "d6107d454181b701ddcaa449a1e422a3", "question": "Why would a band be performing when there are no people nearby?", "question_concept": "band", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["record album", "play music", "hold concert", "blaring", "practice"]}, "answerKey": "A"}
{"id": "ab2eb930b29bb6d5e94a6cd3b04ba01e", "question": "The dogs were protecting their own when they decided to what the bad man?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bad breath", "defend", "run fast", "ocean", "attack"]}, "answerKey": "E"}
{"id": "92869fc0be5dc45f407700692ffd80a0", "question": "What is used to grind wheat for bread?", "question_concept": "wheat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["farmer's field", "countryside", "cereal packets", "bread", "mill"]}, "answerKey": "E"}
{"id": "6a0177586d506cb7b741f4207b428e42", "question": "If you have a large satchel with you when you fly you'll be asked to store it where?", "question_concept": "satchel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airport", "luggage compartment", "with the pilot", "room", "clothing store"]}, "answerKey": "B"}
{"id": "584188da9a429f1bc319abda5e5c7a76", "question": "Where would someone keep their nylon leggings?", "question_concept": "nylon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stockings", "rope", "car", "clothing", "drawer"]}, "answerKey": "E"}
{"id": "e480d4a672af0194e0a6ccdb8c37499b", "question": "If you spend a long time running after a ball how are you likely to feel?", "question_concept": "running after ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["laughter", "sweating", "embarrassed", "breathing heavily", "tiredness"]}, "answerKey": "E"}
{"id": "275c859994f7d3acd3c8863be591ab2c", "question": "When you need to rest it's often because you have been doing what?", "question_concept": "rest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["action", "sleep", "sleeping", "in motion", "using energy"]}, "answerKey": "E"}
{"id": "32758ab86d888be680845b0dfe7de35e", "question": "Boredom and hunger led to a wandering waste of time and a cart full of unhealthy snacks during her trip to where?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new moon", "play cards", "read book", "see art", "grocery shop"]}, "answerKey": "E"}
{"id": "69335eb9bc5b7b5df840c38a086bf8b2", "question": "He was beginning to worry they wouldn't get on the ride before closing, they had been standing in queue for a long what?", "question_concept": "standing in queue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["frustration", "waiting", "hair", "time", "patience"]}, "answerKey": "D"}
{"id": "4396cb65629672723c7b184424e139bb", "question": "This is an unavoidable physiological consequence of running.  What is it?", "question_concept": "running", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["breathlessness", "increased heart rate", "falling down", "muscle bulk", "calluses"]}, "answerKey": "B"}
{"id": "2a58e81a9c4ce095d099e0d785fc2da4", "question": "Sometimes a person has a fear of water or a dislike of being wet, it is still important to make sure they are having a bath why?", "question_concept": "having bath", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["flooding", "drowning", "wet skin", "get wet", "rash"]}, "answerKey": "E"}
{"id": "07f108d5321a66f460685f5c7499ecb2", "question": "Where would there be an auditorium with only a single person speaking?", "question_concept": "auditorium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lights", "crowd", "university campus", "theater", "park"]}, "answerKey": "C"}
{"id": "69bef3eb55463d040bdf98e2c97bfe1f", "question": "To get out of there the person had to keep on walking, they had to keep on what?", "question_concept": "walking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["moving forward", "locomotion", "blisters", "rollerskate", "exercise"]}, "answerKey": "A"}
{"id": "912676495cceefadccbbf8c604486f97", "question": "What very large group of western citizens has bees everywhere?", "question_concept": "bee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["united states", "space station", "trash can", "field of flowers", "bouquet of flowers"]}, "answerKey": "A"}
{"id": "bdf92566f14599f1606109677206001f", "question": "Miss Grady took a stick from Bob because he was playing with it during class.  She wanted to make sure that he couldn't get to it so she put it where?", "question_concept": "glue stick", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk drawer", "kitchen drawer", "classroom", "pocket", "office"]}, "answerKey": "A"}
{"id": "0df042743128b57e874bd5d79b7aae7a", "question": "How does a person begin reproducing?", "question_concept": "reproducing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["genetic mutation", "have sex", "kiss", "flirting", "going on a date"]}, "answerKey": "B"}
{"id": "866ef7266d34c11e5a1b3df49fab96a4", "question": "Joe and Jill didn't want their children to be sedentary.  They might limit the time they children spend doing what?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play sports", "throw things", "reading", "watch tv", "play with toys"]}, "answerKey": "D"}
{"id": "67ffcb4c3f2c6a1155e356f8a15ed250", "question": "They were making sauerkraut, the instructor explained the liquid should be above the cabbage in the what?", "question_concept": "liquid", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jar", "drinking glass", "pot", "container", "can"]}, "answerKey": "A"}
{"id": "87a133afae5d9d29d634f3384f28ef24", "question": "From where would you normally take a cup when you're about to get a drink?", "question_concept": "cup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dishwasher", "water fountain", "sand box", "toilet", "kitchen cabinet"]}, "answerKey": "E"}
{"id": "4779be55f47a301debfc472e4fc2c7b6", "question": "What are you using if there are speakers strapped on your ears?", "question_concept": "speakers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take it all in", "headphones", "desktop", "conference", "concert"]}, "answerKey": "B"}
{"id": "7a28d31e66d870370642de3be47b9ef9", "question": "Because of his anger he couldn't clearly explain or what?", "question_concept": "anger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cool off", "write letter", "get mad", "illustrate point", "destroy enemy"]}, "answerKey": "D"}
{"id": "042898e0c71adac5d123aaa6221c9754", "question": "Where is likely to not just have a kosher restaurant?", "question_concept": "kosher restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jerusalem", "jewish neighborhoods", "dining in", "new york city", "dining"]}, "answerKey": "D"}
{"id": "93bbaccb1c46d22124a846b8514de5bc", "question": "The bald eagle flew from Mount St Helen's to the Puget Sound and all over what?", "question_concept": "bald eagle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["washington state", "utah", "pacific northwest", "northern california", "the desert"]}, "answerKey": "A"}
{"id": "ef889edd1b57d8d0c81e43f73c98c8e9", "question": "Where could you get some knives if you are planning to bring them outside with you?", "question_concept": "knives", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sporting goods store", "backpack", "kitchen", "sharp edges", "dog house"]}, "answerKey": "C"}
{"id": "f4bb8ecacb9ce89e040f5f76bc79afb3", "question": "How can people fulfill their own calorie requirements?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["compete with each other", "feed themselves", "feel lonely", "talk to each other", "ask a doctor"]}, "answerKey": "B"}
{"id": "ec2e18fd8c18a4ebe5a091e0c8b94462", "question": "What does a stove do to the place that it's in?", "question_concept": "stove", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cool house", "warm room", "gas or electric", "burn child", "brown meat"]}, "answerKey": "B"}
{"id": "07b51b231a9d6a143d8a73e69121e1b1", "question": "What is the best way to begin going into trance?", "question_concept": "going into trance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["religious experience", "closed eyes", "loss of control", "sleep", "hallucination"]}, "answerKey": "B"}
{"id": "e1744fc698cffb574e5cf4b29a81ce76", "question": "A computer user working on an important work assignment is located in what structure?", "question_concept": "computer user", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office building", "internet cafe", "house", "school", "internet cafe"]}, "answerKey": "A"}
{"id": "27604394ccee83e089f9ffae1883cf07", "question": "The music was festive but why are the horses dancing in circles", "question_concept": "music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["carnival", "night club", "theatre", "opera", "ringmaster"]}, "answerKey": "A"}
{"id": "1272e693cf9152e7ac71095c643676b5", "question": "In the building where James worked there was a small mezzanine in the auditorium to make more space for seats.  Where might James work?", "question_concept": "mezzanine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["theater", "floors", "concert hall", "education", "school"]}, "answerKey": "A"}
{"id": "7bff23f6c12e9136f0961514bebb8cd3", "question": "If you aren't well rested and it's a rainy day what might you do?", "question_concept": "rainy day", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sleep", "write", "make bread", "stay in bed", "enjoy film"]}, "answerKey": "D"}
{"id": "20ae70b9b157b298569cd761787833e7", "question": "Where would you have a stove if you don't live in a detached dwelling?", "question_concept": "stove", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tent", "car", "living room", "friend's house", "apartment"]}, "answerKey": "E"}
{"id": "bdd29d7c12e3d795b78ffc048631e7e7", "question": "What kind of place has a revolving door and has things to buy in it?", "question_concept": "revolving door", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new york", "public place", "bank", "mall", "supermarket door"]}, "answerKey": "D"}
{"id": "cc1a547bdfdcc95e4d632453af14bc96", "question": "Where can books be read?", "question_concept": "books", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabinet", "backpack", "table", "shelf", "sink"]}, "answerKey": "C"}
{"id": "896b25dc41f84357add1c798d4a96cd8", "question": "Where is seaweed usually found alive?", "question_concept": "seaweed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "found in ocean", "water", "found in sea", "beach"]}, "answerKey": "C"}
{"id": "1ca3cd9475d7e9da2ddb74911ee2bb68", "question": "If a lizard is fed by people every day, what has happened to it?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["documentary", "costa rica", "garden", "encouragement", "captivity"]}, "answerKey": "E"}
{"id": "129ec46cc2541b73198d774ee632c8d7", "question": "What will happen to someone if his or her spirits cannot elevate?", "question_concept": "elevate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sadden", "demote", "depress", "drop", "decrease"]}, "answerKey": "C"}
{"id": "0e5c7c0cec5b693e52f74f5f879d84fb", "question": "If you wanted a license to catch crabs, what government office would you go to?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["most offices", "fish department", "fancy restaurant", "government submarine", "chesapeake bay"]}, "answerKey": "B"}
{"id": "af035b75b6f7a1927b1648963f281c5e", "question": "What furniture will you normally find near a side chair?", "question_concept": "side chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bedroom", "table", "wheel barrow", "building", "office"]}, "answerKey": "B"}
{"id": "32d5b7fcae24f0d4871cfb219c5a4b47", "question": "Metal is used to make what?", "question_concept": "metal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["junkyard", "ore", "instruments", "metal fabrication shop", "bowls"]}, "answerKey": "C"}
{"id": "87505da761eaa5c3c4703d02a12d46bc", "question": "What is the word added to Manchester that signifies what county it is in?", "question_concept": "manchester", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["england", "united kingdome", "lancashire", "greater manchester", "cheshire"]}, "answerKey": "D"}
{"id": "ef3d5d35128678937c36438466e0fc93", "question": "The program kept getting errors, the amateur end user began to what?", "question_concept": "program", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get mad", "compile", "debug", "write code", "get frustrated"]}, "answerKey": "E"}
{"id": "4f1d8007b446b0e10f07fd63cbd31b6f", "question": "John knew that the sun produced a massive amount of energy in two forms.  If you were on the surface of the sun, what would kill you first?", "question_concept": "sun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "heat", "life on earth", "wrinkles", "light"]}, "answerKey": "B"}
{"id": "4c30d5eed4137cba89747510973f37a3", "question": "Lawyers often talk in front of an audience where?", "question_concept": "lawyers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "courtroom", "office building", "press charges", "theatre"]}, "answerKey": "B"}
{"id": "515834727e23e30ab7c8fe5ba7e9a765", "question": "James bought a new set of tire chains and put them somewhere he could find them.  Where would he put them?", "question_concept": "chain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gear shift", "garage", "kitchen", "jewelry store", "hardware store"]}, "answerKey": "B"}
{"id": "34ec6393db5a01f689c11fac153e31c1", "question": "If I wanted to eat something that is made from plants and needs to be washed, what would it be?", "question_concept": "plants", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["roots", "millions of cells", "see work", "leaves to gather light", "flowers on"]}, "answerKey": "A"}
{"id": "0f0e339412f719a019bf373e6daf2530", "question": "Ficus can be planted in a yard to make summer more bearable, what sort of areas do they create?", "question_concept": "ficus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shady places", "screened porch", "pots", "ceramics", "clay pot"]}, "answerKey": "A"}
{"id": "489a082aab43dd1a53f3f1f89c2365ed", "question": "Children's behavior is a direct reflection of their what?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["parents", "old people", "play ball", "many adults", "grown ups"]}, "answerKey": "A"}
{"id": "7c45033e9fd9f1a759923971b14390ed", "question": "Most people who are family like to greet each other with a what?", "question_concept": "most people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartments", "listen to music", "have friends", "know what ophiolites", "hug"]}, "answerKey": "E"}
{"id": "061f326d2a87a10da6316b55bd5522bd", "question": "John bought a new water hose.  But he found his old one near his car.  Where did he find the old one?", "question_concept": "hose", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garden shed", "hardware store", "greenhouse", "garage", "in a van"]}, "answerKey": "D"}
{"id": "d747c4e463b80bfcc49b874063f9fae1", "question": "Where is a control room needed to prevent wide spread disaster?", "question_concept": "control room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airbase", "prison", "mill", "nuclear plant", "recording studio"]}, "answerKey": "D"}
{"id": "df3d27338bcf86b341b8b02d4309daf5", "question": "Where do you keep your pizza slice before you eat it?", "question_concept": "pizza", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "plate", "restaurant", "oven", "popular"]}, "answerKey": "B"}
{"id": "db63bf66a8bfd16e5103cbdd350f5202", "question": "Everybody was changing into costumes in the dressing room, it was almost time to take the what stage?", "question_concept": "dressing room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["theater", "train", "bathhouse", "dwelling", "actors and actresses"]}, "answerKey": "A"}
{"id": "f8a9208665a4f2d64986940456b4b964", "question": "The homeowner frowned at the price of gas, what did he have to do later?", "question_concept": "homeowner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["own home", "mail property tax payments", "board windows", "cut grass", "receive mail"]}, "answerKey": "D"}
{"id": "1bf4c6b5bd870b1a079106e1e97e5d09", "question": "A thoroughfare meandered through fields and woods, where was it passing though?", "question_concept": "thoroughfare", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["move about", "city", "country", "town", "new york city"]}, "answerKey": "C"}
{"id": "c1c73ef0ff662a76cd42c3500240974a", "question": "If I want a new ottoman, where should I go?", "question_concept": "ottoman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["furniture store", "parlor", "turkey", "living room", "den"]}, "answerKey": "A"}
{"id": "aefa60233f3c5c4966f8ac22e901a1c7", "question": "Sean was leaving work and took the roadway that led to his what?", "question_concept": "roadway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neighborhood", "city", "fate", "countryside", "maps"]}, "answerKey": "A"}
{"id": "9221962ed3a6094e5c8f33785ce048cd", "question": "What can you use to get a jellyfish?", "question_concept": "jellyfish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["adriatic sea", "mediterranean sea", "hand", "see", "atlantic ocean"]}, "answerKey": "C"}
{"id": "8c8052980e357545398d27d1c3c832d8", "question": "What has a shelf that does not allow you to see what is inside of it?", "question_concept": "shelf", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chest of drawers", "stove", "hold alcohol", "bookcase", "grocery store"]}, "answerKey": "A"}
{"id": "418913999c665ae9527fd14a6132da39", "question": "What will likely happen after stabbing to death a person?", "question_concept": "stabbing to death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gruesome", "being arrested", "pool of blood", "mess", "grisly"]}, "answerKey": "B"}
{"id": "2634468d21fa33a88cefe28a5d613f59", "question": "The boat passenger was explaining his fear of blowfish, but the captain figured he meant piranhas since they were on a river in the what?", "question_concept": "blowfish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cuba", "styx", "atlantic ocean", "france", "jungle"]}, "answerKey": "E"}
{"id": "66bfb6e209c94e6be5b0d04b0c7e2064", "question": "Where could you find only a few office?", "question_concept": "office", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["skyscraper", "new york", "school building", "city", "work"]}, "answerKey": "C"}
{"id": "3163910d665c139a1f6f07d85803baba", "question": "Where can I go to be a religious gentleman?", "question_concept": "gentleman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["club", "restaurant", "university", "pub", "church"]}, "answerKey": "E"}
{"id": "0e52659484f2f6d763cf0d38d4c5999d", "question": "I want to see a prepared slide up close, what would I use to help?", "question_concept": "lens", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["microscope", "abbreviate", "glasses", "camera", "telescope"]}, "answerKey": "A"}
{"id": "167d2cfa04bfaea0e0b5bac3598d5769", "question": "Where can you buy a magazine, paper or gum?", "question_concept": "magazine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bank", "rack", "bed", "newsstand", "bus depot"]}, "answerKey": "D"}
{"id": "39572e0ba1db51fa74f7fc2d90c5ec7f", "question": "Where would you get some wood if you do not have any?", "question_concept": "wood", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["carpet", "boat", "river", "lumberyard", "synagogue"]}, "answerKey": "D"}
{"id": "2a32b1e541b1daae04690d0d3a4b3310", "question": "The pitcher felt stress and tension on the mound, what did he feel like?", "question_concept": "mound", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desert", "baseball field", "hell", "baseball diamond", "baseball stadium"]}, "answerKey": "C"}
{"id": "71cbfeb995b06b21e890c91040722252", "question": "What negative effect can competing in a chess game on a cold day have?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enemies", "perform better", "sweat", "tension", "frostbite"]}, "answerKey": "D"}
{"id": "a15d564d0be6996251b5d523ac62db2a", "question": "Why is it hard for a young child to read a long book?", "question_concept": "book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["knowledge", "cover", "no pictures", "past", "many words"]}, "answerKey": "E"}
{"id": "6bd170c8d3d99d3c47b3e96427bacaeb", "question": "On a hot day what can you do to enjoy something cool and sweet?", "question_concept": "hot day", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dive", "cool off", "fresh cake", "go for swim", "eat ice cream"]}, "answerKey": "E"}
{"id": "7bc1198664b376f79d584725ad7f874b", "question": "What is likely to be found in a book that is not a foreword?", "question_concept": "foreword", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["last word", "conclusion", "ikea instructions", "afterword", "epilogue"]}, "answerKey": "E"}
{"id": "d6c002d46d9bfa466637cec4a134f332", "question": "How many hours are in a day?", "question_concept": "day", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["week", "bright", "night", "twenty four", "year"]}, "answerKey": "D"}
{"id": "8cb45b421375243e788cfc64bd77b051", "question": "Why is religion so hard to understand?", "question_concept": "religion", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["both positive and negative", "unknowable", "important to people", "ocean", "confusing"]}, "answerKey": "E"}
{"id": "d6ff2d749494d89e9c7a53f587c519f4", "question": "The couple explained they were having trouble communicating, it seemed every conversation took great what?", "question_concept": "communicating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["thinking", "effort", "laugh", "force", "medium"]}, "answerKey": "B"}
{"id": "6974d215428a974641c1df18678522f5", "question": "What would a person need to do if his or her captain dies at sea?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cross street", "have a party", "experience life", "cross road", "man crew"]}, "answerKey": "E"}
{"id": "b94a9764acff078b52a9cbae04661dc9", "question": "What do children require to grow up healthy?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["need care", "come home", "fast food", "watch television", "wash dishes"]}, "answerKey": "A"}
{"id": "80930e9df9ac4ad752749a54e7fc124f_1", "question": "I house outside the center of a community is said to be where?", "question_concept": "house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "subdivision", "newspaper", "residential area", "street"]}, "answerKey": "B"}
{"id": "3310b5b24f03d67179fababf9ae95144", "question": "The field general began to write out a letter to the king, he was told to send what when the enemy was near?", "question_concept": "letter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["syllable", "english alphabet", "word", "email", "invitation"]}, "answerKey": "C"}
{"id": "846bc47ced7119ad2ee19a8780d7fe18", "question": "What will you put on a pen to prevent it from drying out?", "question_concept": "pens", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["write sentences on paper", "ink in", "ink cartridges", "caps", "cling film"]}, "answerKey": "D"}
{"id": "fd5a34e94303d7fd343de2a8f36943d5", "question": "After climbing the mountains, the explored found the cave, what was the general goegraphy of the region he found it in?", "question_concept": "cave", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["west virginia", "kentucky", "desert", "sea", "rocky hills"]}, "answerKey": "E"}
{"id": "4e87db4771f2d6423034935446e3fff1", "question": "They dealt with combustible mixtures in their experiments, this is why they kept a fire extinguisher where?", "question_concept": "fire extinguisher", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospital", "chemistry lab", "most businesses", "classroom", "public building"]}, "answerKey": "B"}
{"id": "a585df0818180ce3c06f963a4c3c810a", "question": "If someone mean wanted to insult somebody by calling them a fruit, where is probably not the smartest place to do it?", "question_concept": "fruit", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gay bar", "market", "grocery store", "refrigerator", "container"]}, "answerKey": "A"}
{"id": "c9f7d07e6d363a99f5fadd68a4dfa35a", "question": "Where would you get a toothpick if you do not have any?", "question_concept": "toothpick", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["box", "grocery store", "eyes", "chewing", "mouth"]}, "answerKey": "B"}
{"id": "c7cb327fa4c0008efaa7741081a365d4", "question": "What would you be building if you designed a place for an annoying critter to stay?", "question_concept": "mosquitoes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spread disease", "swamp", "fly away", "cat condo", "bug campers"]}, "answerKey": "E"}
{"id": "c54ddc0f9d170ba65d9f4f2e0bb41d1c", "question": "The man working in the attic swatted away a bee, but soon the single bee was an entire what?", "question_concept": "bee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["swarm", "pack", "countryside", "soft drink", "field of flowers"]}, "answerKey": "A"}
{"id": "1729c737ff92cf558efecde2c6cafc5e", "question": "What do you need to wear when hiking?", "question_concept": "hiking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cast iron stomach", "physical exertion", "shin splints", "adventure", "fatigue"]}, "answerKey": "C"}
{"id": "19dfd55e967dacd6f5700a62c1e14eee", "question": "What type of store would have lots of sports equipment?", "question_concept": "sports equipment", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mall", "office supply store", "school", "sporting goods store", "sporting event"]}, "answerKey": "D"}
{"id": "b9bed83138901f4a45041b02c5b242c1", "question": "The business man was promoted recently, to celebrate he went where to buy an expensive wristwatch?", "question_concept": "wristwatch", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["case", "jewelry store", "shopping", "jewelery box", "hock"]}, "answerKey": "B"}
{"id": "********************************", "question": "How is a child eager to be going to play likely to get there?", "question_concept": "going to play", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slowly", "rush", "being entertained", "have fun", "enjoyment"]}, "answerKey": "B"}
{"id": "2af70107e04e61e3c7884bc743901c02", "question": "There's some new buying products designed to get you money if you have none. The first step is that it will show you how to declare what?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tax return", "bankruptcy", "pleasure", "debt", "spending money"]}, "answerKey": "B"}
{"id": "be2cb9c96069ac355a7ccef262743d14", "question": "Where can you buy a replacement ax handle?", "question_concept": "handle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bathroom", "hardware store", "water fountain", "grocery store", "fridge"]}, "answerKey": "B"}
{"id": "799e48ec7fb16415c8f82828c5761ed1", "question": "Is that person acting as silly as a clown?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["make mistakes", "ridiculous", "have no home", "mentally unhinged", "schizophrenia"]}, "answerKey": "B"}
{"id": "a5db1e9677af118deb8e4add8bc18db2", "question": "Which group of states is Louisiana part of?", "question_concept": "louisiana", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["deep south", "98 of world's crayfish", "united states", "gulf states", "bible belt"]}, "answerKey": "D"}
{"id": "28357ebf85f8bb82b6a3210c4397e0aa", "question": "Where would you put a plate immediately after eating from it?", "question_concept": "plate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen cupboard", "floor", "table", "dishwasher", "flea market"]}, "answerKey": "D"}
{"id": "7b95825a19d6930d6aed35c7c57a2d82", "question": "James couldn't get comfortable.  There was too much dirt.  He needed to clean out what?", "question_concept": "dirt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ground", "subway", "bank", "bed", "street"]}, "answerKey": "D"}
{"id": "6b270159bd402ddd498a38153f9d1efe", "question": "The rats were hiding in the house, where were they?", "question_concept": "rats", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sewers", "laboratory", "basement", "clinic", "cellar"]}, "answerKey": "E"}
{"id": "eae0e03773365064ce915603c7addc91", "question": "What do people do when they don't understand something?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ask questions", "experience joy", "believe in god", "talk to each other", "get sick"]}, "answerKey": "A"}
{"id": "a5ca7c89196e54938b5827814d0071d4", "question": "James saw a kite flying in the sky.  He traced the string back to its origin and found it.  Where did the string begin?", "question_concept": "kite", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["end of line", "hobby shop", "his hand", "toy store", "child's hand"]}, "answerKey": "E"}
{"id": "ffc3461d437a1c6c22d1c4f6439ebd9c", "question": "What rubber toy filled with helium will make a child happy?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["loved", "learn", "eat cake", "balloon", "become adult"]}, "answerKey": "D"}
{"id": "aa2dcd9bcce5e4445bd3bacbf0bb11d3", "question": "Where do people get beer after a bit of gambling?", "question_concept": "beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bottle", "grocery store", "casino", "spaceship", "hockey game"]}, "answerKey": "C"}
{"id": "6cc797ec148c1fc74592957a55bd0951", "question": "What can happen to you when you are using television and it is not interesting?", "question_concept": "using television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["turn off", "functions", "turning off", "entertainment", "fall asleep"]}, "answerKey": "E"}
{"id": "64dbe5cb840ef4f1d25f8b68db8d5fed", "question": "The business men left the discussion in the dressing room, now they just wanted to relax in the sauna of the what?", "question_concept": "dressing room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["brush hair", "theater", "house", "dwelling", "bathhouse"]}, "answerKey": "E"}
{"id": "a74753bf249c1cbcff632c5c16b0397b", "question": "Where is a likely place for an ivy plant?", "question_concept": "plant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["flower pot", "shelf", "windowsill", "outside", "sill"]}, "answerKey": "D"}
{"id": "9190efbd77fe10b989fcaae35e208a0f", "question": "Where has the newest baseball stadium?", "question_concept": "baseball stadium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["phoenix", "chicago", "antarctica", "san francisco", "urban areas"]}, "answerKey": "A"}
{"id": "ff0303db294a823d4138fb81a6ee6438", "question": "What type of residence has a ground floor with a stoop?", "question_concept": "ground floor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["brownstone", "hotel", "condominium", "entering building", "office building"]}, "answerKey": "A"}
{"id": "63963c9c15835d451aac2e1e0b116388", "question": "If the wood texture is not smooth it is what?", "question_concept": "wood", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gilded", "porous", "solid", "painted", "less dense than water"]}, "answerKey": "B"}
{"id": "cc8324b73ed9625e723ef041dfc77a37", "question": "What might happen if someone is not losing weight?", "question_concept": "losing weight", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["loose skin", "beauty", "miss universe", "death", "healthier"]}, "answerKey": "D"}
{"id": "684dbde19719e8224113433981d6e01e", "question": "Billy lived in the capital of his country, then he moved.  Where might he move to?", "question_concept": "capital", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["small town", "jail", "lower case", "contain governmental activities", "lowercase"]}, "answerKey": "A"}
{"id": "21450618657881d8c5af73691f3423a7_1", "question": "Making a schedule was easy to pick, the major called for knowledge that required a certain what?", "question_concept": "knowledge", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["color", "class", "meeting", "university", "encyclopedia"]}, "answerKey": "B"}
{"id": "8b94b61b604ec0d7508804033eec6d23", "question": "When getting in shape, this is something that does wonders?", "question_concept": "getting in shape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat more", "starve", "give up", "period of recovery", "jogging"]}, "answerKey": "E"}
{"id": "52ecf169febc95a7f5ccb048fc85857d", "question": "What could prevent a driving car from continuing to drive?", "question_concept": "driving car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["automobile accidents", "backache", "pollution", "smoke", "low fuel tank"]}, "answerKey": "A"}
{"id": "e408a5a031caec33782cb3b3a005eecc", "question": "Where do you store a large container?", "question_concept": "large container", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "factory", "juice", "hostel", "cabinet"]}, "answerKey": "E"}
{"id": "31bd05ba62a16ee35217224b98c6baea", "question": "What is a person likely to experience after they stop being married to a mean person?", "question_concept": "stopping being married to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["isolation", "grief", "happiness", "relief", "angry"]}, "answerKey": "C"}
{"id": "b4043bd1f65a8ad088e62042eca259c2", "question": "Despite the large crowds, how did the depressed man feel?", "question_concept": "crowd", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["small group", "alone", "solitary", "solitude", "panic"]}, "answerKey": "C"}
{"id": "4302e727e47f464511d4d04f22bed0d2", "question": "Where does a maid empty a trash can?", "question_concept": "trash can", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus stop", "corner", "hockey game", "motel", "alley"]}, "answerKey": "D"}
{"id": "f0d473701d52125dd055d23042de1b0d", "question": "The dog curled up for a nap, it was tuckered out because it had just been what?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["walked", "petted", "affection", "go outside", "scratch"]}, "answerKey": "A"}
{"id": "d35112a99ab3983fb51c3adae80bc2da", "question": "He used an umbrella while tanning, where was he likely?", "question_concept": "umbrella", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["waves", "seattle", "suitcase", "beach", "jacket closet"]}, "answerKey": "D"}
{"id": "661474a1a0c29dd7a243b284535ac934", "question": "What do the feathers look like on birds found in the rainforest?", "question_concept": "birds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pretty smart", "singing", "dark", "very colorful", "light"]}, "answerKey": "D"}
{"id": "6416dcdf9b8d7d2787f07e7426f86fe4", "question": "The ancient seafaring Norse tribesman brought pelts of weasel aboard his what?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rabbit warren", "used car lot", "chicken coop", "cruise", "viking ship"]}, "answerKey": "E"}
{"id": "0f54a1ee30a0034a3d2db1bfdef9ca85", "question": "What is the opposite of an area of elevation?", "question_concept": "elevation", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["disgust", "reduction", "depression", "demotion", "diminishment"]}, "answerKey": "C"}
{"id": "7850beb1209c41fabe385cbedc96a61a", "question": "What do singers need to do before a show?", "question_concept": "singers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["warm up", "use microphones", "clear throats", "create music", "sound beautiful"]}, "answerKey": "A"}
{"id": "cdb06b28b9c4e7ef7e880d1f096fd409", "question": "When a person with mental illness receives medication and therapy, what has happened?", "question_concept": "mental illness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cause irrational behaviour", "recur", "effectively treated", "managed", "cause suffering"]}, "answerKey": "C"}
{"id": "14309d9bd3c13d1c0efb625198f6304a", "question": "What type of feeling is performing for the first time likely to produce?", "question_concept": "performing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["action", "butterflies", "happiness", "a sense of calm", "anxiety"]}, "answerKey": "E"}
{"id": "a00276c6db928900772c0320aeff77c0", "question": "If someone is found to be committing murder, what did they do to someone?", "question_concept": "committing murder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["misery", "kill", "distress", "tickel", "go to jail"]}, "answerKey": "B"}
{"id": "4706be6e24f1fafd9ff9fe63583acffd", "question": "The computer was hooked up to the internet, what could it do as a result?", "question_concept": "computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["process information", "believe in god", "make decisions", "process information", "receive data"]}, "answerKey": "E"}
{"id": "ee8819b2da5453848c1cbb9d9c93403b", "question": "The planet Mercury is unsuitable for human life or what?", "question_concept": "mercury", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toxic", "uninhabitable", "mercury sulphide", "poisonous", "jupiter"]}, "answerKey": "B"}
{"id": "84ea43b967259814d939c62131f74df0", "question": "Seeing idea become reality was a dream of hers for a long time, but as the time came to get on stage she had more what?", "question_concept": "seeing idea become reality", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["build", "anxiety", "celebrate", "very nice", "ocean"]}, "answerKey": "B"}
{"id": "60e7338e9e6bfc746a15a161eb12706c", "question": "A creek could be located in the opposite for the city which is called what?", "question_concept": "creek", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meadow", "stick", "valley", "forest", "countryside"]}, "answerKey": "E"}
{"id": "a0f5414bf98e094f4d807abee28861a4", "question": "Where off the eastern U.S. would you find an anemone?", "question_concept": "anemone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["flower bed", "tide pool", "florida keys", "coral sea", "aquarium"]}, "answerKey": "C"}
{"id": "44120a9443c619d98ce5bfe4bb219c43", "question": "Where are traveling clothes often kept?", "question_concept": "clothes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["suitcase", "bedroom", "closet", "draws", "dresser"]}, "answerKey": "A"}
{"id": "38ab26e29a0984b212006d39185c43f3", "question": "If one needed the bathroom they needed a key, to get it they had to also buy something from the what?", "question_concept": "bathroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "convenience store", "rest area", "mall", "theater"]}, "answerKey": "B"}
{"id": "a5e207803684eea8a43ca6670c50b354", "question": "Although the sun did rise, what did the pessimist warn everyone it would do?", "question_concept": "rise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lay", "go down", "fall", "below", "sundown"]}, "answerKey": "B"}
{"id": "af3b9a8b1962cd3bcd19e644d873e7bc", "question": "The hardcovers were especially tall, so he removed a shelf on the what to make room?", "question_concept": "shelf", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chest of drawers", "grocery store", "hold alcohol", "nightstand", "bookcase"]}, "answerKey": "E"}
{"id": "43a91955fd0717997a16897c3324e095", "question": "If you're watching a comedy film what would you expect to hear from the audience?", "question_concept": "watching film", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "insight", "being entertained", "laughter", "fear"]}, "answerKey": "D"}
{"id": "7f7a6f2b3087bf37dadbe8aa8d358047", "question": "What can eating lunch cause that is painful?", "question_concept": "eating lunch", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["headache", "bad breath", "heartburn", "gain weight", "farts"]}, "answerKey": "C"}
{"id": "37d88a9bb24913c1973cc26d4ce3394f", "question": "The performer was ready to put on a show and stepped onto the launch platform, what was his job?", "question_concept": "launch platform", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cape canaveral florida", "nasa", "battleship", "ocean", "trapeze"]}, "answerKey": "E"}
{"id": "001b0f5a841fd81d13fbe67c7c7179d6", "question": "Eating is part of living, but your body doesn't use it all and the next day you will be doing what?", "question_concept": "eating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reduced", "getting full", "becoming full", "chewing", "defecating"]}, "answerKey": "E"}
{"id": "9f9ca9bb06d6afc31b19c365fb29a1c9", "question": "Where are you if you've paid to get a pizza?", "question_concept": "pizza", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["popular", "baked in oven", "restaurant", "oven", "plate"]}, "answerKey": "C"}
{"id": "d60c5a494539c66982c0f692afde9499", "question": "What would you use to find a place to stay?", "question_concept": "place to stay", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mexico", "phone book", "town", "city", "sun dial"]}, "answerKey": "B"}
{"id": "a6d3a2cb250a6310b8cabd31dbe2138c", "question": "If you're seeking a connection for your laptop, what are you trying to hook up with?", "question_concept": "connection", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["computer network", "electrical circuit", "lineage", "company", "wall"]}, "answerKey": "A"}
{"id": "27c523eb9099d2eec66296558eb4448e", "question": "The child didn't know the problems his mother was going through, all he had was what for her?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["care", "balloon", "loved", "become adult", "learn"]}, "answerKey": "C"}
{"id": "2509fdd7d94afe9d0c021654ce0ba93f", "question": "To see new films you must?", "question_concept": "see new", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["open eyes", "go to movies", "kick ball", "make art", "look for"]}, "answerKey": "B"}
{"id": "75b8195e23c6bada574f1e41471b8f23", "question": "What can happen when you contemplate alone for a long time?", "question_concept": "contemplate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["daydream", "headache", "get ideas", "sleep", "become distracted"]}, "answerKey": "A"}
{"id": "df1bf6f3f87975aa0c1b6d6153d9ecef", "question": "The pioneer went to the general store for storage measures, what was he looking for?", "question_concept": "general store", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["checkers", "barrels", "baking soda", "buffalo", "salt"]}, "answerKey": "B"}
{"id": "e99d4cb2e69d3e020ee9e4e9a84ac45b", "question": "I was apprehensive to buy the expensive equipment to play a game with so much walking and swinging around in grass, but now I understand why people what?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["care less", "play golf", "shake hands", "believe in god", "trip over"]}, "answerKey": "B"}
{"id": "b1274d6f5969dea4d46f43fbdc28fd97", "question": "What can a newspaper be used to do to an engagement?", "question_concept": "newspaper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["announce", "communicate", "educate", "inform", "cancel"]}, "answerKey": "A"}
{"id": "001cb999a61a5c8b4031ff53cf261714", "question": "John needed a straight wire.  Unfortunately, this one had endured some abuse and had become what?", "question_concept": "straight", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bent", "bent", "crooked", "straightforth", "curved"]}, "answerKey": "A"}
{"id": "18ee7a93410a6b4c9cec5d4894775991_1", "question": "Metal is taken from what which is pulled from the ground?", "question_concept": "metal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dirt", "instruments", "ore", "car", "junkyard"]}, "answerKey": "C"}
{"id": "3b8be90fdd8c67571d8d692eaa6dd87b", "question": "When not in use where on your property would you store you bucket?", "question_concept": "bucket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["utility closet", "outside", "well", "garden shed", "garage"]}, "answerKey": "D"}
{"id": "300bd7704ae8c5fcef618902f18fd01d", "question": "What does someone do to relax at night?", "question_concept": "relax", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["find time", "check mail", "listen to music", "go to bed", "stop worrying"]}, "answerKey": "D"}
{"id": "f18833ace65a54709377134168b457a9", "question": "Where might the stapler be if I cannot find it?", "question_concept": "stapler", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office building", "office supply store", "desk drawer", "with dwight", "desktop"]}, "answerKey": "C"}
{"id": "5bba03b425f5abc6e017f194cf074b06", "question": "Many homes in this country are built around a courtyard. Where is it?", "question_concept": "courtyard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["candidate", "spain", "lawn", "asshole", "office complex"]}, "answerKey": "B"}
{"id": "78276a4eab6e8d6b9ae3749211816977", "question": "Sean was a wreck.  He  loved to build houses, but in his current state, he couldn't do what?", "question_concept": "wreck", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stand up", "produce", "construct", "make", "build"]}, "answerKey": "C"}
{"id": "cf33e0f5891ce53a716432be06a46ee1", "question": "What would be happening if you are pretending to be a police officer?", "question_concept": "pretending", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fighting", "misunderstanding", "deception", "play", "distrust"]}, "answerKey": "C"}
{"id": "3938d6e50d38b1f8774b4f00a89bdb39", "question": "Where would you buy a finely crafted writing instrument?", "question_concept": "writing instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nasa", "classroom", "stationery store", "purse", "office supply store"]}, "answerKey": "C"}
{"id": "cabefb7063a728e77abd44d97397a2a4", "question": "The detective was finding information from witnesses, why would he do that?", "question_concept": "finding information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fun", "ulcers", "get answers", "happiness", "power"]}, "answerKey": "C"}
{"id": "60b909ad1d7956218a5d99954fdebecd", "question": "Joe found spiders in the place where he keeps his tools.  Where might that be?", "question_concept": "spiders", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cupboard", "toolbox", "closet", "garage", "mail box"]}, "answerKey": "D"}
{"id": "********************************", "question": "While on the fan boat he thought he'd see swamps and gators, but he was surprised to spot a bald eagle in what nature area?", "question_concept": "bald eagle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["everglades", "high places", "natural habitat", "new york", "colorado"]}, "answerKey": "A"}
{"id": "f36027954e43cfd926451bdf7cb0c3ac", "question": "Where are you likely to find a supermarket?", "question_concept": "supermarket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy food for family", "city or town", "get supplies", "strip mall", "vermont"]}, "answerKey": "B"}
{"id": "7ec14907622c6d5a6087cd59a22d8c9d", "question": "Where would you need to use a lantern?", "question_concept": "lantern", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grocery store", "antique shop", "house", "dark place", "street"]}, "answerKey": "D"}
{"id": "efe488f67b53a4b6e69782c01c84f06c", "question": "What area does a police officer patrol?", "question_concept": "police officer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["direct traffic", "city", "beat", "street", "park"]}, "answerKey": "C"}
{"id": "7c62637437ad7515452886074010a438", "question": "Why would a woman kill a stranger she met in a dark alley?", "question_concept": "kill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["being raped", "get rid of", "they didn't know the passcode", "get revenge", "were evil"]}, "answerKey": "A"}
{"id": "4f7be1c68654e2924c161c8eca652928", "question": "The baby was cranky, it needed to eat breakfast but refused to what?", "question_concept": "eat breakfast", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy food", "open mouth", "get out of bed", "cry", "wake up"]}, "answerKey": "B"}
{"id": "e4976ee741cf4b28b8a42780ffb15774", "question": "What is made up of people?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buildings", "audience", "apartment", "classroom", "falling down"]}, "answerKey": "B"}
{"id": "14e75a42a416d32a24e2826cae34d2bf", "question": "He was afraid he would die from his cold, so he wisely decided to what?", "question_concept": "die", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "write will", "never want", "were shot", "seek help"]}, "answerKey": "E"}
{"id": "004607228ad49b69eac932c1005d6106", "question": "Where would you get a pen if you do not have one?", "question_concept": "pen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["briefcase", "desk drawer", "friend's house", "pocket", "sidewalk"]}, "answerKey": "C"}
{"id": "a7f54ee1866d5db34eacf40efa53c93e", "question": "Why would a small dog pant if it's hot outside?", "question_concept": "small dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["outside", "europe", "heat", "wet", "dog show"]}, "answerKey": "C"}
{"id": "e56c56c3cfe50ba0c787c2bd67255be8", "question": "She asked her little boy why, he replied that he didn't know and it was just what?", "question_concept": "why", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["case", "reason", "how", "because", "answer"]}, "answerKey": "D"}
{"id": "6f48ee564a48293eb501cc0d8197bdd9", "question": "Where would you display a picture on a horizontal surface?", "question_concept": "picture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["microwave", "desktop", "shelf", "art show", "wall"]}, "answerKey": "C"}
{"id": "13d2a103abbed930cabc9567a1ba12f2", "question": "What skill is needed for riding a bike?", "question_concept": "riding bike", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wheels", "feet", "pedalling", "practice", "good balance"]}, "answerKey": "E"}
{"id": "0c1efb38e023ee9725486fbec4f2d797", "question": "He looked at the field of pumps, all slowing churning oil out of the what?", "question_concept": "oil", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["manual", "street", "restaurant", "ground", "service station"]}, "answerKey": "D"}
{"id": "b7ab4a5e0c19a98f41cd1ba3176f2dff", "question": "The department to where vendors deliver goods for sale is called what?", "question_concept": "deliver", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["delivered", "take away", "receiving", "pick up", "keep"]}, "answerKey": "C"}
{"id": "8bcbb5098876940b2382db3a9a0b1beb", "question": "Where is the worst place to be in a ticket office?", "question_concept": "ticket office", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["at the top", "movie theaters", "train station", "end of line", "opera house"]}, "answerKey": "D"}
{"id": "c7ce02d9365fe9275f88338ad51cbde6", "question": "Exercise is very good for you, for faster recovery you should always do what afterwards?", "question_concept": "exercise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stretch", "lower cholesterol", "weigh", "track", "expend energy"]}, "answerKey": "A"}
{"id": "fb54a118d46b2776e435d411ae3dd9c8", "question": "What happens when you go somewhere and forget something at home?", "question_concept": "go somewhere", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arriving", "arrive there", "turn around", "go back", "fart"]}, "answerKey": "D"}
{"id": "2c13e6d61e3733db90a9fd22d72b3337", "question": "Where would you acquire a wind instrument for you own use?", "question_concept": "wind instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["band practice", "concert", "music store", "symphony", "music room"]}, "answerKey": "C"}
{"id": "350292ae429060a00ff2cf64d71558e4", "question": "Where would a person light alcohol on fire to observe the reaction?", "question_concept": "alcohol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "bar", "pub", "restaurants", "chemistry lab"]}, "answerKey": "E"}
{"id": "********************************", "question": "If a storey contained a panoramic view, what kind of structure would it be in?", "question_concept": "storey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["horizontal room", "storey book", "mall", "tall building", "book of stories"]}, "answerKey": "D"}
{"id": "81cc0d320488c7bacafb285cf7db5fbd", "question": "Where does lettuce arrive by large trucks?", "question_concept": "lettuce", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "supermarket", "farmer's market", "salad", "refrigerator"]}, "answerKey": "B"}
{"id": "26c8a7165d0ed7250b9328f90d83ba83", "question": "Why do people who are dying receive social security payments?", "question_concept": "dying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rejuvenation", "born again", "no longer exist", "unable to work", "change of color"]}, "answerKey": "D"}
{"id": "636fc69dee35cd357b4191b47e64d0e5", "question": "What should I do with a jumping rope?", "question_concept": "jumping rope", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fatigue", "sweating", "get tired", "tiredness", "hopping"]}, "answerKey": "E"}
{"id": "f0c4622a082eb9ad0690dd36dcf61297", "question": "What do geese do every fall in fields?", "question_concept": "geese", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["guard house", "fly", "eat", "follow ultralight airplane", "group together"]}, "answerKey": "E"}
{"id": "4499ebd5e8188b0d5fdef6afd893017a", "question": "I took my seat, the curtains drew back and I enjoyed the what?", "question_concept": "seat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airplane", "movie", "auditorium", "theatre", "show"]}, "answerKey": "E"}
{"id": "230cc491829307e8edb5423c8d09f945", "question": "What should everyone do who doesn't want to fight anymore?", "question_concept": "everyone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["explicate", "pay tribute to king", "hope for peace", "wear shoes", "do well"]}, "answerKey": "C"}
{"id": "6163a897cd7eac1deddd4c002a1930ae", "question": "Where is the ideal location for a post office?", "question_concept": "post office", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "business district", "above ground", "most towns", "center of town"]}, "answerKey": "E"}
{"id": "55478486079423907508a06be13ca536", "question": "Where outside of a city would a squirrel live?", "question_concept": "squirrel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["roof", "inside home", "forest", "yard", "park"]}, "answerKey": "C"}
{"id": "4fa0d61ec82eb1e238d8938d5f43f392", "question": "You should watch out for snakes if floating down what African body of water?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wet grass", "western texas", "high grass", "amazon river", "tree"]}, "answerKey": "D"}
{"id": "b4f79ca5f3595248ee25292ab60ad105", "question": "At the end of the day as he began to eat he paused and thanked her, it wasn't often she would what?", "question_concept": "eat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cook dinner", "did chores", "make food", "stretch out", "get food"]}, "answerKey": "A"}
{"id": "c39131d979c9205c11d0e109e18188e4", "question": "To what do trees roots cling?", "question_concept": "trees", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["yard", "orchard", "museum", "countryside", "surface of earth"]}, "answerKey": "E"}
{"id": "bd773d64f4e22db2358c6e00cbdf2d83", "question": "What probably has a lot of dust in the back?", "question_concept": "dust", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["closet", "door", "corner", "shelf", "library"]}, "answerKey": "A"}
{"id": "2b416120e2fbd84b44b5dcd4eb42ed5c", "question": "At the new comic store he found himself making friends, it was nice to meet people with what?", "question_concept": "making friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["smiling", "smile", "open mind", "common interests", "laughter"]}, "answerKey": "D"}
{"id": "cef855ec07c66a731741026c2839b0d3", "question": "The student explained he had a clue what neuroepithelium was and got really nervous, he then lost his balance because a what issue?", "question_concept": "neuroepithelium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tastebud", "retina", "inner ear", "nasal cavity", "autistic"]}, "answerKey": "C"}
{"id": "0bbb82c1dc4bfd3b0e0c409a0afd248b", "question": "What could people do that involves talking?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["confession", "state park", "sing", "carnival", "opera"]}, "answerKey": "A"}
{"id": "67beae081a9b5ef56988f205f80cf129", "question": "If you're a child answering questions and an adult is asking them that adult is doing what?", "question_concept": "answering questions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["discussion", "explaning", "teaching", "confusion", "correct"]}, "answerKey": "C"}
{"id": "3b4dcfcab4726496bdbe9535cc669082", "question": "He has lactose intolerant, but was eating dinner made of cheese, what followed for him?", "question_concept": "eating dinner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["digestive", "feel better", "sleepiness", "indigestion", "illness"]}, "answerKey": "D"}
{"id": "eebddf5f35d85e9fe2ecbd9b56c1db60", "question": "The teacher played on the upright piano, she was explaining the song to all the students in the what?", "question_concept": "upright piano", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music room", "bathroom", "house", "living room", "music store"]}, "answerKey": "A"}
{"id": "5393ba1ce298bd1ac4744c07d7373a9c", "question": "When you get an F, you fail. If you get A's you are?", "question_concept": "fail", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["passed", "completing", "passed", "passing", "succeeding"]}, "answerKey": "D"}
{"id": "fde48d43e27cefed6ed9c52514e0bb6d", "question": "What is the main purpose of having a bath?", "question_concept": "having bath", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cleanness", "wetness", "exfoliation", "use water", "hygiene"]}, "answerKey": "A"}
{"id": "da83d85e28778c082d9a63f5b890b26d", "question": "The ball was hit over a boundary and struck an audience member.  What kind of game were they playing?", "question_concept": "boundary", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sporting event", "sporting", "basketball", "society", "ranch country"]}, "answerKey": "A"}
{"id": "cfa980561efe82e7ae7080d4f081b463", "question": "What is someone operating a vehicle likely to be accused of after becoming inebriated?", "question_concept": "becoming inebriated", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["punish", "arrest", "automobile accidents", "drunk driving", "talking nonsense"]}, "answerKey": "D"}
{"id": "384b89e789e0f4b4796120394fb6303b", "question": "Where would you get jewelry if you do not have any?", "question_concept": "jewelry", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vault", "suitcase", "neighbour's house", "department store", "safe deposit box"]}, "answerKey": "D"}
{"id": "********************************", "question": "What is a philosopher waiting for to eventually gain through his studies?", "question_concept": "waiting for", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["job", "boredom", "anxiety", "impatience", "wisdom"]}, "answerKey": "E"}
{"id": "732183ead4206e51ed4df18b9c9f14fe", "question": "What do young boys do on the ice in the winter?", "question_concept": "winter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ski", "play hockey", "summer", "knit", "warm"]}, "answerKey": "B"}
{"id": "2632ff6c9b781d3aa74e8dd36b990871", "question": "She loved spending money at the thrift store on knickknacks, this resulted in a lot of what on every shelf in her house?", "question_concept": "spending money", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["poverty", "clutter", "getting", "satisfaction", "more happiness"]}, "answerKey": "B"}
{"id": "63db79b940f36f0333377f85c19eacb2", "question": "I listened to lecture intensely, what is my goal?", "question_concept": "listen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gain confidence", "concentrate", "get attention", "pay attention", "stop talking"]}, "answerKey": "B"}
{"id": "1520a8fd3116e7b856947c5e308d7ce5", "question": "If a person is using a computer to talk to their granddaughter, what might the computer cause for them?", "question_concept": "using computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["program created", "stress", "happiness", "ocean", "headache"]}, "answerKey": "C"}
{"id": "bd780fea2d4dd262583446e64c0f314d", "question": "Joe was there to meet a large number of people.  As he filed though the entrance hall, he saw many strangers who came from far away.  What sort of building is he probably in?", "question_concept": "entrance hall", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["person", "box", "convention center", "public building", "large building"]}, "answerKey": "C"}
{"id": "99e0b2ddf88ebed98b977043b7c2331b", "question": "John wanted scatter his wife's remains in a lake in the wilderness.  He had to delay before of where he lived.  Where did he live?", "question_concept": "lake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mountains", "dead body", "pay debts", "state park", "new york"]}, "answerKey": "E"}
{"id": "eb0e0c4eaf19c1e9b4df3b4d3a11be3d", "question": "Many towns and cities have trash cans where on sidewalks?", "question_concept": "trash can", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospital", "park", "corner", "motel", "office"]}, "answerKey": "C"}
{"id": "467a3b464b08b3ffc9922e2a726554f6", "question": "The family wanted to adopt for enviro-ethical reasons, what did they abhor?", "question_concept": "adopt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["orphan", "biological child", "give away", "foster child", "abandon"]}, "answerKey": "B"}
{"id": "dea70fe40fac9ad03bf319bf8a480efa", "question": "What happens when airplane engines cut off and are unable to be restarted in flight?", "question_concept": "airplanes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stall", "start melting", "taxi", "crash", "speed up"]}, "answerKey": "D"}
{"id": "2f1680da0d388a8453150ff3637e4689", "question": "Where would you be concerned about finding a cavity?", "question_concept": "cavity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["solid object", "molar", "dentist", "unbrushed tooth", "teeth"]}, "answerKey": "E"}
{"id": "8369adc4b4710d00f917d80a75d844d7", "question": "Human beings learn about current events from what print item?", "question_concept": "human beings", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["question authority", "melt", "read newspapers", "act", "dictionary"]}, "answerKey": "C"}
{"id": "20a3bb788cf408d9a3e25e610fe60905", "question": "In what kind of environment does an anemone live?", "question_concept": "anemone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nursery", "south pacific", "desert", "sea water", "atlantic ocean"]}, "answerKey": "D"}
{"id": "36c1f50eec01c287b8ef6ffe69fe0528", "question": "He wanted lodging in the actual what, so that he was already where he needed to be?", "question_concept": "lodgings", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["a yurt", "resort area", "big city", "michigan", "going on vacation"]}, "answerKey": "B"}
{"id": "5f4825137a27f369fe859e85dfe1793f", "question": "If I am suffering from boredom, and I want to see something beautiful, what should I do?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["see art", "see ghost", "watch film", "grocery shop", "do crossword puzzle"]}, "answerKey": "A"}
{"id": "b3dc6d6a5e2f9d7da8eb72816c80b3f8_1", "question": "The goal was to hit the target, but a projectile ball can't hit anything if it isn't in what?", "question_concept": "projectile ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motion", "ocean", "flintlock", "arcade", "tennis court"]}, "answerKey": "A"}
{"id": "63bb6128026ce24209583d0eea75fc27", "question": "Where is a good place to set a cup of coffee while relaxing?", "question_concept": "cup of coffee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coffee shop", "kitchen", "hand", "table", "office"]}, "answerKey": "D"}
{"id": "e8a9142d2402f818273dd62cf5a7b559_1", "question": "If a fried egg was runny and there was no toast to sop it up, after the meal there'd be a messy what?", "question_concept": "egg", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["henhouse", "garden", "plate", "supermarket", "bird's nest"]}, "answerKey": "C"}
{"id": "ead9c9744aee08678759158efe005175", "question": "If I want to behave with proper aplomb, what manners should I avoid?", "question_concept": "proper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["inappropriate", "incomplete", "impolite", "none", "incorrect"]}, "answerKey": "A"}
{"id": "ab8bf60f76bc6119459271140ccae781", "question": "Before lifting weights he liked to warm up on the squash court, he really enjoyed the facilities of the what?", "question_concept": "squash court", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["swimming pool", "rich person's house", "country club", "fitness center", "park"]}, "answerKey": "D"}
{"id": "3c6e2d95a63316b31986e8c7979582c9", "question": "What will happen to animals after eating food?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bite", "digestion", "feel pleasure", "pass water", "listen to each other"]}, "answerKey": "C"}
{"id": "5c171b9837af49211891ce40e4a10204", "question": "If I wanted to grow plants, where could I put a lot of dirt?", "question_concept": "dirt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["corner", "street", "closet", "garden", "bathtub"]}, "answerKey": "D"}
{"id": "56d0fc282a144565f2c852415c6fa92c", "question": "What does a person often feel about someone judging them guilty?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["controversy", "responsibility", "resentment", "judge feelings", "hurt feelings"]}, "answerKey": "C"}
{"id": "5b8a3081c3235d62bc77e2d15f3ad454", "question": "A town between two mountains is located in a what?", "question_concept": "town", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["valley", "hospital", "state", "train station", "michigan"]}, "answerKey": "A"}
{"id": "e43c4eaa04243ddee30f29171718eb92", "question": "James need to use a toilet but there were no public ones in sight.  Eventually he broke down and did something very expensive so that he could get a toilet.  Where might he have gone?", "question_concept": "toilet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motel room", "apartment", "bathroom", "games", "house"]}, "answerKey": "A"}
{"id": "84a736d4b702a6869d8fa8523aee6f1b", "question": "Why did the heavy metal band need electricity at the stadium?", "question_concept": "electricity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert", "bedroom", "make person sick", "building", "church"]}, "answerKey": "A"}
{"id": "72611791cdcb040f2d699827fb9cebc4", "question": "What is a person looking for when completing puzzles or riddles?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["avoid pain", "compliments", "intellectual challenge", "passing grade", "attention"]}, "answerKey": "C"}
{"id": "4477fb61fde4bb8695c241dfc366b554", "question": "If someone was making breakfast, they'd probably put two slices of bread in the what?", "question_concept": "bread", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["plastic bag", "pantry", "supermarket", "toaster", "prison"]}, "answerKey": "D"}
{"id": "ce246bc94a54431b9c0530e71d2456b5", "question": "His house was a mess, he began doing housework to get what?", "question_concept": "doing housework", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boredom", "nice home", "michigan", "feeling satisfied", "house clean"]}, "answerKey": "E"}
{"id": "2eef2d255fe629414f4d24ade8590102", "question": "Where would a corpse be covered by a blanket?", "question_concept": "blanket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bath store", "bedroom", "hospital", "flower garden", "michigan"]}, "answerKey": "C"}
{"id": "2f85d53721ccc8b3fa4cfc184186d124", "question": "The man  tried to break the glass in order to make his escape in time, but he could not.  The person in the cat, trying to kill him, did what?", "question_concept": "break", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["accelerate", "putting together", "working", "construct", "train"]}, "answerKey": "A"}
{"id": "2192c5c2145a6e03755ad89a02e64055", "question": "The trucker plopped on the bench with a sense of relief, where did he arrive?", "question_concept": "bench", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bordello", "rest area", "garden", "bus stop", "state park"]}, "answerKey": "B"}
{"id": "bea07406aaadeef50110883b6932d86a", "question": "What is part of a republic like the USA?", "question_concept": "republic", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["state", "democratic", "kingdom", "democracy", "dictatorship"]}, "answerKey": "A"}
{"id": "7a58e7e7bf76658751e850f790922aba", "question": "Where do you keep extra clothing on a hike?", "question_concept": "clothing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["person", "hamper", "closet", "upstairs", "backpack"]}, "answerKey": "E"}
{"id": "76b2c6d254f9127b4fd66d90e1a330e7", "question": "What could an apple tree do?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new hampshire", "bloom", "washington state", "sunshine", "spontaneously combust"]}, "answerKey": "B"}
{"id": "cdd3d074031fbd3efeb4f9408abef04e", "question": "What very cold area in the east can a crab be found?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fish market", "shallow waters", "atlantic ocean", "fresh water", "shore line"]}, "answerKey": "C"}
{"id": "359aed918343d228e67cef329b693904", "question": "The chef wanted to perfect his craft, what did he do?", "question_concept": "chef", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["thin potatos", "prepare food", "study french cooking", "drink", "cook dinner"]}, "answerKey": "C"}
{"id": "cf02cca40a47c2deefd8b2e5a5ff2f70", "question": "She wanted a kitten and puppy so why did she only get the puppy?", "question_concept": "puppy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["one choice for pet", "cute", "kennel", "soft", "waxy"]}, "answerKey": "A"}
{"id": "ac1abecdbbd7bcde6592ca645c2ecb1e", "question": "There was no shade for Jenny.  She was forced to lie there exposed to what?", "question_concept": "shade", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["full sunlight", "bright sunshine", "sunny place", "eat cake", "direct sunlight"]}, "answerKey": "A"}
{"id": "2adbb4fc0d5249dc411dda433f378591", "question": "What could happen to you after you are cleaning house for a long time?", "question_concept": "cleaning house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neatness", "tiredness", "order", "exhaustion", "sneezing"]}, "answerKey": "D"}
{"id": "5a1c8a9dbbb60e523cc1ba14a370729c", "question": "What is someone doing when scheduling when to go to party?", "question_concept": "going to party", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rumpspringa", "meeting new people", "having fun", "meet new people", "plan"]}, "answerKey": "E"}
{"id": "3665b329f93f7c84edeabe394140f8d2", "question": "What kind of path do comets tend to have?", "question_concept": "comets", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ice", "set orbits", "universe", "space", "solid nucleus"]}, "answerKey": "B"}
{"id": "dbcedaa6a6f1f68bc8f2bf7aef23294e", "question": "What do people feel after having sex that requires them to shower?", "question_concept": "sex", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bedroom", "pleasant", "obesity", "painful", "dirty"]}, "answerKey": "E"}
{"id": "ba3a2b9ff289c106051163f840a6f5ba", "question": "The vet found malignant tumors on the animals, what is their likely fate?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["euthanasia", "pass water", "die of cancer", "feel pain", "feel pleasure"]}, "answerKey": "C"}
{"id": "13fc28f53423a9b3a656c9431df1b3b5", "question": "What is the thing that is agitated in your head when kissing?", "question_concept": "kissing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sexual stimulation", "herpes", "headache", "catch cold", "happiness"]}, "answerKey": "E"}
{"id": "3f4b48708d08f8bf7bec796531023f9c", "question": "Billy was reading the newspaper as he commuted to work, but once he got to his destination he balled it up and put it somewhere. Where did it put it?", "question_concept": "newspaper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["trash", "floor", "subway", "ground", "lawn"]}, "answerKey": "A"}
{"id": "c61790eb63ff6652b878ca051493c07d", "question": "Where do you keep a pail in your house?", "question_concept": "pail", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garage", "pool", "utility room", "hardware store", "wishing well"]}, "answerKey": "C"}
{"id": "e5ebbe0ea4097bb197ac525b49108362", "question": "what is printed with ink and distributed daily?", "question_concept": "ink", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fountain pen", "squid", "newspaper", "book", "printer"]}, "answerKey": "C"}
{"id": "029e36d8f65982b142c319064dc5e32f", "question": "What are people likely to do when an unexpected decent outcome occurs?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kill each other", "thank god", "experience pain", "hatred", "talk to each other"]}, "answerKey": "B"}
{"id": "3d1a67f87b34303f97549ba83e5521c2", "question": "The terrace had Kanji written on it, indicating that it was made where?", "question_concept": "terrace", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["japan", "rice paddy", "garden", "michigan", "italy"]}, "answerKey": "A"}
{"id": "e050bce7048da1b3743a54153e91694e", "question": "The company sent off many purchases, they used recycled cardboard as their what?", "question_concept": "cardboard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["packaging materials", "recycle bin", "box factory", "warehouse", "bowler hats"]}, "answerKey": "A"}
{"id": "8233ccb60dd0c0ff3b7ca5d73e5681f2", "question": "Why might a person be known as a liar?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have no home", "false information", "hungry", "made fun of", "brain tumor"]}, "answerKey": "B"}
{"id": "eb4b2cd0f2a69686e5a82250c5806b84", "question": "The child was politely waiting for dessert, he was eventually rewarded for his what?", "question_concept": "waiting for", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["timing", "expenditure of time", "getting bored", "anger", "patience"]}, "answerKey": "E"}
{"id": "d0bda97a087904320216e4d0b8a08a8d", "question": "The man was giving assistance to a pan handler in the streets, how did he give assistance?", "question_concept": "giving assistance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feeling good", "killing", "law suits", "out of pocket", "feel loved"]}, "answerKey": "D"}
{"id": "e216381e9f0ddd1d248ee25fccca2b1f", "question": "What do you call the caretakers of a child?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["principal", "birth", "loving couple", "act of sex", "parents"]}, "answerKey": "E"}
{"id": "b1fba9ad6193c6751ddb3f58f7f39b35", "question": "Where would you run in to a niece you only see every one and a while?", "question_concept": "niece", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["family reunion", "brother's house", "family picture book", "family tree", "party"]}, "answerKey": "A"}
{"id": "3ceae7a18073050bd2c0448abef1f393", "question": "Working on the elaborate task was taxing, it require extreme what?", "question_concept": "working", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["holding", "concentration", "energy", "job", "energh"]}, "answerKey": "B"}
{"id": "f1182e3a070f5a1be529843aa6e5c20c", "question": "What may you have after awaking after a night of heavy drinking?", "question_concept": "awaking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get up", "discomfort", "discomfort", "headache", "shock"]}, "answerKey": "D"}
{"id": "5799089c131e26473697afc54d5f6964", "question": "What uses a ribbon to put words on paper?", "question_concept": "ribbon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wrapping paper", "girl's hair", "bath", "floral arrangement", "typewriter"]}, "answerKey": "E"}
{"id": "7ce1f99e8185489a7113e6d18c71abb0", "question": "Where are sheep likely to live?", "question_concept": "sheep", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "meadow", "lamb", "farm", "fairgrounds"]}, "answerKey": "D"}
{"id": "69425fb4cd2dc034e9ff223d2d5676ec", "question": "If I was watching TV on the couch and the air was stuffy, I might turn the fan on to make the what more comfortable?", "question_concept": "fan", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hockey game", "living room", "bathroom", "football stadium", "hot room"]}, "answerKey": "B"}
{"id": "f75b22d5b88ac56ae7df030c1ebeded5", "question": "While walking the student needed to store his writing insturment away, where did he put it?", "question_concept": "writing instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk drawer", "cabinet", "purse", "classroom", "pocket"]}, "answerKey": "E"}
{"id": "4eb3e69c0d42a2287692d2b9d2cb5979", "question": "Who watches a play in an auditorium?", "question_concept": "auditorium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "crowd", "city", "group", "high school"]}, "answerKey": "B"}
{"id": "7d937233b4a9043da0b976dbd42d141b", "question": "What is a possible outcome for committing murder?", "question_concept": "committing murder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["problems", "incarceration", "trial", "imprisonment", "prosecution"]}, "answerKey": "D"}
{"id": "6bd176cc91a2a2088807ec446c008856", "question": "where is a good place to obtain new soap?", "question_concept": "soap", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "washing", "cabinet", "own home", "sink"]}, "answerKey": "A"}
{"id": "c3890d43b84635d9e61c007ca2521d5b", "question": "What do people do for food?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talk to each other", "complete job", "wear hats", "kill animals", "believe in god"]}, "answerKey": "D"}
{"id": "6195ed74cf445cb5d991e1076a080dde", "question": "There was many a bottle to choose from behind the cashier where?", "question_concept": "bottle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grocery store", "diaper bag", "gas station", "liquor store", "medicine cabinet"]}, "answerKey": "D"}
{"id": "37644422df4bcd28b3f54bbf3fc2c0f8", "question": "They had to know where to go, they got on the national highway after consulting the what?", "question_concept": "national highway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["canada", "atlas", "united states", "major cities", "book"]}, "answerKey": "B"}
{"id": "23d97480fe45bace231503f8fc367a5b", "question": "What do professors primarily do?", "question_concept": "professors", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["master physics", "state facts", "wear wrinkled tweed jackets", "school students", "teach courses"]}, "answerKey": "E"}
{"id": "15556e26feaa5a8a29c9f30896e535d4", "question": "Where do you throw a ball at pins?", "question_concept": "ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bowling alley", "football stadium", "soccer field", "sporting event", "sporting goods store"]}, "answerKey": "A"}
{"id": "6be05d227f4f6fe727218fc8be9df340", "question": "What might you need to do cleaning?", "question_concept": "cleaning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sing a song", "neatness", "allergies", "healthy living", "using water"]}, "answerKey": "E"}
{"id": "3f3ba1d9a3bfe63df11247a968eaddce", "question": "If i were to spit a lot without noticing i may have extra what?", "question_concept": "spitting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["phlegm", "saliva nd mouth", "disease", "germs", "spittle"]}, "answerKey": "B"}
{"id": "ca9a3ccfb140aa66816f96ac983b6d9f_1", "question": "If student got a list of supplies from class like paper and pencils, their parent would have to go where?", "question_concept": "pencils", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "parking garage", "store", "backpack", "cabinet"]}, "answerKey": "C"}
{"id": "487cabfcd776d89748ee7e7bb681ad59", "question": "Why do young people swallow semen ?", "question_concept": "swallow semen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["you're into", "prostitute", "you're curious", "curiosity", "heterosexual woman in love"]}, "answerKey": "C"}
{"id": "6915dfdefe3b1cd5fd8886c8bb84929a", "question": "Sally was standing in queue.  The line was very, very slow.  What was she feeling?", "question_concept": "standing in queue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["frustration", "delays", "being annoyed", "moving forward", "progress"]}, "answerKey": "A"}
{"id": "ec224c1dbfb569cce7ec317fe987ae68", "question": "What is the animal trying to accomplish?", "question_concept": "animal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sand trap", "live long", "leave home", "feel pain", "eating"]}, "answerKey": "B"}
{"id": "0cba8ddda21e29c8c53482e131d741cd", "question": "James and Holly went dancing together. As they danced, he  pressed himself against her what?", "question_concept": "dancing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["euphoria", "moving body", "rhythmic movement", "happiness", "fatigue"]}, "answerKey": "B"}
{"id": "e65559cd9f5d96b577caeb78d9033502", "question": "If a house has a subscription, what likely shows up in the driveway every morning?", "question_concept": "house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["subdivision", "newspaper", "street", "laundry mat", "surface of earth"]}, "answerKey": "B"}
{"id": "b8937a30f25093910c040f4e63e1d352", "question": "What does a person do when they feel dirty?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel lucky", "cross street", "wash themselves", "eat", "wonder what happened"]}, "answerKey": "C"}
{"id": "aabe8eb218468fc63b6c9aa6d428c951", "question": "After the weight cut he was worried about his energy levels, but this was part of participating in a what?", "question_concept": "energy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "wrestle", "play sports", "matter", "sleep"]}, "answerKey": "B"}
{"id": "43ba9669564217f2f909f33acbedaf95", "question": "what does a person do to stay healthy?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fever", "eat every day", "excited", "headache", "expressive"]}, "answerKey": "B"}
{"id": "2b9b625c788584b8d41f1a74d740e126", "question": "Who is the guard here for?", "question_concept": "guard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["man post", "attack", "intimidation", "prisoner", "unprotected"]}, "answerKey": "D"}
{"id": "eb6807290df71b040e2c7bcc5d11fdea", "question": "If a person stutters when he experiences anxiety or excitement, he'll have difficult doing what?", "question_concept": "excitement", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["express information", "dance", "library", "go somewhere", "study"]}, "answerKey": "A"}
{"id": "f06852fb4bb2764dc208a991d037f211", "question": "Where can you keep letter opener when it likely to be needed soon?", "question_concept": "letter opener", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office supply store", "stationery store", "dek", "martyr's chest", "refrigerator"]}, "answerKey": "C"}
{"id": "5efadabaf61b5174916e3ab659bcd283", "question": "Danny found that the carpet did not ,match the drapes, which was disappointing, because this place was expensive.  But it was the only place in town that wasn't booked solid for the week and he needed it while he was in town, so he couldn't complain.   Where might this place be?", "question_concept": "carpet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["brothel", "restaurant", "building", "bowling alley", "at hotel"]}, "answerKey": "E"}
{"id": "e9d4c747018ff81b8c0aefb5abc3c539", "question": "What do people need to do to change their lives?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["face problems", "better themselves", "pay bills", "become disillusioned", "eat chicken"]}, "answerKey": "A"}
{"id": "30a8cfd186f1aae5acd425a52d058863", "question": "Humans need shelter to survive.  They usually find shelter where?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["underpass", "homes", "workplace", "school", "space shuttle"]}, "answerKey": "B"}
{"id": "9e7805871c8a276300a89fe910a90949", "question": "Someone who had a very bad flight might be given a trip in this to make up for it?", "question_concept": "bad", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["first class", "propitious", "reputable", "one", "sufficient"]}, "answerKey": "A"}
{"id": "047c2d8c65d297b39aa42821c1ca76a9", "question": "Nature can be good and bad for the person who walks, what are some things?", "question_concept": "hike", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["seeing bear", "see beautiful views", "get wet", "getting lost", "murdered by a landshark"]}, "answerKey": "B"}
{"id": "0bed77da54b6c54facd0ee6614aad72e", "question": "Jim decided to lose weight.  He thought that exercise is the best way to lose weight because you can't get rid of what?", "question_concept": "exercise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["need for food", "fitness", "sweating", "fastfood", "thirst"]}, "answerKey": "A"}
{"id": "32e2adee67aace0a98c830fb39463015", "question": "Nature creates more beautiful structures than those that are what?", "question_concept": "nature", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["artificial", "indoors", "city", "man made", "eat cake"]}, "answerKey": "D"}
{"id": "8272f08792b873885f93d4c148e307e5", "question": "The water in clouds turn in to what when it gets cold?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["typhoon", "snowflake", "laddle", "teardrops", "sink"]}, "answerKey": "B"}
{"id": "bc05bc6b4df7a3d25a361515fe8912ad", "question": "What southern U.S. state is know for having many swamps?", "question_concept": "swamp", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wetlands", "new york", "michigan", "louisiana", "river delta"]}, "answerKey": "D"}
{"id": "b893a6e7a2b172bd71f03c9dbee4f960", "question": "When going to sleep what happens to your body?", "question_concept": "going to sleep", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["snoring", "latency", "dreams", "relaxation", "dreaming"]}, "answerKey": "D"}
{"id": "cf8e30dd6956d03e3f0f0397112a8696", "question": "Where is a monkey likely to enjoy being?", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["banana tree", "sailor suit", "theatre", "mulberry bush", "research laboratory"]}, "answerKey": "A"}
{"id": "159d50e325b59c6d29ec371500e173b4", "question": "What is a form of anaerobic exercising?", "question_concept": "exercising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shortness of breath", "lift weights", "error", "fall down", "run"]}, "answerKey": "E"}
{"id": "17eafc807b198236faf06a66f4c05313", "question": "The earth is one planet in what?", "question_concept": "earth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tree", "orbit", "solar system", "fotograph", "dreams"]}, "answerKey": "C"}
{"id": "24eebfa678112100803da16dde148b2d", "question": "Where would you put a container can after you buy it?", "question_concept": "container can", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pantry", "store", "gas", "liquid", "garage"]}, "answerKey": "E"}
{"id": "ec882fc3a9bfaeae2a26fe31c2ef2c07", "question": "Where did you meet your best friend since Kindergarten?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["friend's house", "school", "fraternity house", "internet cafe", "airplane"]}, "answerKey": "B"}
{"id": "0a006d16d9042e0c170935e5fbf7f9af", "question": "James was below the balloon.  He watched it rise.  What direction did he look in?", "question_concept": "below", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["upstairs", "aloft", "diagonal", "upstream", "upwards"]}, "answerKey": "E"}
{"id": "d33a81660058e570a18fb2eafa284a78", "question": "John and Tim like playing. It makes them what?", "question_concept": "playing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feeling happy", "learning", "injury", "burn", "get hungry"]}, "answerKey": "A"}
{"id": "1e09c3136a743b862e783700b7667028", "question": "What could happen if someone is seeing new presents at a birthday party?", "question_concept": "seeing new", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["envy", "jealousy", "education", "fear", "excitement"]}, "answerKey": "E"}
{"id": "5e851c47682bdf79ec7c139ecf124c9a", "question": "Joe's cat smelled something delicious and jumped into this, causing him to panic and fear for its life. Where might it have jumped?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meat loaf", "bedroom", "microwave", "living room", "floor"]}, "answerKey": "C"}
{"id": "b148f18fb8b5a504b67078ef6ac29717", "question": "Why would a person put flowers in a room with dirty gym socks?", "question_concept": "flowers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["continue to grow", "plant themselves", "many colors", "smell good", "make pretty"]}, "answerKey": "D"}
{"id": "b6bbe013995fdb5def3d504319af0791", "question": "The table wasn't level.  some parts were higher and some were lower with no rhyme or reason.   It was very what?", "question_concept": "level", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["electrical circuit", "build evenly", "uneven", "unbalanced", "tilted"]}, "answerKey": "C"}
{"id": "0c2fa15a02d0b6ca6707e98fac7589e4", "question": "The person signed up for home insurance, what is he seeking?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["financial security", "live well", "good relationship", "compliments", "discounted furniture"]}, "answerKey": "A"}
{"id": "a656e74a943f9e2698a25bbcfb4e96db", "question": "James know that committing murder was wrong, but he thought that he could get away with it.  He was really troubled  and fearful because of what?", "question_concept": "committing murder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["happiness", "problems", "prosecution", "distress", "misery"]}, "answerKey": "C"}
{"id": "8086f022f2d4a4888ae1f8c7e4541ab9", "question": "How can someone die from eating hamburger?", "question_concept": "eating hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gas", "getting full", "mad cow disease", "death", "feel full"]}, "answerKey": "C"}
{"id": "5655a3002dd9a6b7dabede1dd26a5893", "question": "Where would using a boat not require navigation skills?", "question_concept": "boat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water", "ocean", "garage", "harbor", "river"]}, "answerKey": "E"}
{"id": "17d9bfaee1efac51b1ca240125bc5977", "question": "What does a self assured person often do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["acknowledgment", "focused", "know what time", "feel important", "trust himself"]}, "answerKey": "D"}
{"id": "801431167b8bff06b9870abe9721536b", "question": "He was very outgoing, for him making friends was no personal what?", "question_concept": "making friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["scary", "having friends", "good feeling", "conflict", "friendship"]}, "answerKey": "D"}
{"id": "85ebdd4f1a3c2ac900eee8e75e48ccaa", "question": "What do you feel when giving assistance to the needy?", "question_concept": "giving assistance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reward", "boredom", "pleasure", "happiness", "satisfaction"]}, "answerKey": "E"}
{"id": "db1eb157671109bbb9113b0f71a6b957", "question": "Paul wants carrots and doesn't need to drive anywhere. He gets them from where?", "question_concept": "carrots", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refrigerator", "store", "farmer's market", "supermarket", "dryer"]}, "answerKey": "A"}
{"id": "c02a3c2d4f726b9e1be99533a24a6ab4", "question": "He was a sloppy eater, so where did he leave a mess?", "question_concept": "mess", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sailboat", "desk", "closet", "table", "apartment"]}, "answerKey": "D"}
{"id": "3ed6391c539e6daa5b5fdb1b6d5d8ace", "question": "What does every person want?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["life partner", "larger house", "second chances", "money", "headache"]}, "answerKey": "A"}
{"id": "1db19a32a3edbff9981976dc9ec800ce", "question": "If a small flying animal picks up a string, where are they taking it?", "question_concept": "string", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bird's nest", "park", "guitar", "kite", "quark"]}, "answerKey": "A"}
{"id": "1e5a138b4c7d456c37abf4990b402bbe", "question": "He had no issue committing perjury, he had a what that he would get away with it?", "question_concept": "committing perjury", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["confidence", "go to jail", "telling lies", "lying", "manual"]}, "answerKey": "A"}
{"id": "9402864beae075392d2ee6c10115fc21", "question": "What could go to a tennis court?", "question_concept": "tennis court", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desert", "college campus", "recreational center", "athletic club", "park"]}, "answerKey": "D"}
{"id": "25136807f7b2e78b115698daa1677b4a", "question": "What could you use to fill a cup and then drink from it?", "question_concept": "cup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sand box", "kitchen cabinet", "waterfall", "water fountain", "table"]}, "answerKey": "D"}
{"id": "bc10bf2bfae26a2226823d42956f6cf0", "question": "The two played video games all night in the living room, he enjoyed visiting where?", "question_concept": "living room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["formal seating", "friend's house", "movies", "home", "apartment"]}, "answerKey": "B"}
{"id": "5a6559db6bae37e3a8af7350be212219", "question": "The weasel ran up away from danger, somebody joked only our first president could get him down from the what?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["washington dc", "ladder", "natural history museum", "cherry tree", "chicken coop"]}, "answerKey": "D"}
{"id": "7ae17f5aecacf18c94a47cc48deb6c36", "question": "If you were looking for a blowfish, you wouldn't look on dry land, you'd look in a what?", "question_concept": "blowfish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fish market", "jungle", "sea water", "body of water", "soup"]}, "answerKey": "D"}
{"id": "5d809e0ee19badc66071653630ea7c51", "question": "George checked the rotor of the Apache, which wasn't powered by internal combustion, but by what?", "question_concept": "rotor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jet engine", "helicopter", "electric motor", "rotator", "electrical circuit"]}, "answerKey": "A"}
{"id": "ad0943fc37034cd2b7e485021f8b1b8c", "question": "The poker dealer spread the flop of cards across the what?", "question_concept": "cards", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["players", "play games", "casino", "table", "toy store"]}, "answerKey": "D"}
{"id": "c2a8c6814ed3e207771cfc23b3b42cf1", "question": "Where is a salt shaker most often kept?", "question_concept": "saltshaker", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cruet", "table setting", "kitchen cupboard", "cabinet", "store"]}, "answerKey": "B"}
{"id": "0b52cc905fff0ca69a45e6353d10e401", "question": "Where would you put a dollar if you want to go to a store and buy something with it?", "question_concept": "dollar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cash drawer", "teh bank", "safety deposit box", "pocket", "piggy bank"]}, "answerKey": "D"}
{"id": "30d0c2006613eec41ae814d76c17a798", "question": "What room is likely to have a sideboard on the counter?", "question_concept": "sideboard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home", "serve food buffet", "dining room", "living room", "kitchen"]}, "answerKey": "E"}
{"id": "f7a6d0d816d14210f3af5dabe21bf804", "question": "What is unlikely to get bugs on its windshield due to bugs' inability to reach it when it is moving?", "question_concept": "windshield", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airplane", "scooter", "motorboat", "car", "motor vehicle"]}, "answerKey": "A"}
{"id": "c306ab28498b67c53decb9dde1d78bd5", "question": "What mall store sells jeans for a decent price?", "question_concept": "jeans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clothing store", "bedroom", "thrift store", "apartment", "gap"]}, "answerKey": "E"}
{"id": "637c710ec9582fd9b9e8eaa3f3fe83bb", "question": "Where can a bath towel be borrowed?", "question_concept": "towel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cupboard", "at hotel", "swimming pool", "clothes line", "backpack"]}, "answerKey": "B"}
{"id": "9ae52783d8fdb5cc2e8caa01542c3341", "question": "Why do people stop caring about their problems?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["no problems", "better themselves", "face problems", "learn from each other", "become disillusioned"]}, "answerKey": "E"}
{"id": "4f23829b96b38b5633ecc3325281726d", "question": "John rode on the plain until it reached the ocean and couldn't go any farther. What might he have bee on?", "question_concept": "plain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mountain", "fancy", "sandplain", "cliff", "gorge"]}, "answerKey": "D"}
{"id": "3fcdc0b03e3c8b10692d642676931f4b", "question": "They were never going to be big actors, but they all had passion for the local what?", "question_concept": "actors", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["theater", "opera", "show", "television", "blockbuster feature"]}, "answerKey": "A"}
{"id": "ddd606743cf71679438a85280f64593a", "question": "Where would you use a folding chair but not store one?", "question_concept": "folding chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beach", "city hall", "closet", "garage", "school"]}, "answerKey": "A"}
{"id": "420641003ba20b966887dfac684efb17", "question": "If you spend a long time shopping in uncomfortable shoes, you might develop what?", "question_concept": "shopping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tiredness", "calluses", "bankruptcy", "standing in line", "sleepyness"]}, "answerKey": "B"}
{"id": "064c3074a682893d49c3c5b4f1e89984", "question": "What does impeachment mean for the president?", "question_concept": "president", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vote", "election", "trouble", "board room", "corporation"]}, "answerKey": "C"}
{"id": "c640116ca6905d5256edadb616b3f76e", "question": "Noble citizen of the Roman empire believed those born with lower status were what to them?", "question_concept": "noble", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["loser", "ignoble", "peasant", "inferior", "plebeian"]}, "answerKey": "D"}
{"id": "35ad89c198d5d6311a71c993bb7b6cba", "question": "Spraining an ankle while playing baseball will cause what?", "question_concept": "playing baseball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["strikes", "eating", "injury", "sore muscles", "pain"]}, "answerKey": "E"}
{"id": "916bbd27545446ca5d83d07c10d013ea", "question": "John was traveling to a new city and took time to check out a business.  He noticed that its carpet was stained with sauces and ketchup. What type of business might that be?", "question_concept": "carpet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bedroom", "chair", "bowling alley", "at hotel", "restaurant"]}, "answerKey": "E"}
{"id": "e40fd2c17fe2cde4bd4af540d35fd518", "question": "If you have a condo in a Wisconsin city known for beer, where are you?", "question_concept": "condo", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "electrical circuit", "residential area", "suburbia", "milwaukee"]}, "answerKey": "E"}
{"id": "98a04457025f18c2287d5c610ff8000d", "question": "Where is hard to read note likely to be?", "question_concept": "note", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fridge", "sheet music", "desk", "bed", "medical chart"]}, "answerKey": "E"}
{"id": "f656a475f07d3adba9d1486eda8e834a", "question": "How does someone go about buying beer?", "question_concept": "buying beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have no money", "pants", "relaxation", "lose money", "spend money"]}, "answerKey": "E"}
{"id": "c865b3547c2a2e3c3916d7be6ab25752", "question": "If there is gum on your shoe where did it likely come from?", "question_concept": "gum", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelf", "movies", "sidewalk", "water fountain", "table"]}, "answerKey": "C"}
{"id": "abd30bab9b96f902fead5378d4f4a1e4", "question": "If a person isn't able to pay their bills what must they do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["know everything", "acknowledgment", "make more money", "throw a party", "spare time"]}, "answerKey": "C"}
{"id": "a4b44a986e7f9045432e20ea75611df4", "question": "What is main benefit to exercising?", "question_concept": "exercising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["losing weight", "healthy", "get in shape", "weight loss", "sweat"]}, "answerKey": "C"}
{"id": "1f492f556fae64f72ce36b6caa242dd0", "question": "Steve thought that it was possible, but he agreed that it was what?", "question_concept": "possible", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["no go", "unable", "unlikely", "impossibility", "cant do"]}, "answerKey": "C"}
{"id": "d0c67c7ae6f2361fe237110455127866", "question": "What region of a west coast U.S. city would you find a Japanese restaurant?", "question_concept": "japanese restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["california", "tokio", "downtown", "narnia", "large town"]}, "answerKey": "C"}
{"id": "7bb279e38a1c9eb47a0c7af979a131a2", "question": "What is a tactic used to interfere with learning about science?", "question_concept": "learning about science", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["repetition", "sense of wonder", "accidents", "intimidation", "increased knowledge"]}, "answerKey": "D"}
{"id": "3095078e4771053d9d5fa8d4f5f3dc38", "question": "What do people usually feel when falling in love?", "question_concept": "falling in love", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting married", "pain", "happiness", "getting married", "suffering"]}, "answerKey": "C"}
{"id": "b23edb651e623e5d1e03e8ed3937e8fc", "question": "The tiger was stuck in what animal prison where he got lazy and fat?", "question_concept": "tiger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jungle", "zoo", "kill", "india", "eat cake"]}, "answerKey": "B"}
{"id": "acf6b667e9353b1743b7c4f60a6a9017", "question": "What do parents tell a child to do on the weekend?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["study", "begin school", "go out to play", "row boat", "clean room"]}, "answerKey": "C"}
{"id": "15b090801256085ad465e74af47cbee9", "question": "Why are dogs often known as man's best friend?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["aggressive", "friendly", "very loyal", "found outside", "very smart"]}, "answerKey": "C"}
{"id": "790b3f583e9bc9424c771691ecc70c20", "question": "Where can you buy a two wheel transportation machine?", "question_concept": "wheel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boat", "michigan", "train station", "bicycle shop", "trunk of car"]}, "answerKey": "D"}
{"id": "22b8219d43a38a1130e0a35ece152337", "question": "Where might an alien use a vacuum?", "question_concept": "vacuum", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["space", "closet", "kitchen", "orbit", "container"]}, "answerKey": "A"}
{"id": "5d4233146435ab0ca211e8ac9bfce76f", "question": "Where do you buy condoms?", "question_concept": "condoms", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "sock drawer", "cd store", "medicine chest", "bedroom"]}, "answerKey": "A"}
{"id": "be737cd4db844574ef594442ce6c9453", "question": "What animal is known for being a follower?", "question_concept": "sheep", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["goat", "expensive", "lion", "wolf", "meadow"]}, "answerKey": "A"}
{"id": "550164b7cf4e03153484136f10122c70", "question": "The soldier was told to get to the rendezvous point, for there he was suppose to what?", "question_concept": "soldier", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fight enemy", "go to war", "fight for freedom", "wait for orders", "follow instructions"]}, "answerKey": "D"}
{"id": "a617eb4d27edea93e7fd630ce00c8219", "question": "If you want to kill someone you can do what to them with a gun?", "question_concept": "kill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sip through", "damnation", "shoot", "commit crime", "eat breakfast"]}, "answerKey": "C"}
{"id": "bd47827418d5b8d7fb3502a398644435", "question": "The hostess greeted the employees to the program, she then led them to their what?", "question_concept": "hostess", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["group people", "welcome guests", "occupations", "work room", "seat customer"]}, "answerKey": "D"}
{"id": "31487ab8b1e8f12e252590cc58bd19c2", "question": "Where is a likely place to store unused soap?", "question_concept": "soap", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabinet", "supermarket", "jail", "butt", "own home"]}, "answerKey": "A"}
{"id": "ce2fd94212243f843b3f357046051f57", "question": "Loss of someone you love can cause what kind of feeling in your heart?", "question_concept": "love", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["painful", "happy", "blind", "contagious", "bring joy"]}, "answerKey": "A"}
{"id": "f87f40db71a56b5beda3194550202dc9_1", "question": "Where in your home would you keep a ballpoint pen when not in use?", "question_concept": "ballpoint pen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["backpack", "bank", "desk drawer", "eat cake", "office desk"]}, "answerKey": "C"}
{"id": "0b25bbd9e9aa976655e1975e31331709", "question": "James was someone who was caught in his own delusions.  To him, the truth didn't do what what?", "question_concept": "truth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work to advantage", "matter to", "help", "free mind", "further knowledge"]}, "answerKey": "B"}
{"id": "925232b4c9bba945a38ac7ef0f15f8d0", "question": "He wanted to live somewhere were every yard was uniform in size and landscaping, where should he look for a house?", "question_concept": "yard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "three feet", "subdivision", "parking garage", "michigan"]}, "answerKey": "C"}
{"id": "3338109fcafaaa370c8900a53e1b3ed8", "question": "The flasks was used to distill elements, where was is being used?", "question_concept": "flask", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["laboratory", "chemistry lab", "coat pocket", "after hours speakeasy", "bordello"]}, "answerKey": "B"}
{"id": "e172a93c72d305ee8262a8deb00d9fc3", "question": "What was the man encouraged to do after he expressed his anger violently?", "question_concept": "anger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cool off", "punch", "illustrate point", "fight", "release energy"]}, "answerKey": "A"}
{"id": "f1c2e37abf17d9e4ad16eb40f966c79f", "question": "Where can a student learn to play a triangle?", "question_concept": "triangle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["math class", "math book", "in pythagorus' band", "orchestra", "music class"]}, "answerKey": "E"}
{"id": "d29252ddaf7c7ef491abcce342d7bb98", "question": "What do you need to do to use television if it is already turned on?", "question_concept": "use television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get wet", "open eyes", "kill", "plug in", "first turn on power"]}, "answerKey": "B"}
{"id": "8c3c6b34bdb650a6517bca3786406c99", "question": "The guys had a regular poker game, rather than going to the movies this what their what?", "question_concept": "playing poker", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["competition", "fun game", "losing money", "fun", "social event"]}, "answerKey": "E"}
{"id": "ff1bf2ec835c9df8695ae0cfb5281646", "question": "When you stroke a dogs fur what have you done?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["start fighting", "play", "lots of attention", "petted", "bone"]}, "answerKey": "D"}
{"id": "c7526b682e64f355384631b35cd78fc9", "question": "Dan fell off a bar stool.  He did this because he was what than ever before?", "question_concept": "bar stool", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "drunker", "tavern", "restaurant", "shorter"]}, "answerKey": "B"}
{"id": "0fba83d3997f048adcc31937221af77e", "question": "The wood was still rough to the touch, what did the woodworker have to do?", "question_concept": "wood", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["petrify", "sanded", "warp", "composted", "clean"]}, "answerKey": "B"}
{"id": "a5456dc611aa93b81d7ab6ed8e160f85", "question": "The chief saw his entire tribe wiped out, he was a leader with a single what?", "question_concept": "chief", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["peon", "indian", "minister", "follower", "employee"]}, "answerKey": "D"}
{"id": "11416df796f63d2f0dddc846b9c139d3", "question": "The flower grew tall to compete for sunlight, what did its neighbor do?", "question_concept": "flower", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["blossom", "park", "open", "cast shadow", "vase"]}, "answerKey": "D"}
{"id": "c908d7c4633c5e6add9463bdd47cb27e", "question": "If while driving to work another car makes a careless maneuver, what emotion might you feel?", "question_concept": "driving to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boredom", "happiness", "transportation cost", "getting there", "road rage"]}, "answerKey": "E"}
{"id": "7e522a60756f854c5331125f998bc36b", "question": "What kind of food makes someone sick?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boat", "necessary to live", "edible", "unhealthy", "kitchen"]}, "answerKey": "D"}
{"id": "f4a75bf3f115b826a8097edfd0ff2781", "question": "Where would you find the sharpest parts of a triangle?", "question_concept": "triangle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["three vertices", "point", "3 sides", "three sides", "math book"]}, "answerKey": "A"}
{"id": "02f43014a135cbd39f23b044c99de96e", "question": "How might a automobile get off a freeway?", "question_concept": "automobile", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exit ramp", "garage", "driveway", "repair shop", "stop light"]}, "answerKey": "A"}
{"id": "8cf478192696744b3427f7c109019af5", "question": "What does going to bed with your spouse for sex lead to?", "question_concept": "going to bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bad dreams", "a good nights sleep", "rest", "sleepiness", "get pregnant"]}, "answerKey": "E"}
{"id": "4ccd43cdff044bc4c644dadff1ff1e0b", "question": "What would it be if they get a surprising show over and over?", "question_concept": "surprising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["surprise", "fight", "annoyance", "might scare", "irritated"]}, "answerKey": "C"}
{"id": "7b7941b883328ad39048d4dfb1eb5623", "question": "Sally thought that competing wasn't worth the risk. If she pushed more what might happen?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pressure", "trying harder", "put harder", "enemies", "death"]}, "answerKey": "E"}
{"id": "008b7ba0c039f6d0d542c6c90aae173c", "question": "John is sitting in a toilet stall in a bathroom, outside he can hear cars going around in circles.  What is the function of the place he is most likely at?", "question_concept": "bathroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eating food", "public place", "race track", "at hotel", "public building"]}, "answerKey": "C"}
{"id": "4c968fa73699a38639ba3ffa1745bc21", "question": "What event might one buy tickets for seats?", "question_concept": "seats", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "show", "auditorium", "movies", "rest area"]}, "answerKey": "B"}
{"id": "b1d5cdbf8ef7b3954a6a352bd4df5866", "question": "The merchant wanted to open in a high-traffic space, where did he rent space?", "question_concept": "merchant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mall", "business", "store", "sale", "sell goods"]}, "answerKey": "A"}
{"id": "c3bc395561113c96ec43afd715da5061", "question": "The newlyweds began copulating their marriage, they wanted many what?", "question_concept": "copulating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["babies", "odors", "sadness", "rapport", "ejaculation"]}, "answerKey": "A"}
{"id": "d0bd5b5ee7319d1c4727e38d429dd54e", "question": "How does a planet usually move around the sun?", "question_concept": "planet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["writing", "universe", "outer space", "outerspace", "orbit"]}, "answerKey": "E"}
{"id": "81f5e741d970578867495ceea5a0c848", "question": "When a group of people are talking at work they might be doing what?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["having a concert.", "cough", "sharing of ideas", "speak", "sneeze"]}, "answerKey": "C"}
{"id": "6714593a8d1f8ae39930c1f0316e9ffc", "question": "What emotion leads to punching?", "question_concept": "punching", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fists", "hitting", "boxing gloves", "anger", "hands"]}, "answerKey": "D"}
{"id": "75cb55aec7e64f592c01eee5d4578dcd", "question": "They kept doing things the same, she suggested they also try doing things what?", "question_concept": "also", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["differently", "otherwise", "expensive", "only", "mere"]}, "answerKey": "A"}
{"id": "0b30831fb1862bc62339bdf930cbc447", "question": "Where could you find a shark before it was caught?", "question_concept": "shark", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pool hall", "tomales bay", "marine museum", "business", "desert"]}, "answerKey": "B"}
{"id": "29c194d032a266a7160bff6f546a4d9d", "question": "Where is one likely to find poker chips?", "question_concept": "chips", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "pantry", "motherboard", "bar", "bar"]}, "answerKey": "D"}
{"id": "ea33206992fb7ad1c3476e9673bb4a9c", "question": "Dance can be elegant and specific, or you can just have fun and what?", "question_concept": "dance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling down", "trip", "fall down", "move around", "celebrate"]}, "answerKey": "D"}
{"id": "2b7dd91da5dde1560ace2cd82af926de", "question": "Where can one obtain a bass fiddle?", "question_concept": "bass fiddle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jazz band", "string quartet", "group band", "nursery rhyme", "music store"]}, "answerKey": "E"}
{"id": "eb50f536830ba18ab987c7ff652e2aba", "question": "Why does having a disability sometimes making academic tasks hard for a person?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mentally challenged", "have choice", "lots of space", "hungry", "acknowledgment"]}, "answerKey": "A"}
{"id": "6bc3ebcfd04965c25bde71339955746c", "question": "What is the purpose of playing games for children?", "question_concept": "playing games", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["winning", "learning", "losing", "fatigue", "skill"]}, "answerKey": "B"}
{"id": "163898952cb6baf3a6440696e1352e86", "question": "If for some reason you were to start killing people, what would you be likely to receive?", "question_concept": "killing people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feelings of guilt", "prison sentence", "terrible", "encouragement", "die"]}, "answerKey": "B"}
{"id": "aa984e2b487d08889bc0c73bab5ac945", "question": "If someone laughs after surprising them they have a good sense of what?", "question_concept": "surprising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["joy", "fight", "frightened", "humor", "laughter"]}, "answerKey": "D"}
{"id": "d78baca23e0a636a8961e17119047e63", "question": "People played a variety of games in the soccer field.  It was the closest thing they had to what?", "question_concept": "soccer field", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["town", "beach", "park", "near", "outside"]}, "answerKey": "C"}
{"id": "ac6378b5e8462dc1bde1155d706213d8", "question": "What is likely to have a better school cafeteria?", "question_concept": "school cafeteria", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["high school", "canteen", "polytechnic", "large room", "all kinds of schools"]}, "answerKey": "C"}
{"id": "c1aebf059c5102f4e773f7fe4afe13f0", "question": "When someone has little knowledge and is judging someone they are considered what?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["objectivity", "knowing yourself", "experience", "ignorance", "introduction"]}, "answerKey": "D"}
{"id": "1017807310a25d3ea4a4ec305e91cba3", "question": "She wanted to get in shape, but she couldn't stay focused on the hour long what?", "question_concept": "get in shape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sweating", "excercise", "work out", "video", "swim"]}, "answerKey": "C"}
{"id": "7192c9f5c513aac9042bad595ff5af9f", "question": "When you do something and have fun, its something you?", "question_concept": "have fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spontaneous", "stop working", "pay for", "do like", "do enjoy"]}, "answerKey": "E"}
{"id": "7c05e8d5a057085455eea243fbd1cd90", "question": "What is a salesman responsible to do at work?", "question_concept": "salesman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["traveling to chicago", "get fired", "books", "sell products", "service account"]}, "answerKey": "D"}
{"id": "3cb91a71a6567da870eedf37becc97ef", "question": "How does going jogging generally affect one's self esteem?", "question_concept": "going jogging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel better", "feel pride", "sweating", "ocean", "arthritis"]}, "answerKey": "A"}
{"id": "9b4bbf3c4d24ecdb4b27320afb706808", "question": "Where would you find people standing in a line outside?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus depot", "end of line", "opera", "neighbor's house", "meeting"]}, "answerKey": "A"}
{"id": "43df3a316880d8bab346c06bd43b94dd", "question": "If you are committing perjury you have done what while under oath?", "question_concept": "committing perjury", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["crime", "disrespect judge", "embarrassment", "lie", "indictment"]}, "answerKey": "D"}
{"id": "858a5eaa587fe0e266722228671a6bd1", "question": "Where can you find the meaning of \"ficus\"?", "question_concept": "ficus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dictionary", "apartment", "libary", "middle east", "arboretum"]}, "answerKey": "A"}
{"id": "34005ef0caafefc8585c9fcd50e94557", "question": "When are people buying products more?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["debt", "economic boom", "being able to use", "disagreements", "trading"]}, "answerKey": "B"}
{"id": "f61d83f90b92a8d537989e55ee70542d", "question": "The buildings were intended to not have residential kitchens in them, what were they designed for?", "question_concept": "buildings", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["large city", "small", "eat cake", "university", "town"]}, "answerKey": "D"}
{"id": "3bf06235a537adc9d85431846595b800", "question": "Animals come in all types, some fly thanks to their lightweight hollow what?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tails", "bones", "eyes", "heads", "bodies"]}, "answerKey": "B"}
{"id": "79ec11d8072ce42779adfe0a19bd5374", "question": "The child felt like it was all pretend, he didn't understand what?", "question_concept": "pretend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["people believe", "daydreams", "transcendentalism", "laughter", "religion"]}, "answerKey": "E"}
{"id": "2982d0eae1bf880f5930341af7665716", "question": "Where is a lake likely to be glacial?", "question_concept": "lake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "new york", "new york", "mountains", "countryside"]}, "answerKey": "D"}
{"id": "ba9132ebf2bc3ad21e6a0631dc4e0a77", "question": "They needed grape juice for their party, they went to buy it and other snacks at the what?", "question_concept": "grape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["field", "restaurant", "salad", "market", "food store"]}, "answerKey": "E"}
{"id": "d06de16a4aaeaef32b398c1213257b4a", "question": "Why do some people get passports and go to different locations?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["believe in god", "smoke marijuana", "desire to travel", "use weapons", "throw away"]}, "answerKey": "C"}
{"id": "eee9476bf29498b7d74b043afe316fc6", "question": "Where do apples form on an apple tree?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["south africa", "sunshine", "new york", "bloom", "trunk"]}, "answerKey": "D"}
{"id": "a85441d6a0e3f871d81a9f19b31360b7", "question": "Where areas are there likely to be many nightclubs?", "question_concept": "nightclub", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["manhattan", "drink and dance", "alcohol", "major city", "downtown area"]}, "answerKey": "D"}
{"id": "f11a2975898033893d6a38f75d791fdf", "question": "What can machines do that humans cannot?", "question_concept": "machines", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fail to work", "perform work", "answering questions", "see work", "fly"]}, "answerKey": "E"}
{"id": "a2977fd575faba162d04a490dabd1b9b", "question": "What does someone stop doing when being dead?", "question_concept": "dead", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["moving", "working", "breathing", "alive", "deadworks"]}, "answerKey": "C"}
{"id": "cd39e442204d3edf7acc185fd59c8a44", "question": "The place where my linen closet is really needs repainting a light color as it only has one overhead light.", "question_concept": "linen closet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["house", "home", "pool house", "hallway", "bedroom"]}, "answerKey": "D"}
{"id": "c77e1039d78cdff197a370fcda0f2b9f", "question": "Punk rock music is an important part of what action sport?", "question_concept": "music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["skate", "listen", "opera", "opera", "relax"]}, "answerKey": "A"}
{"id": "f537f6bb8527724e0b1e1c1051326cd5", "question": "Where might a mouse be found to make it country?", "question_concept": "mouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "cook", "computer lab", "old barn", "research laboratory"]}, "answerKey": "D"}
{"id": "d3b145911a76fd6fbe9a23ab027be024", "question": "Where is a bird likely to make it's home?", "question_concept": "bird", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forest", "nest", "roof", "leaves", "sky"]}, "answerKey": "A"}
{"id": "dc2fa76467ff342abdb4cf142f92dddd", "question": "When a person suffers from hunger early in the day what do they do?", "question_concept": "hunger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat hamburger", "eat breakfast", "open fridge", "buy food", "cook dinner"]}, "answerKey": "B"}
{"id": "246249cd7976358051a9811ff9c30736", "question": "How would you express information if you do not have a pen or pencil?", "question_concept": "express information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["may disagree", "close mouth", "write down", "talk", "eyes"]}, "answerKey": "D"}
{"id": "32be8cbc1b5a967310bcab8b80563481", "question": "What does everyone feel of monsters?", "question_concept": "everyone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["looking for love", "afraid of", "good at", "make pet", "different"]}, "answerKey": "B"}
{"id": "ad769851a59375865607452d3bf2a45d", "question": "Why does someone want to examine thing closely?", "question_concept": "examine thing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy", "learn about", "buy", "complex", "interesting"]}, "answerKey": "B"}
{"id": "5ea6b94d1a911365b06cf776919413e8", "question": "What does \tdrinking alcohol lead to?", "question_concept": "drinking alcohol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have fun", "intoxication", "vomiting", "drinking more alcohol", "nausea"]}, "answerKey": "B"}
{"id": "820df15b615d221e38a71fcc44461085", "question": "Where would your hear a bass clarinet along side other wood wind instruments?", "question_concept": "bass clarinet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["opera house", "school band", "music store", "orchestra", "bathroom stall"]}, "answerKey": "D"}
{"id": "0a4a00ba435397c4a0496dd2c2426be7", "question": "What is the opposite of a little of something?", "question_concept": "little", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["much", "plenty", "more", "big", "lot of"]}, "answerKey": "E"}
{"id": "a7f29f4aebe0e3bcb77038fea71bf28c", "question": "The princess was pure, the evil wizard wished to do what to her?", "question_concept": "pure", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dirty", "tarnish", "corrupt", "contaminated", "applied"]}, "answerKey": "C"}
{"id": "ecd32cc0c17d4738a27bba3399f04591", "question": "The piece of paper was worth a lot of money, it was an old Apple Inc what?", "question_concept": "paper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["notebook", "copy machine", "stock certificate", "ream", "thumb drive"]}, "answerKey": "C"}
{"id": "8b2af2d865b7dc500427786c846eacaf", "question": "During the winter hunt he could hear every motion in the woods, this was because of the what of everything?", "question_concept": "motion", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["being still", "silence", "stationary", "stillness", "standing still"]}, "answerKey": "D"}
{"id": "383282aace64dd49138bac2392f8b38e", "question": "If a car-less person want to listen to talk radio in private, where might they listen to it?", "question_concept": "radio", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["trunk", "bedroom", "diner", "space shuttle", "shop"]}, "answerKey": "B"}
{"id": "eaf6838d29bcd4ebf408da2f75aa65c3", "question": "Billy was an astronaut.  When he looked at the world from space, how did it look?", "question_concept": "world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["diverse", "round", "square", "orange", "complicated"]}, "answerKey": "B"}
{"id": "7c8bc9c0e56389eef033bca40c88c151", "question": "Where is a good place to have a fireplace in a house?", "question_concept": "fireplace", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["big house", "train", "cabin", "living room", "home"]}, "answerKey": "D"}
{"id": "ca60a46c9007e4b6213f50bfb5342fdd", "question": "If you own a cat where is the last place you'd want to find it?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["trouble", "dog's mouth", "backyard", "nature", "home"]}, "answerKey": "B"}
{"id": "f50209f04d11690d7c8f30e29b35ff02", "question": "Where would you find a kosher deli along side a number of different places to eat?", "question_concept": "kosher deli", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["los angeles", "food court", "new york city", "jewish community", "jewish neighborhoods"]}, "answerKey": "B"}
{"id": "d725f1c2e150a3221de31612123f3f46", "question": "What do you do when you're going to market?", "question_concept": "going to market", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy food", "see other people", "buying vegetables", "buy a fat pig", "traveling"]}, "answerKey": "A"}
{"id": "f7735d721dfdc94621154951d4eaa4cf", "question": "She feared that she had cancer, but upon discovering truth that she hadn't, what was her attitude toward life?", "question_concept": "discovering truth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["conclusion", "pain", "happiness", "relief", "boring"]}, "answerKey": "C"}
{"id": "eaf980db7e945b1cf6d648fa55ddcb5e", "question": "What is the feeling of one having fun?", "question_concept": "having fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["smiling", "pleasure", "hurt", "injuries", "laughter"]}, "answerKey": "B"}
{"id": "8bbfe8cd056d612e9d3190f278bef287", "question": "If I keep getting crumbs under my table, what should I put under it?", "question_concept": "table", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["conference", "neighbor's house", "rug", "net", "card room"]}, "answerKey": "C"}
{"id": "aa7c4c351cf8d59792aa68e3de339db4", "question": "Christians believe you will go to heaven if you're what?", "question_concept": "dying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unable to work", "born again", "change of color", "dead", "no longer exist"]}, "answerKey": "B"}
{"id": "23df3bac9cfcb156f4cfd8a05f21c5e2", "question": "James loved to surf but he wasn't good at it. He would always do what?", "question_concept": "surf", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wipe out", "enjoy yourself", "start fighting", "get wet", "drown"]}, "answerKey": "A"}
{"id": "d21777d771dc6fd08e769d378651817e", "question": "Sarah gave her brother a guy to her home.  While she was gone, he used it to do what?", "question_concept": "key", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["front door", "turn lock", "solution to problem", "install", "open doors"]}, "answerKey": "E"}
{"id": "611a4cc0e288b8a11afa923f48cb2ab4", "question": "When did mammoth's live?", "question_concept": "mammoth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boscage", "forest", "prehistory", "prehistoric times", "ancient times"]}, "answerKey": "E"}
{"id": "8e7941ce31996ca83cc0a68f7313c96d", "question": "After killing people, the murderer went to church after feeling what?", "question_concept": "killing people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["murder", "remorse", "religious", "retaliation", "anguish"]}, "answerKey": "B"}
{"id": "ea02772e27f5bd40eced3b65e8c6427f", "question": "What might result in an unsuccessful suicide attempt?", "question_concept": "committing suicide", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["die", "interruption", "bleed", "hatred", "dying"]}, "answerKey": "B"}
{"id": "de54d03e69d9765872f95ff06ed21499", "question": "What can happen if you are buying products that someone else does not want you to buy?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["joy", "disagreements", "agony", "pleasure", "owning"]}, "answerKey": "B"}
{"id": "b231a732a3fdf0621391e7e385f8d651", "question": "The child was getting many gifts for his birthday, his father reminded him to do what after opening each one?", "question_concept": "getting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["show appreciation", "asking for", "exchanging", "say thank", "smile"]}, "answerKey": "D"}
{"id": "b9121c3228f961c5ad68958c702cd94b", "question": "Bob stands in the grass surrounded by trees and nature, where is Bob?", "question_concept": "grass", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rest area", "desert", "state park", "fairgrounds", "soccer game"]}, "answerKey": "C"}
{"id": "4015ab002ff8c233d1c7ef26f5156b88", "question": "Bart entered his horse into the contest.  Where did he do this?", "question_concept": "horse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["circus", "in kentucky", "western movie", "central park", "state fair"]}, "answerKey": "E"}
{"id": "0197ade3bb26d163ab2e284c960c626f", "question": "From where does a snowflake form?", "question_concept": "snowflake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cloud", "snow storm", "billow", "air", "snowstorm"]}, "answerKey": "A"}
{"id": "a90f9197a13c64089c9ba95bcba275ad", "question": "All the power tools like the drill used for fixing cars made for a very loud workplace where?", "question_concept": "drill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["basement", "work shop", "tool shed", "repair shop", "store room"]}, "answerKey": "D"}
{"id": "684204df916cc58d47293960f9c6ed9f", "question": "Applying for a job can make someone feel what sort of emotion, even if they get it?", "question_concept": "applying for job", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["working hard", "frustration", "rejection", "defeat", "stress"]}, "answerKey": "E"}
{"id": "a2aa95861ef74bf1ecfc55db505e3982", "question": "A farmer sees a weasel in the woods, where is the farmer?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chicken coop", "beach", "fairytale", "great outdoors", "corn fields"]}, "answerKey": "D"}
{"id": "8555dd9667d010018961a2f7d1c22704", "question": "He picked up the perfect pebble, he planned to skip it across the entire small what?", "question_concept": "pebble", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["manual", "lake", "aquarium", "pond", "playground"]}, "answerKey": "D"}
{"id": "84a761f516efce04ab27d7ca8dd25255", "question": "Traveling from new place to new place is likely to be what?", "question_concept": "traveling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["going somewhere", "exhilarating", "diarrhea", "relocation", "exhausting"]}, "answerKey": "B"}
{"id": "45a6becd307342669d9d17474e50b97a", "question": "Turkey only has a small northern part of their country located in part of the what?", "question_concept": "turkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["middle east", "oven", "balkan peninsula", "provide meat", "asia minor"]}, "answerKey": "C"}
{"id": "c509c499bace6de324b39c0d4d0c30fa", "question": "Where might someone store a reusable shopping bag?", "question_concept": "shopping bag", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "home", "mart", "obesity", "closet"]}, "answerKey": "E"}
{"id": "77ddc9134bb27f9962aa2ed5ec5a5ef9", "question": "How could you have fun by yourself with no one around you?", "question_concept": "fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fairgrounds", "watching television", "tired", "enjoyable", "friend's house"]}, "answerKey": "B"}
{"id": "715583129369c0c5c9f499c93a1c095e", "question": "The potato might be the official vegetable of what?", "question_concept": "potato", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vegans", "kitchen cupboard", "restaurants", "chicken", "maryland"]}, "answerKey": "E"}
{"id": "a478e8b7c049781574f7fbb11ba1eec0", "question": "Where is the sky most beautiful?", "question_concept": "sky", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["planetarium", "outdoors", "atmosphere", "night", "photo"]}, "answerKey": "B"}
{"id": "f427f9de6bf580314531baf86de8acbc", "question": "What type of fruit is easily broken in to sections?", "question_concept": "section", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slide", "citrus", "band", "orchestra", "coconut"]}, "answerKey": "B"}
{"id": "0f7425ecbe369bf41a230aab92d84132", "question": "Marathoners feel fatigued after running twenty six miles, but some that have pushed them self too hard might be prone to what?", "question_concept": "running twenty six miles", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["excruciating pain", "passing out", "death", "drunk", "exhaustion"]}, "answerKey": "B"}
{"id": "c872c08a95dd28a16479b76f240a4ad5", "question": "Billy liked driving cars.  He was good at it.  But he was rattled ever since his father experienced what?", "question_concept": "driving car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["transportation", "pollution", "stress", "death", "go somewhere"]}, "answerKey": "D"}
{"id": "08d908ed723f813574992195d61386a2", "question": "I am cold, what should I do to stay warm?", "question_concept": "cold", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stay in bed", "light fire", "freezer", "lay on ice", "spit"]}, "answerKey": "B"}
{"id": "5365fd00ef8cec62ee5685e246a939db", "question": "Copulating with the wrong partner may be ill advised, many diseases can be transferred that can cause different types of what?", "question_concept": "copulating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["intense pleasure", "ejaculation", "period of rest", "enjoyment", "skin irritation"]}, "answerKey": "E"}
{"id": "5649bd90dbb57e223fd843b7a4563a0f", "question": "What do audiences clap for?", "question_concept": "audience", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cinema", "theatre", "movies", "show", "hockey game"]}, "answerKey": "D"}
{"id": "0a2195ae8d4706abc5721578c9991466", "question": "Where would you get a balalaika if you do not have one?", "question_concept": "balalaika", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["orchestra", "music store", "buy music", "make music", "symphony"]}, "answerKey": "B"}
{"id": "5d15989039d46156b417c149728591de", "question": "Hoping for a beautiful day, what did the clouds do that disappointed everyone?", "question_concept": "beautiful", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["homely", "overcast", "hideous", "overrated", "misshapen"]}, "answerKey": "B"}
{"id": "6eb57102b44ab74163d8f9821cbdabd0", "question": "What type of demands to the unions need to be making to go off strike?", "question_concept": "go off strike", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reasonable", "more money", "not go to work", "return to work", "union"]}, "answerKey": "A"}
{"id": "63861ac5e633db9090704ae315ef6f93", "question": "The landscaper was carefully arching stones together, he was creating an elaborate what over the creek?", "question_concept": "stones", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "made from rocks", "balloon", "field", "bridge"]}, "answerKey": "E"}
{"id": "8058c566a4f488033d00e6520b17caea", "question": "John was not happy with his marriage. He and his wife drifted apart.     All and all, recent turns could be described as what?", "question_concept": "happy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["inappropriate", "sadness", "unsatisfied", "unfortunate", "disenchanted"]}, "answerKey": "D"}
{"id": "57b83653d82b27d32bc39228130f3516", "question": "The poor girls needed a light to see, what was the relationship between that light and finishing her homework?", "question_concept": "light", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["darkness", "cumbersome", "obesity", "forceful", "crucial"]}, "answerKey": "E"}
{"id": "410f907f817dd7aa8e73291a918d3d86", "question": "Where would you find a ticket booth and see a concert?", "question_concept": "ticket booth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clerk", "indoors", "movie theater", "venue", "auditorium"]}, "answerKey": "D"}
{"id": "506c2dbfe7b00a82bfdf0507a8de88fb", "question": "Who is not famous for a superhighway with no speed limit?", "question_concept": "superhighway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["europe", "germany", "industrialized country", "city", "america"]}, "answerKey": "E"}
{"id": "42520bf3f93f8de23670044e019001a3", "question": "The low trickle of water revealed a stone, where was the stone found?", "question_concept": "stone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "gallbladder", "driveway", "river bed", "creek bed"]}, "answerKey": "E"}
{"id": "5e260e1d96187716888cbd968010bb65", "question": "Where is the closest place from where you could borrow salt?", "question_concept": "salt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean water", "table", "shaker", "neighbor's house", "lake"]}, "answerKey": "D"}
{"id": "ed50555f8db2b8f66caf9868dcd7e13b", "question": "No matter what date you put on it, we all know the universe to be what?", "question_concept": "universe", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["very old", "infiniverse", "getting younger", "infinite", "real"]}, "answerKey": "A"}
{"id": "a8c284637dabc87745a7eb05d4f7fcbc", "question": "A meteor travels through galaxies which are a part of what?", "question_concept": "meteor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["republic of ireland", "sky", "orbit", "universe", "school"]}, "answerKey": "D"}
{"id": "5758a0fb686071e95d95b1cfad5299a0", "question": "What is a person considered a bully known for?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ridiculous", "false information", "made fun of", "brain tumor", "bull rider"]}, "answerKey": "C"}
{"id": "d986f17acb3ed19c77e3ca3f98c026b9", "question": "She had an interest in the man, what did she want to do with him?", "question_concept": "interest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["see particular program", "see exhibits", "see people play game", "have conversation", "watch film"]}, "answerKey": "D"}
{"id": "4a4f6408fae400ce0beb5bea0f9913e9", "question": "Where is a drug kept in a home bathroom?", "question_concept": "drug", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nursery", "ghetto", "cupboard", "pharmacy", "medicine cabinet"]}, "answerKey": "E"}
{"id": "8c655f3a55bde41aad880f138d7a445d", "question": "When cooking sheep meat a lot of people might want to be well?", "question_concept": "sheep", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ram", "lamb", "done", "ram", "wolf"]}, "answerKey": "C"}
{"id": "56417ee33b44f0d916bedfb6fd99b0ec", "question": "Where would you sit in a chair while working toward an advanced degree?", "question_concept": "chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["aeroport", "church", "furniture store", "university", "living room"]}, "answerKey": "D"}
{"id": "43fb083962f825ae651d88648bbd2f74", "question": "Farm land makes use of what?", "question_concept": "farmland", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["countryside", "michigan", "north dakota", "farming areas", "illinois"]}, "answerKey": "D"}
{"id": "aed771629c8dbd0c2587891e98030607", "question": "A good interview after applying for a job may cause you to feel what?", "question_concept": "applying for job", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["offer", "income", "rejection", "hostile", "hope"]}, "answerKey": "E"}
{"id": "d0a42c8180b4e080aa071dd70fce7e03", "question": "Computers have allowed everybody to answer questions they have quickly, but still we seem to be getting duller despite access to this what?", "question_concept": "computers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["economic boom", "advance knowledge", "produce sound", "teach", "follow instructions"]}, "answerKey": "B"}
{"id": "533599262a5dae7c7137cfe69e0e24fb", "question": "There was a long cottage somewhere.  People thought it was haunted.  It was overgrown, there was nothing near it.  It's was far into the what?", "question_concept": "cottage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mountains", "countryside", "train", "painting", "village"]}, "answerKey": "B"}
{"id": "edd1634d911614590c6b8ca730df95fe", "question": "Where is knight always portrayed as a hero?", "question_concept": "knight", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["middle ages", "chess board", "kids story", "fairy tale", "castle"]}, "answerKey": "D"}
{"id": "9a544e9f4847c41a15fdf47ae7b98d8a", "question": "James is carrying a duffel bag with him because he doesn't have a vehicle of his own and needs a bag to carry his things in while he uses what?", "question_concept": "duffel bag", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["library", "transit", "bus station", "army barracks", "locker room"]}, "answerKey": "B"}
{"id": "26bd85f05d29863ed777a4f1a4b8fa63", "question": "What would you need if you want to smoke?", "question_concept": "smoke", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["you're stupid", "kill yourself", "roll joint", "cigarette", "lighter fluid."]}, "answerKey": "D"}
{"id": "3884d82524f2337ce53ce64776293cf7", "question": "James decided that competing was the right choice.   Not competing has a defined outcome, but if he competes then what could happen?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["might win", "perform better", "enemies", "winners and losers", "lose"]}, "answerKey": "A"}
{"id": "acb3147d946db3b06a596d48e0be56cf", "question": "What could you use to get to some airplanes?", "question_concept": "airplanes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["taxi", "carry people", "car", "stall", "crash"]}, "answerKey": "A"}
{"id": "52ab95f9216f1994e37cc08f7f258f13", "question": "What happens when driving?", "question_concept": "driving", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lack of fuel", "paint scratching", "wheels turning", "tire wear", "traffic accident"]}, "answerKey": "C"}
{"id": "f60641f550d5ee44ac1bedcaf6ad6357", "question": "What are our bodies doing after having food?", "question_concept": "having food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling down", "digesting", "gas", "weight gain", "not hungry"]}, "answerKey": "B"}
{"id": "d9835ede7a0ed79325de13ca95b85b78", "question": "Why would one try to avoid work?", "question_concept": "going to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["making money", "leave home", "success", "malaise", "bad mood"]}, "answerKey": "D"}
{"id": "2987db72e66f5fa0015ac64f9b3614ec", "question": "What do you do in order to fly in airplane?", "question_concept": "fly in airplane", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy tickets", "passenger", "read", "add gas", "run through checklists"]}, "answerKey": "A"}
{"id": "8b548832703a8c68a788e2f9c0e222ae", "question": "What is another name for the color of the fur of a dog with light colored fur?", "question_concept": "small dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fair", "basket", "dog hair", "game", "sun"]}, "answerKey": "A"}
{"id": "1ddd239a2a6438a891cb411b82e7f450", "question": "Sally was bored because she didn't like the junk that was on what?", "question_concept": "junk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "garage", "caddy", "bed", "television"]}, "answerKey": "E"}
{"id": "6544a50bf9563d52dbd2034e81df0bf3", "question": "The lion sensed his competitor was timid, so what attitude did the lion take?", "question_concept": "timid", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reckless", "bellicose", "defensive", "aggressive", "dauntless"]}, "answerKey": "D"}
{"id": "5ff6ce8ad88459272ffe23d33db4970a", "question": "John felt a snake slither over him as he rested.  He was afraid to raise his covers for fear of startling it.  Where might he be?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pet shops", "oklahoma", "basement", "bedroom", "dreams"]}, "answerKey": "D"}
{"id": "2ca05683157a3cd89d82016f13e560ec", "question": "Where can you find a place to eat in an urban area close to local nightlife?", "question_concept": "place to eat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "downtown", "mall", "shopping center", "own house"]}, "answerKey": "B"}
{"id": "1a8fbab20bbdf0bbf3961894662d5f7c", "question": "You have to a lot of thinking while studying a new subject, but it is how you gain what?", "question_concept": "thinking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fatigue", "depression", "best way", "weight", "knowledge"]}, "answerKey": "E"}
{"id": "5b5d2a8b83282f61c68a870116042f64", "question": "How will you communicate if you are far away from who you want to communicate with?", "question_concept": "communicate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["think", "talk with people", "talk to people", "speak out", "send email"]}, "answerKey": "E"}
{"id": "cfa081b5ba90dae4d7ddb5b7ad9d369a", "question": "Why would you not trust your friends after chatting with friends?", "question_concept": "chatting with friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fever", "smoke", "laughing", "coughing", "lie"]}, "answerKey": "E"}
{"id": "009a7aabffe0583fc2df46656b29c326", "question": "He came from old money and had a fortune, but he made new money making shrewd trades where?", "question_concept": "fortune", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat cake", "cookie", "stock market", "real estate", "treasure chest"]}, "answerKey": "C"}
{"id": "2521b3fe6bfd6aeb91f9107dc7c4fbee", "question": "Animals make up a large part of the?", "question_concept": "animal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["carrying cargo", "favorite", "ecosystem", "nature", "ecology"]}, "answerKey": "C"}
{"id": "3fe45ab3bd4a844ea290050fc0ece8c1_1", "question": "At a shop what can you buy to put your spare unused things?", "question_concept": "shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["basement", "cardboard box", "ocean floor", "high school", "container"]}, "answerKey": "E"}
{"id": "a2e0f6b5651e5271fcff8d6f5c9adfee", "question": "A person with digestion issues eats a meat-filled breakfast, what does he feel?", "question_concept": "eating breakfast", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["heartburn", "overeating", "happiness", "being satisfied", "gain energy"]}, "answerKey": "A"}
{"id": "d6900a01a9dd6627b4bb22b0f6d191a5", "question": "What is a prisoner sentenced to do?", "question_concept": "prisoner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["scape jail", "dream of freedom", "become a hairdresser", "attempt to escape", "do time"]}, "answerKey": "E"}
{"id": "8f2976690c83be6b8fa3a1196dfd9722", "question": "Jesse  enjoyed remembering the past because he helped him understand it.  And understanding the past helped him with doing what?", "question_concept": "remembering", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["phoning", "nostalgia", "writing down", "active", "being prepared"]}, "answerKey": "E"}
{"id": "570be8c1edb8c638603dc5c8cae421cc", "question": "David watched some nesting birds using his binoculars while on vacation.  Where might David be?.", "question_concept": "birds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sky", "vaccation", "forest", "countryside", "roof"]}, "answerKey": "C"}
{"id": "08d3175de59a639be02f2ebc032d56bd", "question": "Where would you find many varieties of plants including a rosebush?", "question_concept": "rosebush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kew gardens", "garder", "backyard", "shop", "beautiful garden"]}, "answerKey": "E"}
{"id": "549cf641318edfc0510fa7c7dbb359e1", "question": "If I did not have a rosebush, where would I get one?", "question_concept": "rosebush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pot", "museum", "garden center", "formal garden", "backyard"]}, "answerKey": "C"}
{"id": "dfa23d3422b7294843447b6950d2b476", "question": "What does a person with a what likely do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel important", "trust himself", "own house", "electrical circuit", "know what time"]}, "answerKey": "E"}
{"id": "1fe90a4aee405e1aa2279442d28803ae", "question": "What are cats often known for?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["whiskers", "sharp teeth", "purr", "four legs", "sharp claws"]}, "answerKey": "E"}
{"id": "01794dde3ca2991615f1aa2f63fb22e3", "question": "As he looked out the window, he knew the landing was happening soon, and it made him nervous, but where would he be soon?", "question_concept": "landing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment building", "disembark", "stairwell", "deplane", "airport"]}, "answerKey": "E"}
{"id": "f794e376672c98ac25d8f70506a26e68", "question": "Where can you find a dogs house?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["found outside", "faithful", "frightening", "cold", "four legs"]}, "answerKey": "A"}
{"id": "ace8fa2943ba8414aebdb74b48906fae", "question": "Tweed is a rare fabric in modern clothing, what brand should I look for when buying it?", "question_concept": "tweed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["scotland", "brown", "fabric store", "clothing stores", "eddie bauer"]}, "answerKey": "E"}
{"id": "21ce6f7c5c3d1ad8cf234988c1ad471f", "question": "If you really wanted a grape, where would you go to get it?", "question_concept": "grape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["winery", "fruit stand", "field", "kitchen", "food"]}, "answerKey": "B"}
{"id": "6c84e79d0595efd99596faa07c4961d0", "question": "What would you do to a rock when climb up a cliff?", "question_concept": "climb", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grab", "look down", "throw", "falling", "may fall"]}, "answerKey": "A"}
{"id": "88f1fe6cfbcb1a25f25454341c789463", "question": "His compressor needed a new hose, where did he go?", "question_concept": "hose", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garden shed", "hardware store", "brothel", "garage", "greenhouse"]}, "answerKey": "B"}
{"id": "5074bcaf0f700c9f3c8c563067af156a", "question": "The man closed his eyes as the music played, what effect did the music have?", "question_concept": "music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coma", "enjoyable", "soothing", "universal", "good or bad"]}, "answerKey": "C"}
{"id": "6a253e076cd2af00e17d9950d70daf47", "question": "Setting up framing, truss and beam are some of the first steps in what?", "question_concept": "beam", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new construction", "warehouse", "driving", "ceiling", "bridge"]}, "answerKey": "A"}
{"id": "5af7c7860e3be61d4cfd814cc109f9d9", "question": "What is another name for a disk for storing information?", "question_concept": "disk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["computer store", "computer to store data", "computer hard drive", "cd player", "usb mouse"]}, "answerKey": "C"}
