{"id": "90b30172e645ff91f7171a048582eb8b", "question": "The townhouse was a hard sell for the realtor, it was right next to a high rise what?", "question_concept": "townhouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["suburban development", "apartment building", "bus stop", "michigan", "suburbs"]}, "answerKey": ""}
{"id": "000990552527b1353f98f1e1a7dfc643", "question": "There is a star at the center of what group of celestial bodies?", "question_concept": "star", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hollywood", "skyline", "outer space", "constellation", "solar system"]}, "answerKey": ""}
{"id": "dca0f2859f3c3dd43a9b2bfeff4936a8", "question": "What were the kids doing as they looked up at the sky and clouds?", "question_concept": "kids", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ponder", "become adults", "wonder about", "open door", "distracting"]}, "answer<PERSON>ey": ""}
{"id": "8795a949b39702af0e452c9e1229046d", "question": "The person taught an advanced class only for who?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["own house", "own self", "wonderful memories", "know truth", "intelligent children"]}, "answerKey": ""}
{"id": "1f74ea1f73b9f5d91a665b4d90218a6e", "question": "What is a likely consequence of ignorance of rules?", "question_concept": "ignorance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["find truth", "hostility", "bliss", "accidents", "damage"]}, "answerKey": ""}
{"id": "0b7734f608c188350573247e3ef2a00d", "question": "After graduating the dentist set up his dental office back where he grew up, he wanted to always live in his home what?", "question_concept": "dental office", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neighborhood", "town", "street", "office building", "city"]}, "answerKey": ""}
{"id": "d8d5e97e8e7f90712a81b14aee6f3627", "question": "Something that has a long and sharp blade is a?", "question_concept": "blade", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cup", "fan", "chuck", "sword", "spatula"]}, "answerKey": ""}
{"id": "8d916be530b91e6269b1d475601ae7ab", "question": "What will you experience after doing housework for a long time?", "question_concept": "doing housework", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sneezing", "satisfaction", "tiredness", "backache", "get tired"]}, "answerKey": ""}
{"id": "04919ff8acd9c71a0d7f1383255512b3", "question": "What might a successful writing program cause?", "question_concept": "writing program", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bugs", "frustration", "need to integrate", "loop", "satisfaction"]}, "answerKey": ""}
{"id": "a1f4dfbe9a3f49d4a84c2283e15d4c99", "question": "The man wanted to telegram the check, so where did he place it?", "question_concept": "check", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wallet", "pay envelope", "bedside table", "desk drawer", "cash register"]}, "answerKey": ""}
{"id": "5929f5704637184dc3390dd6964cacca", "question": "John was worried when a sink hold opened in his yard.  It if was any bigger it might have swallowed his what?", "question_concept": "sink", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home", "neighbor's house", "laundry room", "car", "apartment"]}, "answerKey": ""}
{"id": "b761674a4096d85a7f548604e8ca4f92", "question": "The bird wanted to fly, but couldn't, where was it?", "question_concept": "bird", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cage", "box", "nest", "countryside", "roof"]}, "answerKey": ""}
{"id": "6816062213d9298fbe40876b1be4e634", "question": "Stan studied the underground map carefully.  He never took this route before and needed to know where to go.  What was he looking for on the map?", "question_concept": "underground map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bathroom", "library", "super market", "subway station", "county engineer's office"]}, "answerKey": ""}
{"id": "1891bccf27335dbc43201c9790e0996e", "question": "Where do most people keep their cup of coffee while working?", "question_concept": "cup of coffee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "purse", "table", "coffee shop", "mexico"]}, "answerKey": ""}
{"id": "6917399ea434e6c484459f895c72ef90", "question": "What kind of well is likely to create controversy?", "question_concept": "well", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kansas", "ground", "oil field", "countryside", "dry"]}, "answerKey": ""}
{"id": "a11bcfa54ff513de5a642a566b4c206c", "question": "If a clock is not ticking, what is its likely status?", "question_concept": "clock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stop working", "dead batteries", "fail to work", "time event", "working correctly"]}, "answerKey": ""}
{"id": "c7c5b885f47cc8889ae4dffa5bf77b14", "question": "After the tussle everybody was cooling off, the person who stopped the fight told them to just what?", "question_concept": "cooling off", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chills", "calm down", "better decisions", "dance", "revenge"]}, "answerKey": ""}
{"id": "f15c3dd96d02bc6424e9ca888ebbb621", "question": "What would you do if you have a love of music involving strings and you do not have an electronics near you?", "question_concept": "love of music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play piano", "attend classical concert", "play violin", "listen to radio", "go to opera"]}, "answerKey": ""}
{"id": "5f51c728ff1968db2e684335dcc72c6d", "question": "He had to put the dough in the oven, what did he want to do with that dough?", "question_concept": "oven", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["baking food", "cool temperature", "field", "roast", "bake"]}, "answerKey": ""}
{"id": "fd2f6692cc27cce4c94997b8d4ef7987", "question": "When someone doesn't know how to skate well, they normally do what to stay up?", "question_concept": "skate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spin", "romance", "hold hands", "fall down", "grab side railing"]}, "answerKey": ""}
{"id": "eb252ee1a725a738d8c956caec8791fd", "question": "If I am at a soccer field, and hear small children laughing and screaming nearby, where am I?", "question_concept": "soccer field", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["playground", "backyard", "countryside", "mexico", "park"]}, "answerKey": ""}
{"id": "6441acd650f06440079c9ccba488a2d3", "question": "After eating dinner, he threw it up due to being poisoned, what happened afterwards?", "question_concept": "eating dinner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["heartburn", "feel better", "tummy ache", "indigestion", "illness"]}, "answerKey": ""}
{"id": "a2bfdb13e8f6f6945b827c71ab350c99", "question": "When a person is breathing in a paper bag what are they trying to do?", "question_concept": "breathing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["warm air", "continue to live", "going to sleep", "hyperventilation", "stay alive"]}, "answerKey": ""}
{"id": "99ee95c1d5a7a8e4409b6a17137dbf66", "question": "Where would you look to find out what a beaver is?", "question_concept": "beaver", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["australia", "countryside", "zoo", "tannery", "dictionary"]}, "answerKey": ""}
{"id": "9b0027a8bdbb4aa5e33cc75bd84aa0b5", "question": "What group of people's homes will usually have a grocery store?", "question_concept": "grocery store", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["town", "strip mall", "street", "neighborhood", "strip mall"]}, "answerKey": ""}
{"id": "9d2f3cfa032655fb72ade38941def326", "question": "Death came in the form of an airplane crash, how did it occur?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["happen quickly", "happen to", "last forever", "bring sorrow", "sad"]}, "answerKey": ""}
{"id": "df4368b976f1f7d2e2ea4807d4c4c045", "question": "I was in a swimming pool next to the Hollywood sign, where was I?", "question_concept": "swimming pool", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["california", "home", "resort hotel", "ymca", "motel"]}, "answerKey": ""}
{"id": "6843480c5c05eca606b83e9c925da43a", "question": "Sam found himself in a small churchyard surrounded by tall buildings.  Where might he have been?", "question_concept": "churchyard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["valley", "maine", "village", "england", "city"]}, "answerKey": ""}
{"id": "c4bf68163c4cf8cf16dd8ed00c906b28", "question": "What has more than four walls inside of it and might contain a bookcase?", "question_concept": "bookcase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["public library", "house", "display nice clock", "den", "study"]}, "answerKey": ""}
{"id": "d1e3039e1ada479354bfa866b3870822", "question": "Where do crumbs end up after they fall of your plate?", "question_concept": "crumbs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "floor", "breadbox", "box of crackers", "rug"]}, "answerKey": ""}
{"id": "b870c8326fcd43bebec50b7a0c7a46eb", "question": "What could happen to a soul after death?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ghost", "burial", "rebirth", "decomposition", "sadness"]}, "answerKey": ""}
{"id": "23a30f6f1c659a84ab25c84d3385d905", "question": "Where would you get silicone if you do not have any?", "question_concept": "silicone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["contact lens", "tube", "many man made items", "breast implants", "hardware store"]}, "answerKey": ""}
{"id": "dbf107a805e2decef2dee35879a15537", "question": "The office used a lot of paper, they were constantly running out of it using the what?", "question_concept": "paper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["notebook", "ream", "fax machine", "copy machine", "stock certificate"]}, "answerKey": ""}
{"id": "75198a8f06a80f2008fc58eaab627bec", "question": "Printed news struggled in the modern era, with the instant internet the newspapers just felt like what?", "question_concept": "news", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relevant", "propaganda", "old information", "fiction", "park"]}, "answerKey": ""}
{"id": "2fef162dd803ab164e0b1b94821b9dd8", "question": "She was really wound up about the test, her roommate told her to relax and what?", "question_concept": "relax", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stretch out", "stop worrying", "go to bed", "have tea", "listen to music"]}, "answerKey": ""}
{"id": "3f930ccb2f4e3aa1dd5be9f2d38b8b48", "question": "The child's wild imagination made him able to see story that he read, he was able to do what with the story?", "question_concept": "see story", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["picture it", "reading", "visualize", "open book", "go to movies"]}, "answerKey": ""}
{"id": "2fc16ace053d5a906a6d5f422a242d7c", "question": "Where would you most commonly find a stop sign?", "question_concept": "sign", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "bus stop", "street corner", "school", "roadblock"]}, "answerKey": ""}
{"id": "b8fec0eabb7090db994997dd694cac69", "question": "Where would you find a weasel that is not alive?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["washington dc", "prehistoric museum", "chicken coop", "cherry tree", "natural history museum"]}, "answerKey": ""}
{"id": "96646a5c200f027b54ffbf8021552b80", "question": "What part of some furniture could you put some stamps in?", "question_concept": "stamps", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["suitcase", "desk", "drawer", "cabinet", "case"]}, "answerKey": ""}
{"id": "ba2ebed8baf269abcfa0020ea11ba3eb", "question": "What is a good learning method known for?", "question_concept": "learning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["effectiveness", "distress", "education", "gaining knowledge", "increasing knowledge"]}, "answerKey": ""}
{"id": "4bcb3da3045057af215cbc04e763af16", "question": "The bat needed to go back to the benched players, where did it go?", "question_concept": "bat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bridge", "belfry", "new mexico", "dug out", "off the field"]}, "answerKey": ""}
{"id": "b887bcc33bb61eb20e37e52ac1ce626f", "question": "From where is a pizza removed before it is served?", "question_concept": "pizza", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["oven", "oven", "restaurant", "popular", "plate"]}, "answerKey": ""}
{"id": "bec4f1a2e26fe096a26216d762c78993", "question": "There is normally music at the beginning and end of the programming you watch on what?", "question_concept": "music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["silence", "silent", "television", "night club", "night club"]}, "answerKey": ""}
{"id": "f4ba945c957f3444e35f833dd0435c0a", "question": "Where is a weasel likely to have an accent?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hen house", "michigan", "forrest", "great britain", "dumpster"]}, "answerKey": ""}
{"id": "4d3e355cf5aa074edefe7173b77f63ec", "question": "The man wanted to learn about United States presidents, where should he look?", "question_concept": "united states", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["northern hemisphere", "history book", "north america", "atlas", "indians"]}, "answerKey": ""}
{"id": "6fce16ebf11626ef55bf0c0e37c9456d", "question": "The child tried to put a band-aid on the pet, his mom stopped him and explained they were only for skin of who?", "question_concept": "skin", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apples", "body", "people", "fruit", "finger"]}, "answerKey": ""}
{"id": "9e102eadc895ea08bbfbfd80c8309ee0", "question": "What can happen between two people judging a situation differently?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["guilty feelings", "conflict", "fight with each other", "argument", "controversy"]}, "answerKey": ""}
{"id": "fcff323001011dd21ed51fabff2f969f", "question": "The conductor was pompous and wanted his platform higher, he wanted to make sure he could be seen clearly from every seat in the what?", "question_concept": "platform", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["museum", "cafeteria", "building", "depot", "concert hall"]}, "answerKey": ""}
{"id": "43abbb0eb56d4e462c01b28afd632b8c", "question": "How could a weasel go to another continent?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["viking ship", "mulberry bush", "chicken coop", "fedex", "rabbit warren"]}, "answerKey": ""}
{"id": "5b8c59723217c25ab48b653822e0adac", "question": "Where can you find all of space?", "question_concept": "space", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["milky way", "jar", "universe", "suitcase", "box"]}, "answerKey": ""}
{"id": "********************************", "question": "The handle broke on a tool that James needed.  It was a long metal handle, with springs in it.  What was the tool?", "question_concept": "handle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["saucepan", "brush", "carry purse", "keys", "umbrella"]}, "answerKey": ""}
{"id": "0889b1d775c31b6c8162a3349da5ddb0", "question": "Jenny and all of her friends go to a church. So do all of her neighbors and everyone she knows.  There is no one in her area that doesn't go to church.  Where might she live?", "question_concept": "church", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["christian community", "every town", "wedding", "populated area", "city"]}, "answerKey": ""}
{"id": "6a73597749f0c6803624ba4b3671c21c", "question": "What could happen to you after you have been sitting quietly and then are offered a job?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relaxing", "satisfaction", "happiness", "relaxation", "anxiety"]}, "answerKey": ""}
{"id": "a069688802ba0a0418b9d3a1a5592859", "question": "If a heifer were living in a rural area with other domesticated animals, what would that area be called?", "question_concept": "heifer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "countryside", "farm yard", "german field", "barnyard"]}, "answerKey": ""}
{"id": "778ef50cd09c693a2483f4a8ef9e2f33", "question": "A person is an area where there is not a prison anywhere nearby, only grass where are they likely to be?", "question_concept": "prison", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["penitentiary", "alcatraz", "countryside", "kansas", "america"]}, "answerKey": ""}
{"id": "65868de4b9ef8c4e3c330446791afddd", "question": "What happens when hunger is not sated?", "question_concept": "hunger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eating", "starvation", "eat a worm", "have lunch", "discomfort"]}, "answerKey": ""}
{"id": "302848fd33b7de1db309c7893c9468d5", "question": "If one picked many flowers to display, what would they put them in?", "question_concept": "flowers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vase", "anthology", "rest area", "countryside", "table"]}, "answerKey": ""}
{"id": "f49cbed605a903623637c74d7e5752ec", "question": "Even though legality is questionable, a lot of people have a pet ferret all over where?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["out of doors", "great britain", "house", "rocky mountains", "north america"]}, "answerKey": ""}
{"id": "d07d6a6db10059cd4815351405c741a6", "question": "He worked in what large sorting area moving boxes with many a beam overhead?", "question_concept": "beam", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bridge", "ceiling", "warehouse", "new construction", "school"]}, "answerKey": ""}
{"id": "091b90d1d4393b3e8790e605927e2126", "question": "Where did the balloon go when the child lost control of it?", "question_concept": "balloon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["birthday party", "sky", "circus", "tree", "grocery store"]}, "answerKey": ""}
{"id": "95b2c13b66129dc5e7551f4fa849d411", "question": "Why would you be able to see through a door?", "question_concept": "door", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["keep people out", "glass", "xray vision", "enclose cupboard", "open"]}, "answerKey": ""}
{"id": "c1e186a3132cb4858fa701f4d400fbf5", "question": "Where would you sit in a chair while hearing about sin?", "question_concept": "chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["church", "university", "furniture store", "brothel", "office"]}, "answerKey": ""}
{"id": "fbc9fe0c02a75bec9aa79bb7366e4794", "question": "Reading the instructions was difficult, it took all his what?", "question_concept": "reading", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concentration", "written material", "accumulating knowledge", "brain power", "concentrating"]}, "answerKey": ""}
{"id": "f2c11e9f99aa19752b57a8c4cfe27048", "question": "Where could you find millions of accordion?", "question_concept": "accordion", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music store", "instrument room", "variety show", "grocery store", "san francisco"]}, "answerKey": ""}
{"id": "afb23d492be6a9481caf099e19bd0fe6", "question": "A fart is something that is known to?", "question_concept": "fart", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["impress", "smell bad", "attention", "offend", "expel gas"]}, "answerKey": ""}
{"id": "e2068d00603c52cfa67f96e6a12424d4", "question": "Where is a personal rosebush likely to be found?", "question_concept": "rosebush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["in the attic", "botanic garden", "backyard", "flower garden", "garden center"]}, "answerKey": ""}
{"id": "6efe7877b8edb04b0ae605f648915f3b", "question": "James took his dog for a walk in a place where there were picnics.   Where might he have taken his dog?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "dog pound", "table", "backyard", "leash"]}, "answerKey": ""}
{"id": "411f6391e0671388f372eca1bf860dad", "question": "On what is your chicken usually presented to you?", "question_concept": "chicken", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stove", "plate", "freezer", "fast food restaurant", "pizza"]}, "answerKey": ""}
{"id": "220605d634a4c6ebf7025c891396ed12", "question": "She found the  vintage dress beautiful, this was despite that it looked very what compared to the other's elaborate dresses?", "question_concept": "beautiful", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["homely", "bad", "overcast", "outdated", "plain"]}, "answerKey": ""}
{"id": "5da3c2f05d6f3cefbbbe4646ae64ae4c", "question": "In what environment would you find a marmoset?", "question_concept": "marmoset", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["warm climate", "great outdoors", "jungle", "rainforest", "shopping mall"]}, "answerKey": ""}
{"id": "6b080789e4d841e70d88edd828f9e464", "question": "What about a baseball makes it roll?", "question_concept": "baseball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hard", "break window", "round", "fly in sky", "fun to play"]}, "answerKey": ""}
{"id": "2eaa592ef41f55bd626d2b1d72f6aea3", "question": "Billy didn't like the darkness.  It was so dark that he couldn't make out any action.  He thought that these would be better quality.", "question_concept": "darkness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bed", "movies", "moon", "cellar", "moving"]}, "answerKey": ""}
{"id": "40041ef429ed015f329ffa3aef7b3476", "question": "Where do people keep cups?", "question_concept": "cup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen cabinet", "drawers", "closet", "restaurant", "apartment"]}, "answerKey": ""}
{"id": "1830737fdb09847324ddaf8e11bf4269", "question": "Where would you be landing if you were descending from a flight?", "question_concept": "landing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airport", "ladder", "stairwell", "apartment building", "ocean"]}, "answerKey": ""}
{"id": "3dc165719e917dbb6a8f901d6b1e02a6", "question": "Where was there previously a mammoth?", "question_concept": "mammoth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forest", "wild", "zoo", "movie", "ancient times"]}, "answerKey": ""}
{"id": "5e186319d14b1a8d6dee464e3680036b", "question": "If someone is standing in front of the class with index cards, what are they doing?", "question_concept": "index card", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["card catalogue", "fileing cabnet", "oral report", "draw pictures", "library"]}, "answerKey": ""}
{"id": "b6717893b27f6c05c619684eb15690f2", "question": "Where could you find more than a few barbershop?", "question_concept": "barbershop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["razor", "city", "shopping mall", "barber college", "village"]}, "answerKey": ""}
{"id": "e0c1481f8e7353d7ba82f98429d3e7b0", "question": "Sarah was analyzing the evidence because she didn't think she knew enough regarding it.   She wanted to do what?", "question_concept": "analysing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forget", "discovering truth", "enlightened", "new knowledge", "learn more about"]}, "answerKey": ""}
{"id": "b370186aefb03b810418051a7c3ea2f5", "question": "A koala lives alone.  How might you describe its habitat?", "question_concept": "koala", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["africa", "great outdoors", "queensland", "jungle", "wilderness"]}, "answerKey": ""}
{"id": "1da772e014dc60f790ac921bcfdf549a", "question": "What do you need to do while hiking to not get sick?", "question_concept": "hiking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enjoy nature", "get tired", "get lost", "pick flowers", "drink water"]}, "answerKey": ""}
{"id": "2a23800d9897030fdc7a9a286694ea6f", "question": "The black footed ferret is settling in large numbers in what geographic region?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["north america", "rocky mountains", "own home", "work", "out of doors"]}, "answerKey": ""}
{"id": "54548d73316e95b20965f971e75a116b", "question": "What could you have but still not be able to be hearing testimony if it is damaged?", "question_concept": "hearing testimony", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ears", "boredom", "frustration", "careful listening", "ability to hear"]}, "answerKey": ""}
{"id": "fa9d57256b02efa856f9c585c112a141", "question": "Where might an antique harpischord be found?", "question_concept": "harpsichord", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert hall", "music shop", "museum", "band", "mansion"]}, "answerKey": ""}
{"id": "2232f7c9fbdd2ab72fda34c5ab1eeda7", "question": "What would a homeless person need if they already have a fire to stand next to?", "question_concept": "fire", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cleansing", "warm room", "hot chocolate", "warm hands", "help people"]}, "answerKey": ""}
{"id": "6baa05d256dc723d6040986a354e229c", "question": "If an animal has gone days without food, what is its likely desire?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["keep alive", "snack", "need to eat", "bite", "lie down"]}, "answerKey": ""}
{"id": "1e565c6e6f0a9216ed43328ca7060379", "question": "Spam comes in a can, what other type of similar meat comes in a can?", "question_concept": "spam", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ham", "blam", "splog", "sping", "spick"]}, "answerKey": ""}
{"id": "709ec266b92bc2d2cf3f749e721e880c", "question": "Grapes are often depicted in what sort of artistic expression involving pigments?", "question_concept": "grape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["book", "fruit stand", "painting", "field", "winery"]}, "answerKey": ""}
{"id": "4cefa8256eeb65cb59e9ccd226b76989", "question": "When fixing a light bulb to a larger piece of furniture, what is created?", "question_concept": "light bulb", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["room", "idea", "basement", "theatre", "lamp"]}, "answerKey": ""}
{"id": "7e2cd3e8603f7382ca599c6214c09f40", "question": "The character in the horror story was afraid to go to sleep, this is because the monster came upon him in a what?", "question_concept": "go to sleep", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dream", "rest up", "get up early", "pillow", "nap"]}, "answerKey": ""}
{"id": "96f1ec19683e5fe6aa0581da455ed913", "question": "If you have some wool, what could you use it for?", "question_concept": "wool", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["craft store", "clothing store", "fabric store", "make a product", "sweater"]}, "answerKey": ""}
{"id": "ebbc0aeabef3636fcef1db2335d397cd", "question": "Where could you get something that is made out of wool and ready to use?", "question_concept": "wool", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["craft store", "fabric store", "seamstress", "sweater", "clothing store"]}, "answerKey": ""}
{"id": "8055f4c058cb7f75048a7567f33344c8", "question": "The captain made an order about a knot and the who did it?", "question_concept": "captain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["follower", "private", "deckhand", "military", "manual"]}, "answerKey": ""}
{"id": "c4d6b72979a717e699e96605c4f6974c", "question": "He enjoyed releasing energy in a healthy way, he always made time for what?", "question_concept": "releasing energy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dancing", "exercising", "sex", "sweat", "motion"]}, "answerKey": ""}
{"id": "84c802b1abc620e16a701cb3d5dcaa53", "question": "James and Jim were clerks who sorted stuff.  Where might they work?", "question_concept": "clerks", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mail office", "post office", "store", "shop", "stock shelves"]}, "answerKey": ""}
{"id": "bbf271a16f66ced481869ac9246753fd", "question": "A gazelle is the mascot of their high school.  Jane stood under a picture of one while wearing what?", "question_concept": "gazelle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["trophy room", "ball gown", "hat", "kalahari desert", "wildlife refuge"]}, "answerKey": ""}
{"id": "7b5526c0858ce164739c714d37215847", "question": "WHo is the most important person in someone's life?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["high wages", "husband or wife", "headache", "walking", "stay alive"]}, "answerKey": ""}
{"id": "d54709b9f05fcfcb7d89542bac81beac", "question": "What is a monkey in a zoo in?", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rain forest", "captivity", "french government", "bushes", "madagascar"]}, "answerKey": ""}
{"id": "fc189a0c02c7db71e3123cd7f0b4cef8", "question": "Where does a doctor work slowly?", "question_concept": "doctor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["emergency room", "nursing home", "medical school", "dentist", "golf course"]}, "answerKey": ""}
{"id": "cd53be591ce29cca772c7b5bb5e4bcb7", "question": "The broken faucet uncontrollable poured water into something.  What might that be?", "question_concept": "faucet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water fountain", "draw water", "sink", "bathroom or kitchen", "restroom"]}, "answerKey": ""}
{"id": "8d5edc8511298586e2cc0c0f58757491", "question": "Danny didn't like roll-on deoderant.  He preferred what?", "question_concept": "deodorant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spray", "medicine cabinet", "drum sticks", "grocery store", "own bathroom"]}, "answerKey": ""}
{"id": "1e22d463619bde9ee5130867eeaf0509", "question": "The populace wasn't excited to vote, they knew no matter who they chose it was just another weasel they were sending where?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["natural history museum", "the arlington cemetery", "chicken coop", "washington dc", "cherry tree"]}, "answerKey": ""}
{"id": "bbc744818da76fc80327d03ac23f0f27", "question": "Jane was afraid of committing perjury at a trial.  What might perjury lead to?", "question_concept": "committing perjury", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jail time", "go to jail", "criminal prosecution", "mistrial", "injustices"]}, "answerKey": ""}
{"id": "3622381c889f08b6c7146b49a8806567", "question": "Global warming is causing problems in California, and our rapidly changing society still can't keep up with it.   In addition to wildfires, they also have what?", "question_concept": "changing society", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["confusion and chaos", "draught", "no water", "acceptance", "change in leadership"]}, "answerKey": ""}
{"id": "683b7d13a459f5e6e310fbb2b2b9606c", "question": "If a person is feeling sad, what do they often seek?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["to climb a tree", "feel ashamed", "work at home", "gain weight", "live happily"]}, "answerKey": ""}
{"id": "26ed1dc67dd9e6edf45eb7d1a9ef04b1", "question": "What does the animal need to do?", "question_concept": "animal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["live long", "feel pain", "run away", "eating", "fight for life"]}, "answerKey": ""}
{"id": "a04ed842e50830e6da7d62a0a53207c1", "question": "What do people pretend to do when someone they like tells a joke?", "question_concept": "pretend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["people believe", "agree", "laughter", "religion", "can fool"]}, "answerKey": ""}
{"id": "26cf1eca0eafba24ed7efe1c8c4f6170", "question": "Jane turned to a random page and saw \"sloth.\" What might she have been looking at?", "question_concept": "sloth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["universe", "wilderness", "dictionary", "commercial", "math book"]}, "answerKey": ""}
{"id": "19b33e90b3e2d133f7a8d7674a431eca", "question": "What can a computer do quickly?", "question_concept": "computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["think", "get a virus", "run programs", "process information", "make decisions"]}, "answerKey": ""}
{"id": "1a21a3b77a805f7a453ff7dc69efb845", "question": "I had nothing but my hands from which to survive in the woods, what did I use them for?", "question_concept": "hands", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["articulate", "sign language", "cup face", "soft", "cup water"]}, "answerKey": ""}
{"id": "03aac4e041884106a614b7a101dd1f4d", "question": "Before we can become men, we are?", "question_concept": "men", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gods", "boys", "ladies", "children", "lady"]}, "answerKey": ""}
{"id": "011e451bc75b78fe4960a9915d05213f", "question": "Rather than make a mess removing it all, they just decided to paint over the what?", "question_concept": "paint", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fulfilment", "draw", "park", "with brush", "wallpaper"]}, "answerKey": ""}
{"id": "71abe27c155a5e582883919e74ff0bc1", "question": "Who is likely to have a large cymbal?", "question_concept": "cymbal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drumkit", "music store", "your brother", "symphony orchestra", "marching band"]}, "answerKey": ""}
{"id": "b2ffa6f4d7ea097907fb89d611963350", "question": "While driving to work, what might a person be dreading?", "question_concept": "driving to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ongoing issue", "transportation cost", "stress", "getting there", "road rage"]}, "answerKey": ""}
{"id": "44d474361e046b556b48b31e1bf5b611", "question": "John acted cold, but in reality he was very what?", "question_concept": "cold", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["baking", "prepared", "amiable", "opposite of hot", "chilly"]}, "answerKey": ""}
{"id": "35c2b1c7637dc83575bb26c1d9553336", "question": "Where would a small dog that is someone's pet live?", "question_concept": "small dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["aspca", "kenne", "basket", "next door", "person's home"]}, "answerKey": ""}
{"id": "50151d231fade66bc95684562d161958", "question": "The package was set t o deliver the next day, but the customer had the option to go what it now?", "question_concept": "deliver", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take away", "pick up", "ship", "keep", "receiving"]}, "answerKey": ""}
{"id": "7d0f388d8c9c8dbf7c2143c804bfab1c_1", "question": "The biohemist mom feared her dirt eating child would consume bacteria what did she avoid with him?", "question_concept": "bacteria", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water", "petri dish", "ground", "dirt", "finger"]}, "answerKey": ""}
{"id": "8e9852f85771fceacf387d727b0772e5", "question": "He liked the car and decided to buy it, he was now getting what of his first vehicle?", "question_concept": "getting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wanting more", "conquest", "ownership", "disappointment", "satisfaction"]}, "answerKey": ""}
{"id": "1055f0701fef17174ca35a6d7be66842", "question": "The two woke up and went out on the cabin's porch, they were breathing fresh air and one remarked \"now this is what I call what\"?", "question_concept": "breathing fresh air", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["breathe deeply", "look around", "living", "feeling good", "fresh air"]}, "answerKey": ""}
{"id": "a30f60507591ad14d87daa1a5a557f2f", "question": "When will you be unable to get warm since you will not know that you are cold?", "question_concept": "get warm", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel more comfortable", "swimming pool", "sleep", "start to sweat", "sweating"]}, "answerKey": ""}
{"id": "463019f928bd81579eaec5bfc9ce298a", "question": "Where can you go to have a person assist you in mailing a letter?", "question_concept": "letter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["swimming pool", "post office", "envelope", "mail box", "alphabet"]}, "answerKey": ""}
{"id": "6884a0deac2a638822f4946dfa8e3eb2", "question": "What is the ultimate goal of going to work?", "question_concept": "going to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["leave home", "stress", "malaise", "anger", "making money"]}, "answerKey": ""}
{"id": "65ca5ca4ca1774d67adb78eeef8cc7d5", "question": "James was scared to run through the course because he feared injury.  What might he do instead?", "question_concept": "run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["walk quickly", "walking", "stand still", "walk slowly", "go quickly"]}, "answerKey": ""}
{"id": "88007fc6a84123e90b792dc1807de427", "question": "If I am looking for a dental office in Ann Arbor, what state am I likely in?", "question_concept": "dental office", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["minnesota", "hospital", "michigan", "nebraska", "town"]}, "answerKey": ""}
{"id": "178872169facd619022718ff94993b34", "question": "What does a person do when they hear a joke?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel loved", "cross street", "laugh out loud", "cry", "hurry home"]}, "answerKey": ""}
{"id": "e0fee64a8401f66e3c35c79d8519844e", "question": "Johnny's teacher felt that he had such potential that he should not do what?", "question_concept": "teacher", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["time test", "tell story", "lower expectations", "quit", "encourage"]}, "answerKey": ""}
{"id": "f6e88f129e0cc58d9c04765b37f152fe", "question": "The person spent all morning putting on make-up and elegant clothing, what was their result?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["look beautiful", "intellectual stimulation", "compliments", "she was late", "time to rest"]}, "answerKey": ""}
{"id": "2b0e5425d86ac18c47cd1806a74fb284", "question": "When would you need to get warm?", "question_concept": "get warm", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["more comfortable", "heater on", "you're cold", "heat stroke", "feel comfortable"]}, "answerKey": ""}
{"id": "f514fdb807c433be25529cc79610e3b3", "question": "James was the servant.   Max was his what?", "question_concept": "servant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["free person", "slave", "lord", "boss", "in charge"]}, "answerKey": ""}
{"id": "91e6269752463b39ec50888d0a13f46a", "question": "The soldier was interred at the graveyard outside of Washington DC, where was he buried?", "question_concept": "graveyard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["church property", "every town", "arlington", "cemetery", "grave yard"]}, "answerKey": ""}
{"id": "2273f05dbd17824a8d2111d4fc2b5c47", "question": "When you are trying to remember something, you are putting what on it?", "question_concept": "remember", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["focus on", "try", "spotlight", "take pictures", "memorize"]}, "answerKey": ""}
{"id": "cddbdb53dbcc149bf1912aa7ea171570", "question": "What could sunshine light on fire if it is magnified?", "question_concept": "sunshine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sea", "summer", "moon", "windowsill", "desktop"]}, "answerKey": ""}
{"id": "1912cc3ef40e13763d0375c531d2f1c0", "question": "Even if you have a disability you can still have what which is the ability to do your job well?", "question_concept": "disability", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["competency", "potential", "capacity", "strength", "competence"]}, "answerKey": ""}
{"id": "ebae1b07ab3230d4b59b2c7ecc5a89d3", "question": "Sarah was looking for an ancestor who was buried in an old cemetery.   Where might he find that cemetery?", "question_concept": "cemetery", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["most cities", "churchyard", "field", "city", "countryside"]}, "answerKey": ""}
{"id": "27e65eba6f596045187e7af84151340a", "question": "Bob spent all of his time analyzing the works of masters because he wanted to become what?", "question_concept": "analysing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["more intelligent", "better understanding", "enlightened", "discovering truth", "headache"]}, "answerKey": ""}
{"id": "83d853f94d28a5507a7db70d396d3dff", "question": "What should you prepare for if you are judging someone?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["giving a test", "feeling guilty", "responsibility", "go to jail", "being judged"]}, "answerKey": ""}
{"id": "ecdb6d61299c2f8fd29a56380c7f6aa3", "question": "Where do you get a needle from an IV?", "question_concept": "needle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "hospital", "doctor's office", "haystack", "mom's sewing kit"]}, "answerKey": ""}
{"id": "c6f2cbec1de5ed18901375529cffed43_1", "question": "The couple decided to stay in bed all day together, unfortunately though it was because they what?", "question_concept": "stay in bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tired", "were sick", "you're sick", "lazy", "rest more"]}, "answerKey": ""}
{"id": "a3244a4b31bf92bf70c773e21f4da5ec", "question": "What was a historical occurrence in some cases when people of a certain belief system met people with different beliefs?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["thank god", "kill each other", "commit suicide", "believe in god", "experience pain"]}, "answerKey": ""}
{"id": "a72c911c9e5c7b315b034305dc294afc", "question": "The sloppy dad saw the random stuff in the kitchen, and saw space for it next to the glasses, where did he decide to place it?", "question_concept": "stuff", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cupboard", "box", "anything", "table", "cabinet"]}, "answerKey": ""}
{"id": "54592ec6a82ff212eed8e2ee14de18a3", "question": "Computers complete mathematic operations at what speed?", "question_concept": "computers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["expensive", "9 gigahashes per second", "fun", "fast", "do work"]}, "answerKey": ""}
{"id": "9aca1e1ca1d53f0a4489cb59ca78418e", "question": "What might frequent trips of going to market cause?", "question_concept": "going to market", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spending money", "bankruptcy", "tedium", "time wasted", "meeting new people"]}, "answerKey": ""}
{"id": "8ce45dcedea9223e850ade4801985564", "question": "The couples were together for fifty years in the study, what did they do less?", "question_concept": "couples", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enjoy movie", "enjoy sex", "row with each other", "have sex", "company"]}, "answerKey": ""}
{"id": "0ca6391549e2b1f8e4ea809c0e5b256a", "question": "What is another name for trash can?", "question_concept": "trash", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dumpster", "wastepaper basket", "soccer game", "garbage can", "wastebasket"]}, "answerKey": ""}
{"id": "68250b9f3448c4b65aa9ce688c6e4cf3", "question": "If you competing against someone who is a rival what may you share?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bad blood", "puncher", "winning or losing", "accomplishment", "rivalry"]}, "answerKey": ""}
{"id": "61984fa9de4fc5484dca07aa697c03b2", "question": "Where do you normal use a pillow case?", "question_concept": "pillow case", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "england", "pillow", "bedding store", "bedroom"]}, "answerKey": ""}
{"id": "36aa45a085fad5e9c9479811f19bffea", "question": "The adviser had said the class was a requisite for his major, but on the first day he realized it was completely what?", "question_concept": "requisite", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dispensable", "unworthy", "unneeded", "inessential", "unnecessary"]}, "answerKey": ""}
{"id": "5566e5b229dc312159ead031240af4fd", "question": "Where might someone find a map on the wall?", "question_concept": "map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["atlas", "museum", "amusement park", "backpack", "classroom"]}, "answerKey": ""}
{"id": "7c7576876bed0d9e07e69bc10cedbfd8", "question": "Where is a movie about a barber shop done?", "question_concept": "barber shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["high street", "small town", "seville", "comb", "canada"]}, "answerKey": ""}
{"id": "38e5d6155751fd84b046ecd71c29f44a", "question": "The student practiced his guitar often, he always spent his free period where?", "question_concept": "guitar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music room", "rock band", "toy store", "stage", "concert"]}, "answerKey": ""}
{"id": "ce8c5db1047aa21178422eaa11c4a33d", "question": "How does a person get around town?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["catch cold", "thank god", "cross street", "promise to do", "driving"]}, "answerKey": ""}
{"id": "8db48320d6def5cbf15e1ba800ddc9fa", "question": "The store offered cloth bags, but she had already brought her tote bag from her what?", "question_concept": "tote bag", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["house", "shopping cart", "store called target", "school", "city"]}, "answerKey": ""}
{"id": "9c5880baa5e1746f83b40b9f7e4e118c", "question": "According to many people of faith the first people ate from an apple tree causing them to be expelled from where?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["their house", "paradise", "dirt", "south africa", "park"]}, "answerKey": ""}
{"id": "b34abcccf20b28c1539cb26dc5da6b74", "question": "What the opposite of liken?", "question_concept": "liken", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["discriminate", "differentiate", "contrast", "distinguish", "discern"]}, "answerKey": ""}
{"id": "2c2dfb48b4bcfb134991c37679dbccd6", "question": "Why do people memorize their passwords?", "question_concept": "commit to memory", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["to delete their accounts", "must remember", "pass exams", "important", "useful"]}, "answerKey": ""}
{"id": "189bed3cb3fbac403343294d25141925", "question": "How do people usually begin an interaction?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talk to each other", "shake hands", "pay bills", "own property", "rub noses"]}, "answerKey": ""}
{"id": "6d94fb7d13aff3ed9eedda570498e8ca", "question": "what do living things do as they get older?", "question_concept": "living things", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["increase in size", "procreate", "move", "increase in size", "increase population"]}, "answerKey": ""}
{"id": "7f710a9fc9b0669b1d54e089627c9d5f", "question": "Where can toilet paper be bought?", "question_concept": "toilet paper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rest area", "department store", "grocery store", "cabinet", "bathroom"]}, "answerKey": ""}
{"id": "a97c51e1ff690058dd27219ce22c6508", "question": "What do I want that makes me want to procreate a lot?", "question_concept": "procreating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["quintuplets", "large family", "children's laughter", "population increase", "added responsibilities"]}, "answerKey": ""}
{"id": "5db6f0b5c7b4ed600010157d629a2f5f", "question": "Falling doesn't cause injury.   Injury is cause by the ground, specifically what involving the ground?", "question_concept": "falling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting hurt", "being laughed at", "lacerations", "gravity", "contact with"]}, "answerKey": ""}
{"id": "99c81f995c9182b92ebc658c89b1b417", "question": "Where do you buy vegetables?", "question_concept": "vegetables", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "fridge", "refrigerator", "cashier", "vegetable garden"]}, "answerKey": ""}
{"id": "7e0023fe1f499a61d5a6f9af01effbfc", "question": "The politician was taking money from lobbyists, what were they hoping to do by giving it?", "question_concept": "money", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["settle everything", "multiple more money", "pay bills", "increase power", "control people"]}, "answerKey": ""}
{"id": "b99b1c7a51a26f5178475313a9bbd287", "question": "What would I need if I want to know the number of people with cancer and I know the total number of people?", "question_concept": "number", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mathematics", "statistic", "percentage", "equation", "math problem"]}, "answerKey": ""}
{"id": "666922651ffa1c7db321f29613e2a127", "question": "The company blew up over night, it seemed like one day they were local and the next what?", "question_concept": "local", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["foreign", "far away", "everywhere", "national", "worldwide"]}, "answerKey": ""}
{"id": "bb7108a24332df7512017038505e329d", "question": "Danny was getting warm. He turned on the air conditioner so he wouldn't do what?", "question_concept": "getting warm", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feeling iritable", "pleasure", "euphoria", "starting fire", "get hot"]}, "answerKey": ""}
{"id": "e4e4384278560fc9d86c670732ae1025", "question": "I have a chess pawn and 15 other pieces, what could I play with this?", "question_concept": "chess pawn", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toy store", "chess set", "soccer", "chess game", "small case"]}, "answerKey": ""}
{"id": "5abced4fbb2487ab085fe86653e05465", "question": "The family was poor and couldn't afford meat, so what purpose did the potato serve in their meal?", "question_concept": "potato", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["croquettes", "prince edward island", "main course", "french fries", "root cellar"]}, "answerKey": ""}
{"id": "5d08f46e6d34d7b18523504704995fd1", "question": "What positive effect can making bread cause?", "question_concept": "making bread", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "mess", "pride", "gratifying", "better eating"]}, "answerKey": ""}
{"id": "622995494e6e096f778670cecb3d830b", "question": "Where is a microphone boom likely to be used to broadcast live music?", "question_concept": "microphone boom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tv studio", "new york", "concert", "recording studio", "building"]}, "answerKey": ""}
{"id": "4ed764f071d4f558f9ec42b48ef3ca01", "question": "It was a weird place to be, she was the most popular but also the most what?", "question_concept": "popular", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unknown", "nerdy", "disliked", "annoyed", "geek"]}, "answerKey": ""}
{"id": "bedcaa84506fe21fc324db3c618cdfa8", "question": "If I have information, why would I want to be communicating it?", "question_concept": "communicating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sharing of knowledge", "people to think", "distributed information", "response", "misunderstandings"]}, "answerKey": ""}
{"id": "ea0ee21980f85234853de55d7064d10c", "question": "Where would finding a fox make a farmer angry?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["painting", "hen house", "wooded areas", "chicken coop", "bird's nest"]}, "answerKey": ""}
{"id": "45fd62a1570cf4e226de657ada37bcc6", "question": "Where is usually the best place to store cheese?", "question_concept": "cheese", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["the cupboard", "refrigerator", "plate", "fridge", "mouse trap"]}, "answerKey": ""}
{"id": "bfddbe2b019d7ab2bb1bb25871716f06", "question": "The instructor explained that pretending was all it was, and practicing that was the only way to hone their what?", "question_concept": "pretending", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["imagining", "acting skills", "creativity", "vehicle", "play"]}, "answerKey": ""}
{"id": "f4a30172513ca4c5763ded05f2f9a756", "question": "Where do terrorist grow heroin poppies in the mountains?", "question_concept": "terrorists", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["explode bombs", "afghanistan", "prison", "downtown", "airport"]}, "answerKey": ""}
{"id": "5b0b9d64c3ce0d4d3648d45bbd88ff61", "question": "If you have a cavity in the back of you mouth where is it?", "question_concept": "cavity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["teeth", "mouth", "molar", "too much sugar", "dentist"]}, "answerKey": ""}
{"id": "cfef1ee6edd1e673f85e624509cb6f05", "question": "What is the biological intent when two people are copulating?", "question_concept": "copulating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reproduction", "ejaculation", "babies", "rapport", "cumming"]}, "answerKey": ""}
{"id": "d2542eb1408a9420c520145c46a82663", "question": "What always happens to products after you are buying products?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["owning", "pleasure", "agony", "never used", "disagreements"]}, "answerKey": ""}
{"id": "42ce969a0fd4788aaadff77e8405f288", "question": "Why would you try to find a bad apartment?", "question_concept": "apartment", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["avoid buying", "staying cheap", "town", "budget", "michigan"]}, "answerKey": ""}
{"id": "cea0ef7b40f17c03793a0d5785ce8091", "question": "Where do you wait before using your train ticket?", "question_concept": "train ticket", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["polar express", "purse", "train depot", "conductor's hand", "ticket booth"]}, "answerKey": ""}
{"id": "3d045798bf3bcd73608d297ec8426953", "question": "The construction began and they sunk a big support shaft into the ground, this would support the fancy what out front?", "question_concept": "shaft", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mine", "column", "windshaft", "building", "tunnel"]}, "answerKey": ""}
{"id": "4f434f12da41878d149d085b1df26408", "question": "From where could you read your child a storey before bed?", "question_concept": "storey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tall building", "mall", "apartment building", "book of stories", "roof"]}, "answerKey": ""}
{"id": "46d709541c4a58e71f9b7f47d3a2ff12", "question": "Buying something nice after a stressful event can make you what?", "question_concept": "buying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel better", "possessing more", "relax", "go broke", "losing money"]}, "answerKey": ""}
{"id": "7b4fd5d4f54eb97257eee2c859f7cc69", "question": "Where would you be shopping if you were at a bazaar named in a 1950's song?", "question_concept": "bazaar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["festival", "canada", "arabia", "istanbul", "india"]}, "answerKey": ""}
{"id": "fd6857afa7df49af59cb6b3378010c87", "question": "What would you do if someone poured water above you?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wet clothes", "spring forth", "take several forms", "take it", "room temperature"]}, "answerKey": ""}
{"id": "30f7b893533032e1b7572ad40400dc76", "question": "What is a person helping for when going to customer service?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["thank god", "enjoy working", "complain", "wait in line", "offer help"]}, "answerKey": ""}
{"id": "8ec10dc05137c639d1d51780a18fff2f", "question": "The country was waging war, those back home had to ration their household use of what?", "question_concept": "waging war", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["armies", "energy", "using electric", "asserting power", "weapons"]}, "answerKey": ""}
{"id": "18e72c9c9da44dbf888d6ac0deadb0ef", "question": "Where would a runway used by people wearing camo in an array of colors?", "question_concept": "runway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["military base", "bowling alley", "mall", "city", "fashion show"]}, "answerKey": ""}
{"id": "ef730bdda2cb6081f1c0d1a67291415b", "question": "What has a keel in its skeleton?", "question_concept": "skeleton", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "aircraft", "case", "ship", "museum"]}, "answerKey": ""}
{"id": "5f9675ecdc9de4feccaa84588db94c24", "question": "Where can one find a road that goes through New York City?", "question_concept": "road", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["valley", "bridge", "new york", "town", "north america"]}, "answerKey": ""}
{"id": "bdb789fc941b9e1906b3ba858703b157", "question": "Dog lovers are known to have?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nose", "paws", "big heart", "one mouth", "four legs"]}, "answerKey": ""}
{"id": "8206e2a8bfb379249fb4d95bc92378ac", "question": "Where do students sit in chairs for large meeetings?", "question_concept": "chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["synagogue", "friend's house", "auditorium", "desk", "office"]}, "answerKey": ""}
{"id": "e3f2d6d45f26aeeeb63defeb9ba18614", "question": "A cat smelled something delicious and jumped into something dangerous. What might it have smelled?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meat loaf", "back yard", "microwave", "floor", "bedroom"]}, "answerKey": ""}
{"id": "7a96b8faba4541809bf8060492e6531b", "question": "If you're successful while performing you should receive what from you critics?", "question_concept": "performing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["happiness", "accolades", "tiredness", "do best", "sadness"]}, "answerKey": ""}
{"id": "23820bf771919047a8d0ab30b0a722b3", "question": "A person in the Navy is wearing a monkey suit, what should they be wearing?", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["banana tree", "sailor suit", "treetops", "gorilla suit", "mulberry bush"]}, "answerKey": ""}
{"id": "cdecadaf8838312c4b717f54ef0f3e83", "question": "The parking area was full at every hour and every day with residents' cars, where was it located?", "question_concept": "parking area", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment complex", "people", "ugly", "city", "amusement park"]}, "answerKey": ""}
{"id": "7137dd6f5b64eb313a9178f69e55b2c4", "question": "What can talking for a long time cause that makes you think of waterfalls?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["revelation", "sore throat", "conversation", "drink", "dry mouth"]}, "answerKey": ""}
{"id": "f62d475a3ae5922c9e93b3ddb2a243bb", "question": "They began their family trip, they went from one national highway to another in their adventure across where?", "question_concept": "national highway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["canada", "united states", "major cities", "the country", "atlas"]}, "answerKey": ""}
{"id": "d237ab9aad3cf167e09dea0507f35b02", "question": "Billy learned that he was a really bad runner, so he decided to do what?", "question_concept": "learn", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["remember information", "quit", "run", "teach", "forget"]}, "answerKey": ""}
{"id": "bb0d05f15c214a2f958767e61deb3882", "question": "What does an artist make when they see beautiful scenery?", "question_concept": "scenery", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "photograph", "cake", "picture", "painting"]}, "answerKey": ""}
{"id": "18904c8a04b9dab4e9c5cd24e60afbba", "question": "Defending one's base against enemy attacks at all cost is what?", "question_concept": "base", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desirable", "likeable", "admirable", "home plate", "exhausting"]}, "answerKey": ""}
{"id": "5ea1c9129d3195720e3ad8ff334f6479", "question": "What happens to a persons hands after years of playing guitar?", "question_concept": "playing guitar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listening to music", "making sound", "arthritis", "making music", "singing"]}, "answerKey": ""}
{"id": "d1f788f39ed4a98a5aa43ec44736a030", "question": "If something is known for its solidity it is often the case that it has few what?", "question_concept": "solidity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["space", "openness", "fluidity", "hollowness", "weakness"]}, "answerKey": ""}
{"id": "5d5fbce4682e27c308c492cb8a885e1c", "question": "Sally couldn't hold on to Luke.  He kepy wiggling out of his grip.  With the match taking so long, she decided to do what?", "question_concept": "hold", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["let go", "lose", "give up", "hold on", "put down"]}, "answerKey": ""}
{"id": "7f1c37c50aa2826924554a969676ec63", "question": "If your cutting tools aren't sharp they can lead to what kind of cut performance?", "question_concept": "sharp", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jagged", "dull", "unobservant", "inelegant", "inaccurate"]}, "answerKey": ""}
{"id": "f0845c750514a31a063dc0c0dbf2466b", "question": "What is the benefit of deciding to examine a thing?", "question_concept": "examine thing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["know what", "might want", "interested in", "learn more about", "use it"]}, "answerKey": ""}
{"id": "bab752fb6b11993834a9a5248db3bfe7", "question": "He had stayed up playing video games, and now his book made a good pillow where?", "question_concept": "book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["xbox", "bedside table", "coffee table", "backpack", "school room"]}, "answerKey": ""}
{"id": "aae8e5595b8c9a2e68c901097802d40c", "question": "I was getting in line and had a 5 hour wait, what did I feel over my time standing there?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["common sense", "longer lines", "fatigue", "get worried", "agitation"]}, "answerKey": ""}
{"id": "ae112757c389ccab476ed55b65a73fe9", "question": "Where might one leave from to pursue their route as part of their job?", "question_concept": "route", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["the globe", "post office", "map", "get to specific place", "atlas"]}, "answerKey": ""}
{"id": "cf1d1041b22f588ea187aee2e9b22bb8", "question": "A child wants to survive, what does he need to do?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["breathe", "fall down", "play tag", "ask questions", "eat"]}, "answerKey": ""}
{"id": "bed9fb7dbd32f3228a1ae8dbf6aed2f6_1", "question": "It was the first day and he was prepared, he had a whole pack of pencils in his what?", "question_concept": "pencils", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["backpack", "classroom", "store", "pocket", "cabinet"]}, "answerKey": ""}
{"id": "d77861add52c70d9c67b7f5766fcd241", "question": "Where do you buy food shaped like mickey mouse?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refrigerators", "kitchen", "disneyland", "record store", "shop"]}, "answerKey": ""}
{"id": "93a88ad4a7583040649e0cf3d0f3c211", "question": "Where might carpet help your feet stay warm?", "question_concept": "carpet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "basement", "office", "bedroom", "building"]}, "answerKey": ""}
{"id": "db5c06784bd7885138232850969653a7", "question": "Many people enjoy getting together with friends, they like to gather around the table to do what?", "question_concept": "many people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listen to music", "watch t v", "talk", "play games", "eat ice cream"]}, "answerKey": ""}
{"id": "8f3e31e46c81511e39c4f4028bc0b232", "question": "If a person really wants to focus on a topic and judge it honestly, what should they be using?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["acknowledgment", "congratulated", "critical thinking", "problem solving", "equal opportunity"]}, "answerKey": ""}
{"id": "5dfee150d0435fefa4b3a5c2d292e378", "question": "What is the benefit to you of waiting for a bus?", "question_concept": "waiting for", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["impatience", "time consuming", "being late", "reflection", "have time to think"]}, "answerKey": ""}
{"id": "209faba061890995e3b25e9f99a4b6ed", "question": "He had only seen a lemur on television, but during the trip he saw one out in the what?", "question_concept": "lemur", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["zoo", "dictionary", "wilderness", "sunshine", "rain forest"]}, "answerKey": ""}
{"id": "69a596dc47a864be31b189e599de2fe1", "question": "The military tribunal was discovering the truth, what were they investigating?", "question_concept": "discovering truth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["startled", "enlightenment", "murders", "wars", "denial"]}, "answerKey": ""}
{"id": "20d97af0a60ceae1eec856f694a4dc9f", "question": "The man wanted to impress women, so he began exercising, what was his expected result?", "question_concept": "exercising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sore muscles", "pain", "tiredness", "handsome face", "muscle growth"]}, "answerKey": ""}
{"id": "345f5fa9925de3a48035ff85bbeede35", "question": "When is an armoire found near the entrance to your dwelling?", "question_concept": "armoire", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bedroom", "living room", "furniture store", "house", "closet"]}, "answerKey": ""}
{"id": "f56f9a720f1140037d3f967b1a5ecd34", "question": "The minimalist author looked at his written work, but what was he focused on?", "question_concept": "written", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unwritten", "oral", "aforewritten", "paper", "verbal"]}, "answerKey": ""}
{"id": "79256fcf19c4ef22f187a1941152dcab", "question": "The detective was on a stakeout to examine thing, he was going to sit there all day and what?", "question_concept": "examine thing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rest", "discovery", "interests", "learning", "observe"]}, "answerKey": ""}
{"id": "e30ae6ab5bce9793f0ed8c32c0627390", "question": "Printing on printer at home can get costly, between the ink and what?", "question_concept": "printing on printer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lead", "using paper", "computer", "error", "drink coffee"]}, "answerKey": ""}
{"id": "850b587650fa7c11766dcb3df58d7348", "question": "In the gruesome aftermath everyone struggled, though it was over there was definitely no what?", "question_concept": "everyone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["canidate to win election", "victory in war", "confused", "understood", "happiness"]}, "answerKey": ""}
{"id": "f552db63a413af5eb7db1ee9b916afab", "question": "What would friends do if they no longer like each other?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have dinner", "understand each other", "part company", "keep secrets", "group together"]}, "answerKey": ""}
{"id": "5e8d6d7b72205e07bc1112d053980355", "question": "What would a person feel contemplating their life accomplishments?", "question_concept": "contemplating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["completeness", "clear thought", "deep thoughts", "revelations", "sense of fulfillment"]}, "answerKey": ""}
{"id": "7e611b76883e645cc7db357b8ac7b3d3", "question": "The farmer began reproducing the crop, he kept up this what until his fields were full?", "question_concept": "reproducing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fun", "birth", "propagation", "overpopulation", "plowing"]}, "answerKey": ""}
{"id": "205ab92415db0d60a257ee8fba2daacd", "question": "Where can you buy candy and see ushers?", "question_concept": "candy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["theatre", "supermarket", "jar", "amusement park", "movies"]}, "answerKey": ""}
{"id": "a11eb3798cda0c7cd471bc93c738e743", "question": "Sarah noticed that she was out of cookies. She was going to serve some with dinner, so she needed more.  Where might she go?", "question_concept": "cookie", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jar", "store", "dessert", "evercookie", "home"]}, "answerKey": ""}
{"id": "ac9437d351ecbbaee50499d4eba2df6d", "question": "Where might someone store a contraceptive?", "question_concept": "contraceptive", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vagina", "medicine cabinet", "pharmacy", "kitchen", "drug store"]}, "answerKey": ""}
{"id": "c2c0c889b9880ca21b507a2bef95ba53", "question": "In a barbershop, what does the barber use to clean off someone's face after shaving them?", "question_concept": "barbershop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shopping mall", "water", "commercial area", "dirt", "razor"]}, "answerKey": ""}
{"id": "98e29a0593f509e4d7829fdb24119ecd", "question": "Twisting an ankle while playing sports will cause what?", "question_concept": "playing sports", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hurt", "sweating", "injuries", "pain", "rain"]}, "answerKey": ""}
{"id": "2256ca4fac74766f422f17dec031d161", "question": "What do you usually fill with furniture?", "question_concept": "furniture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["room", "store", "friend's house", "removal van", "building"]}, "answerKey": ""}
{"id": "414bd3088551ea2bb0a68eef6a9ad934", "question": "Where might eyeglasses get fogged up in the winter?", "question_concept": "eyeglasses", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shirt pocket", "michigan", "on one's head", "breast pocket", "case"]}, "answerKey": ""}
{"id": "572b7be6350be6f8361e0a4440637ce1", "question": "The car was completely totaled in the accident, the only place it belonged now was atop a what?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["driveway", "parking lot", "scrap heap", "parking garage", "garage"]}, "answerKey": ""}
{"id": "9f2c79604bb86212104211adf85f5bfa", "question": "When you are being bored, strategy games and traveling are ways to do what?", "question_concept": "being bored", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["entertain", "go somewhere", "knit", "play chess", "make patchwork quilt"]}, "answerKey": ""}
{"id": "81c3ccc031d3ebc1c9275ff65b6be807", "question": "What would you do to a door that you want to get through?", "question_concept": "door", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["found on car", "closed", "apartment", "locked", "opened"]}, "answerKey": ""}
{"id": "6c6ea635a004b73a5847b6542684803b", "question": "What sort of place is likely to sell crab dishes?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tidepools", "fancy restaurant", "fish department", "most offices", "business"]}, "answerKey": ""}
{"id": "3d6a28bbc1271d43b281e265630345d2", "question": "Why was the couple standing in line?", "question_concept": "stand in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tickets", "polite", "money from bank", "wait turn", "killing time"]}, "answerKey": ""}
{"id": "a5ba48993e46e56480d9dc7196d58542", "question": "What can fit in a condominium?", "question_concept": "condominium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "complex", "large city building", "towels", "florida"]}, "answerKey": ""}
{"id": "770eebe6eba4dec72190965ad7884d22", "question": "Where do you get books in a bag?", "question_concept": "bag", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "shopping mall", "grocery store", "shopping cart", "bookstore"]}, "answerKey": ""}
{"id": "67a23a2fd1f445db4b7dc342a887d159", "question": "Where are you likely to find a variety of hair salons?", "question_concept": "hair salon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mail", "shopping center", "small town", "hotel", "metropolitan city"]}, "answerKey": ""}
{"id": "3f9317664dc7484437a8ee518a8ee840", "question": "What risk of people with severe insanity is that they might do what?", "question_concept": "insanity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kill people", "run in marathon", "advance into battle", "wage war", "seek help"]}, "answerKey": ""}
{"id": "f2211ec560b9ffb60dd728438e969dbb", "question": "Where might I get written information on a past event?", "question_concept": "information", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["book", "magazine", "internet", "television", "newspaper"]}, "answerKey": ""}
{"id": "670aa5d324cc3f39c034dc0fc68d5861", "question": "Sarah wanted to stay in bed all day.  She was up late with a cough last night and wanted to do what?", "question_concept": "stay in bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play dead", "you're sick", "rest more", "were sick", "left alone"]}, "answerKey": ""}
{"id": "511666b1003b340a0a8213ec5fe38b81", "question": "What will happen after eating hamburger every day for too long?", "question_concept": "eating hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rocket launch", "tasty", "gas", "indigestion", "health problems"]}, "answerKey": ""}
{"id": "1ab67e7593bf8858bd45ed606486327c", "question": "What type of structure would hold any type of person including gamblers?", "question_concept": "gamblers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "book bets", "race track", "deal seconds", "casino"]}, "answerKey": ""}
{"id": "bfd67879d0b3550a94d99210387025ed", "question": "Who has a need for a subway station?", "question_concept": "subway station", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city underground", "london", "big city", "new york", "a sandwich"]}, "answerKey": ""}
{"id": "b573fb3b40fd85a9171c1e0b459b71cb", "question": "John had good reason for his actions, but he couldn't explain himself to his daughter.  She say things in black and white, but really, that wasn't how the world was.  What was the world?", "question_concept": "world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chaotic", "round", "complicated", "diverse", "small"]}, "answerKey": ""}
{"id": "cacdd89beb62e8c0d20d8c26f06d2e3d", "question": "Billy lived in the east wing of his home.  The west wing was where he kept boxes full of junk.  Sometimes he wished he hadn't decided to live in what?", "question_concept": "wing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospital", "buffalo", "airplane", "mansion", "large building"]}, "answerKey": ""}
{"id": "78cd945a7f08bb254115d70659458120", "question": "Jenny took her ficus for walks.  Everyone thought that this was silly.  You don't walk a plant, they said. It was big and heavy, too.  Still, every day she took it for a walk.  Where might she have taken it?", "question_concept": "ficus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["green house", "large pot", "park", "nursery", "arboretum"]}, "answerKey": ""}
{"id": "3047e6b519fc811a70e52cf79fe97e77", "question": "What is the hoped result of learning?", "question_concept": "learning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["correct answers", "intelligence", "effectiveness", "education", "distress"]}, "answerKey": ""}
{"id": "8d75b7124c922af798e2c82f4c20b5bb", "question": "How might a person be prompted to take action?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["incentive", "attempt suicide", "wave goodbye", "further cause", "thank god"]}, "answerKey": ""}
{"id": "2983b16eacbaa0583b7c5c98d56e97c1", "question": "What could listening to music cause you to do?", "question_concept": "listening to music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["smile", "entertainment", "deafness", "home run", "calm"]}, "answerKey": ""}
{"id": "dbccba5e0c7c0545f2b4d45f63db61d2", "question": "A person is driving by an upscale shopping complex, where is it located?", "question_concept": "shopping complex", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["subarbs", "michigan", "big city", "suburbia", "overseas"]}, "answerKey": ""}
{"id": "219cbfc92cb8624da9f649edeabb9e5f", "question": "Zoss was a philosopher.  He choose philosophy because thought that understanding things better would lead to what?", "question_concept": "understanding better", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["peace", "decreased knowledge", "ideas", "interest in", "wisdom"]}, "answerKey": ""}
{"id": "a63d96a1a9387973976dff68c4039981", "question": "As the earl approached on horse back and saw the tower, what did he know he was close to?", "question_concept": "tower", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airport", "radio station", "city", "medieval castle", "london"]}, "answerKey": ""}
{"id": "421fcd84441db9de906319d7f0c61432", "question": "Where must a television be kept quiet?", "question_concept": "television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabinet", "house", "garage", "apartment", "bedroom"]}, "answerKey": ""}
{"id": "4cdf83fe252f0883a93af57a421fd9c8", "question": "Billy spends his time cleaning dust. Where can dust be found?", "question_concept": "dust", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["television", "everywhere", "corner", "attic", "most buildings"]}, "answerKey": ""}
{"id": "298089ddab6eddec5e2c629abf1a51c8", "question": "Jay would run after the ball into the street, and always failed to so what?", "question_concept": "run after ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get tired", "have legs", "look first", "not fat", "fast"]}, "answerKey": ""}
{"id": "6ead94dce0ff31bc39c62bbb42f133b5", "question": "When you cut into an apple what is something that you can find inside of it?", "question_concept": "apples", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["knife", "stems", "seeds inside", "peel", "grocery store"]}, "answerKey": ""}
{"id": "fdf107a872ff151241266c2fc5de4535", "question": "What does a car usually do on a freeway?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cost money", "crash", "pass", "go fast", "move quickly"]}, "answerKey": ""}
{"id": "9a0f29ed1eeee98931f0d1d670b41b7a", "question": "The person was scared to face the crowd, but they knew they had to walk out to the microphone and speak at center what?", "question_concept": "microphone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["submarine", "demonstration", "stage", "conference", "record sound"]}, "answerKey": ""}
{"id": "ee506d6aa3ccc859f6d5b4054c047fc3", "question": "What did the businessmen want when they sought entertaining experiences?", "question_concept": "entertaining", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["favors", "boredom", "gratification", "validation", "happiness"]}, "answerKey": ""}
{"id": "1898388f414bfb89016ed5a3ed0752d0", "question": "What type of local would you expect to see one apple tree after another?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nevada", "washington state", "pennsylvania", "front yard", "countryside"]}, "answerKey": ""}
{"id": "3141857e63c3cce542a7a210860e234f", "question": "The space shuttle flew past Neptune, where was it looking to leave?", "question_concept": "space shuttle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["orbit", "solar system", "universe", "outerspace", "there are four choices for each sentence"]}, "answerKey": ""}
{"id": "f26e52a3b9c40e78bb485e70f7368a82", "question": "Where would you find a shopping bag filled with clothes?", "question_concept": "shopping bag", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["restaurant", "good will", "supermarket", "grocery store", "mart"]}, "answerKey": ""}
{"id": "68b3569431a6a9ba85bf0ffa64a5d4a1", "question": "She thought she had done bad on the test, but the A+ showed she had done what?", "question_concept": "bad", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["excellent", "sufficient", "happy", "competent", "beneficial"]}, "answerKey": ""}
{"id": "d3861b05b57e4be80eb7171842a92eb3", "question": "What would you want to get after you serve customers?", "question_concept": "serve customers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["help", "come back", "food", "money", "pay to"]}, "answerKey": ""}
{"id": "b611cac8d175cf35e44e1d21e824aba5", "question": "A good thing for friends to do when they want to discuss something important?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["borrow money", "meet for lunch", "talking about", "smile", "keep secrets"]}, "answerKey": ""}
{"id": "5abbb0e366a0327dcca1b237457c098f", "question": "Janes loved having food, but he couldn't eat as much as he would like because he has trouble doing what?", "question_concept": "having food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["weight gain", "gas", "getting fat", "digesting", "not hungry"]}, "answerKey": ""}
{"id": "********************************", "question": "Where can I get a beer from someone I don't know well?", "question_concept": "beer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bar", "keg", "neighbor's house", "casino", "friend's house"]}, "answerKey": ""}
{"id": "f6d9271cabb87afb7c6b4187b195a199", "question": "The man sat down at his piano and looked into the darkness and heard coughs and whispers, where was he?", "question_concept": "piano", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["performance", "restaurant", "symphony orchestra", "church", "concert hall"]}, "answerKey": ""}
{"id": "48bee74b35fd02b60303ec156a54294c", "question": "The bird was alarmed when a raccoon walked by her next, what did she do to scare it away?", "question_concept": "bird", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["experience flight", "eat a worm", "learn to fly", "attempt to fly", "squawk"]}, "answerKey": ""}
{"id": "e4b103a4f1373b6a92c460437a562bd4", "question": "Some people are environmentalists, when they see cardboard in the rubbish they move it to the what?", "question_concept": "cardboard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["warehouse", "recycling bin", "packaging materials", "recycle bin", "trash"]}, "answerKey": ""}
{"id": "0dc56e6fa6050863ebdee9d225b8c208", "question": "He was trying to do printing on printer, but there was some what popping up?", "question_concept": "printing on printer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toner", "error", "using paper", "paper and ink", "drink coffee"]}, "answerKey": ""}
{"id": "7da9d09579298472619e012c2e395bb5", "question": "A committee is just a way to get overtime and special pay, that's why people who work in where always make them?", "question_concept": "committee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["church", "business", "government", "school", "city hall"]}, "answerKey": ""}
{"id": "4f8eae6183aaad0c9abea351287c7b03", "question": "What do children love to do with scissors and paper?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listen to music", "reach over", "need care", "cut and paste", "play basketball"]}, "answerKey": ""}
{"id": "fae79a6551a98db1217fa61a7a243587", "question": "The main attraction was a reconstructed dinosaur skeleton, it stood tall on a platform in the center of the what?", "question_concept": "platform", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "museum", "park", "arena", "concert hall"]}, "answerKey": ""}
{"id": "144fe132d8a7c178a6acedddc3aff873", "question": "James wanted to stop the message, but it was already out for delivery.  This type of message could he very fast. What is this?", "question_concept": "stop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motor", "telegraph", "organ", "telegram", "email"]}, "answerKey": ""}
{"id": "5bdd8b4357171af3732b505f6093c49b", "question": "What structure is a nightclub in?", "question_concept": "nightclub", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["major city", "building", "manhattan", "city", "downtown area"]}, "answerKey": ""}
{"id": "9e8cf3376a136e9351671606bac8f1f2", "question": "Where would you find an apple tree, a tractor, or plow?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["washington state", "flowers", "farm yard", "new jersey", "old orchard beach"]}, "answerKey": ""}
{"id": "71d66349ea1c21045973528be3874301", "question": "How  will running after a ball need to be done?", "question_concept": "running after ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["catching up with", "physical exertion", "sprinting", "squinting", "tiredness"]}, "answerKey": ""}
{"id": "7fe3d905cb5647ef31ba49e4a5477cd4", "question": "What is the opposite of something that is valuable?", "question_concept": "value", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["expensive", "worthlessness", "disesteem", "invaluable", "worthless"]}, "answerKey": ""}
{"id": "1d9519689d508ced6edfc9a04bb1ada2", "question": "Animals needed to travel large distances, so what traits evolved from this need?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["less fur", "gay", "fly", "drink", "feel pain"]}, "answerKey": ""}
{"id": "eaae9b3976dc98dd116ea08c34d58145", "question": "Where are almost all rosebushes found?", "question_concept": "rosebush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["outdoors", "flower bed", "fountain", "flower garden", "park"]}, "answerKey": ""}
{"id": "4fd9bb4a67bb054432495fc62d41ccef", "question": "They were trying the impossible, to capture the sound of what?", "question_concept": "sound", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["quiet", "music", "silence", "vision", "movie"]}, "answerKey": ""}
{"id": "bc24fc52b5019544aa9ad270a93e64c2", "question": "When you communicate among a group in person what are you doing?", "question_concept": "communicate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["send email", "listen", "talk with people", "think", "speak out"]}, "answerKey": ""}
{"id": "9c816bd4f0517ea0f16dffe7d86123ac_1", "question": "What fires a projectile ball and holds a burning rope?", "question_concept": "projectile ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["flintlock", "motion", "arcade", "tennis court", "mzzleloader"]}, "answerKey": ""}
{"id": "a5d1dea3eaf8c90ff423bb37a6a3a62b", "question": "Where would you find a lizard not native to an area with a lot of rain?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["iraq", "tropical areas", "garden", "pet shop", "desert country"]}, "answerKey": ""}
{"id": "857c967b7c5de845597620e7a158b3db", "question": "What does a person want when they get hurt?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["headache", "not feel pain", "more leisure time", "acquire wealth", "injury"]}, "answerKey": ""}
{"id": "2aaf458e8f6a44aa1fcfb81ff814c720", "question": "Of anybody you want to find where, a salesman is probably the worst surprise behind it?", "question_concept": "salesman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toy store", "front door", "counter", "shop", "car show"]}, "answerKey": ""}
{"id": "7109a210dbec4d6dbd8c276b8ae644fa", "question": "What state is usually always red in the national elections?", "question_concept": "red", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["utah", "louisiana", "texas", "oklahoma", "redtwig"]}, "answerKey": ""}
{"id": "87ddb84c02d2c380159338aded52f268", "question": "What do people do to express when they have fun?", "question_concept": "have fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enjoy yourself", "smile", "laughter", "have sex", "stop working"]}, "answerKey": ""}
{"id": "d32be3cf3fe1b701118331921d31b0f4", "question": "The man was watching television in hopes of catching his favorite show, what was he seeking?", "question_concept": "watching television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["entertainment", "falling asleep", "relaxation", "sleep", "wasted time"]}, "answerKey": ""}
{"id": "4a6f559d71502a656787a180404e041b", "question": "Where might there be a small amount of lint?", "question_concept": "lint", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rug", "pouch", "laundromat", "pocket", "purse"]}, "answerKey": ""}
{"id": "d9dd74ee33260bba54f8708e5385b96a", "question": "I designed some new buying products to help you through financial difficultises. If you owe a lot of money what can this help you get out of?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bankruptcy", "shopping", "debt", "spending money", "agony"]}, "answerKey": ""}
{"id": "0a22e846d650cfab0b6a157fd670639b", "question": "How would you know if someone who does not succeed often is a christian?", "question_concept": "christian", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["commit sin", "fail", "rebel", "attend church", "believe in jesus christ"]}, "answerKey": ""}
{"id": "202ca80830063bde5e0e003a9fc8685d", "question": "Solo feels good when he's creating art.  Seeing his work take form makes him feel immense satisfaction in his skills.  Showing it to others makes him feel what emotion?", "question_concept": "creating art", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["controversy", "communication", "pleasure", "pride", "sadness"]}, "answerKey": ""}
{"id": "0ecca9e9b71eeb9af17abc512a37432c", "question": "Gary was walking for a long time, but all he could see was sand.  He didn't know how far he went, or how much farther he had to go.  Where might Gary be?", "question_concept": "sand", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beach", "desert", "surface of earth", "concrete", "ground"]}, "answerKey": ""}
{"id": "4f997524423ce868d0b1951788405bcb", "question": "What is the overall goal likely to be of someone who is helping others?", "question_concept": "helping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["better world", "obligation", "enjoyment", "will thank", "good feelings"]}, "answerKey": ""}
{"id": "7cca88e55ddd03e225756491eca7140e", "question": "If a young person wants to express themselves, what outlets do they try to find?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["caregiver", "acknowledgment", "art", "creativity", "much money"]}, "answerKey": ""}
{"id": "af846676633ceeebf5e9f87b5098f62b", "question": "If someone is looking at a ceiling in their bedroom, where are they likely located?", "question_concept": "ceiling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "bedroom", "loft", "house", "classroom"]}, "answerKey": ""}
{"id": "544d1957d61d1f97e52d77e5b28673ac", "question": "What does a child learn in school to do?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["count to ten", "state name", "write", "read book", "dress herself"]}, "answerKey": ""}
{"id": "33573f60dac3ea26ebe0e182a9a29fb7", "question": "They were lost in the woods and scared, but praying made them what?", "question_concept": "praying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sense of peace", "wasted time", "relief", "talk to god", "feel safer"]}, "answerKey": ""}
{"id": "ee59810181e1ef04a60b46ed28abb3e9", "question": "He needed stitches, the doctor aimed the point of the what and began?", "question_concept": "point", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["needle", "middle", "alpenstock", "arrowhead", "arrow"]}, "answerKey": ""}
{"id": "3d06dfb3b44ba5606f3ef49ebe7b0b4a", "question": "People were sitting in rows waiting for the performance, where were they?", "question_concept": "row", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arena", "auditorium", "theatre", "farmer's field", "vegetable garden"]}, "answerKey": ""}
{"id": "42ab078f45b2cfa612bffbecaaf01572", "question": "If I am creating art and drop my paintbrush, what is my feeling?", "question_concept": "creating art", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enlightenment", "pleasure", "love", "relax", "frustration"]}, "answerKey": ""}
{"id": "2a1f501e267e4ad1a50f82c33c112bf3", "question": "The person decided they wanted to return the overpriced product, it was really bad quality for the what kind of price tag?", "question_concept": "bad", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["paper", "competent", "sincere", "premium", "upright"]}, "answerKey": ""}
{"id": "d33cd51f8cbdf3c85510174bff994df8", "question": "Why would a ship slow down to avoid a collision?", "question_concept": "ship", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["list to port", "run aground", "heading east", "near dock", "near shore"]}, "answerKey": ""}
{"id": "ca585de81536853a3720b501acba7bab", "question": "What should you do to prepare for a run?", "question_concept": "run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go quickly", "learn to walk", "get out of bed", "eat a salad", "stretches"]}, "answerKey": ""}
{"id": "43a9cb8ede82f7010422ba3588f7ece2", "question": "You neck comes out of where?", "question_concept": "neck", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chest", "body", "shoulder", "bottle", "guillotine"]}, "answerKey": ""}
{"id": "cecb27a6186f77fed1a61949f7d9d6bd", "question": "What happens many times while chatting with friends?", "question_concept": "chatting with friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["agreement", "crying", "communication", "love", "laughter"]}, "answerKey": ""}
{"id": "99cf0fa9b30ac255fc3385cb497e98cb", "question": "The compressor coil was dirty so what wouldn't work?", "question_concept": "coil", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["television", "electric motor", "car", "refrigerator", "circuit"]}, "answerKey": ""}
{"id": "1f9b49c181f14cbcfd043e624011f5fd", "question": "You might store your leotard in the locker room here.", "question_concept": "locker room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["health club", "stadium", "gymnasium", "locker room", "gim"]}, "answerKey": ""}
{"id": "e71d8231251e1ee2916c1fc3d5feaf6e", "question": "The service technician grossly overcharged, but the customer didn't know how to fix the air conditioning of his what?", "question_concept": "air conditioning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["car", "offices", "popsicle", "movie theatre", "house"]}, "answerKey": ""}
{"id": "110ad2fa55cdd30eb08e0ffa0200b61d", "question": "Most of the yards are filled with grass in what glove-shaped state?", "question_concept": "yard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gas", "city", "michigan", "three feet", "subdivision"]}, "answerKey": ""}
{"id": "127549ae53cee4bec9469ec28a091d93", "question": "The company refused to do repairs.  They felt that the cost of fixing the bridge would exceed what.", "question_concept": "cost", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["income", "benefit", "tax benefit", "revenue", "for free"]}, "answerKey": ""}
{"id": "4e55946454064f1a02f1c7015a28cd2a", "question": "Death was permanent for me, so how long did it occur?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["surprise everyone", "last forever", "happen to", "happen quickly", "it is nature's way"]}, "answerKey": ""}
{"id": "e6774b0c2e0ab44180051f4d52ccc1a0", "question": "Someone who enjoys giving assistance is likely to be known for what?", "question_concept": "giving assistance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["helpfulness", "good feeling", "patience", "happiness", "feeling good"]}, "answerKey": ""}
{"id": "c736aace51654949102677a7da7b9022", "question": "If someone spent the night drinking alcohol in Boston, they might wake up with what kind of headache?", "question_concept": "drinking alcohol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wicked", "disturbing", "frequent urination", "vomiting", "accidents"]}, "answerKey": ""}
{"id": "16905db72520432d0fa38d8eb304e826", "question": "A fox is walking around New England, where is a likely place it could be found?", "question_concept": "fox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["northern hemisphere", "undergrowth", "countryside", "mountains", "nantucket"]}, "answerKey": ""}
{"id": "702a2d72ea4563c9c45c6e445441f4e8", "question": "Smoke isn't good for the body, it's basically a slow way to do what?", "question_concept": "smoke", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kill yourself", "think again", "you're stupid", "cigarette", "eat dinner"]}, "answerKey": ""}
{"id": "bbbb2745f89f5d6554c7a317dbbd4ee0", "question": "It seems everybody crucifying the man have forgotten the basics of the system, all people are supposed to be assumed what?", "question_concept": "all people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["due process", "hurt", "different", "innocent until proven guilty", "human"]}, "answerKey": ""}
{"id": "d32234db233c80777ed28873ada7a2b9", "question": "The knife was advertised as being always sharp, but obviously regular use will make anything what?", "question_concept": "sharp", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unobservant", "inaccurate", "dull", "rough", "above board"]}, "answerKey": ""}
{"id": "3037ec4c19bb3286d9ebd9416bb64f66", "question": "Unfortunately he suffered a mortal wound, he was one of many lost in the what?", "question_concept": "wound", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fight", "patient", "injured person", "body", "battle"]}, "answerKey": ""}
{"id": "c1f14c31f4d98445652e806cea770c99", "question": "Jamie is a cowboy.  He owns a horse.  What might the horse do when he digs his boots into the horse's side.", "question_concept": "horse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pet", "go away", "run quickly", "drink water", "sleep"]}, "answerKey": ""}
{"id": "8a1c1a76553e4e8d04b2c9578c06d766", "question": "How might someone avoid boredom?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have fun", "talk more", "play games", "learn new", "go somewhere"]}, "answerKey": ""}
{"id": "a3a634d7ce345192ac43c1789e576bd5", "question": "What emotional response would be elicited from listening to others try to convince you about the efficacy of something which you do not care about?", "question_concept": "hearing testimony", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exasperation", "annoyed", "anger", "boredom", "scrutinizing"]}, "answerKey": ""}
{"id": "bea369dd17724c1a28af2e3f0e1dc212", "question": "Where would find most of a country's major industry?", "question_concept": "industry", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["big city", "los angeles", "factory", "civilization", "michigan"]}, "answerKey": ""}
{"id": "06fdd7ffe69cb4137f09f1d92b163a3d", "question": "When you work out and get hot you need some cooling off afterwards, in the meantime the body gets a head start by what?", "question_concept": "cooling off", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sweating", "air conditioning", "expansion", "shivering", "relaxation"]}, "answerKey": ""}
{"id": "d6bc0f38f5250b18b758cfb31c2bdd69", "question": "Where would you step on a tile?", "question_concept": "tile", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wall", "computer game", "roof", "floor", "ceiling"]}, "answerKey": ""}
{"id": "82222274f049146f6235caa3b175c443", "question": "I bought an upright piano, but my mother says not to leave it outside, where should it put it?", "question_concept": "upright piano", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living room", "band", "college", "house", "back pocket"]}, "answerKey": ""}
{"id": "60a5108218c3a7233bb242f1f7b82a33", "question": "Where would you find a toy soldier that is not being played with?", "question_concept": "toy soldier", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["movies", "ebay auction", "toy box", "toy store", "child's hand"]}, "answerKey": ""}
{"id": "7ce905a29e6b62683ae1548696395934", "question": "You need a like source to enjoy what communication item?", "question_concept": "light source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hallway", "sky", "flashlight", "books", "candy"]}, "answerKey": ""}
{"id": "6c9d67a10f24fd92629620a6e9344dff", "question": "The separate areas in your house are called what?", "question_concept": "house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rooms", "out house", "basement", "living room", "bathroom"]}, "answerKey": ""}
{"id": "51a8956459f8b2e074f0b9c7ed17500c", "question": "What is the closest place where you could borrow a pen?", "question_concept": "pen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "pocket", "friend", "neighbor's house", "desk drawer"]}, "answerKey": ""}
{"id": "95dd791dbe5e9b0c8905211c8401f350", "question": "Jill is at a shopping complex surrounded by traffic and large cities, where is she?", "question_concept": "shopping complex", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["subarbs", "countryside", "michigan", "suburbia", "big city"]}, "answerKey": ""}
{"id": "25b000b2707cf0f53326e5b50380c442", "question": "What state is likely to have an abundance of air conditioners?", "question_concept": "air conditioner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["humidity", "house", "office building", "texas", "warm climates"]}, "answerKey": ""}
{"id": "19fa764527b857aa677b1eb05d493ad7", "question": "Beaver went to school near DC and went for a vacation in Canada.  During his vacation he bought many souvenirs. What is something he might have bought?", "question_concept": "beaver", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["books", "ontario", "woodlands", "washington", "magnet"]}, "answerKey": ""}
{"id": "f912e19d112c410cac12024421f42688", "question": "Where would you install a security system?", "question_concept": "security", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert", "university", "airport", "office", "home"]}, "answerKey": ""}
{"id": "d33f1f8978e37dc80c46433c37e962f2", "question": "What holds a cigarette being lit?", "question_concept": "cigarette", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tobacconist shop", "mouth", "ashtray", "carton", "hand"]}, "answerKey": ""}
{"id": "190df9ea1a3c098c5c5eba943639d455", "question": "What does someone have for a lead person playing guitar who is attractive?", "question_concept": "playing guitar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["compose song", "pick", "practice", "have fun", "desire"]}, "answerKey": ""}
{"id": "1369d9e90a997efbcf35df7d89d8e3a0", "question": "He expected a happy life, but when he was subjected to hardship over and over again, what did he feel?", "question_concept": "happy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unfortunate", "disillusioned", "inappropriate", "disenchanted", "sadness"]}, "answerKey": ""}
{"id": "1bc6614aaf1dd7f12737e63401112ab3", "question": "Where do humans gather for sports?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "mortuary", "television", "conclave", "stadium"]}, "answerKey": ""}
{"id": "cc1c2cc873f9a8de8d74759c3153878b", "question": "Where is the most powerful light source?", "question_concept": "light source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sky", "torch", "books", "house", "lamp"]}, "answerKey": ""}
{"id": "ecbed1b7fe87e1981d5f9fa528d31b46", "question": "They loved reproducing the night with their friends every year, it was the most what thing most of them do?", "question_concept": "reproducing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bizarre", "offspring", "fun", "propagation", "birth"]}, "answerKey": ""}
{"id": "7cf9d225a1d655aedd9cc0eaaed2bccb", "question": "the trainee had been climbing ladders all day and passed the test, when he got home he collapsed on the couch from what?", "question_concept": "climbing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting higher", "falling down", "intoxication", "exhilaration", "exhaustion"]}, "answerKey": ""}
{"id": "a423b820e1675ce51d2eb829c518e59f", "question": "The kids were all in the playroom when the parents came to pick their children up from where?", "question_concept": "playroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "preschool", "big house", "nursery school", "toy"]}, "answerKey": ""}
{"id": "1fa9eedb707a37f897783adb251381b0", "question": "Running when you're out of shape can easily lead to something that might be distressing and uncomfortable.  What can it lead to.", "question_concept": "running", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["breathlessness", "increased heart rate", "calluses", "tiredness", "lactic buildup"]}, "answerKey": ""}
{"id": "bb375dbd3d47d3c58b60de4db9531e1b_1", "question": "The central passage of what leads to an area where a person of royal birth is put to rest?", "question_concept": "central passage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["piramid", "arena", "large building", "public building", "tomb"]}, "answerKey": ""}
{"id": "4e82ba7b8a1197b741e0c7c1ea9a0783", "question": "What does a person want to do by working?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["more leisure time", "own house", "acquire wealth", "not feel pain", "a vacation"]}, "answerKey": ""}
{"id": "3bb8f309e1f19bb386b96985389a515e", "question": "How does one start the process of procreate?", "question_concept": "procreate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["moan", "die", "std", "moaning", "kiss"]}, "answerKey": ""}
{"id": "4434076be3f84f19a91fcd653a9047ec", "question": "To be successful while performing what must you do?", "question_concept": "performing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["accolades", "injury", "tiredness", "do best", "stay home"]}, "answerKey": ""}
{"id": "b164f62bb040e08499661827da2c2db4", "question": "What type of chair has a rung?", "question_concept": "rung", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["highchair", "rocking chair", "straight chair", "rocking chair", "folding chair"]}, "answerKey": ""}
{"id": "f544ba6dc7de86da942d18bc0396a105", "question": "What does someone experience when they discovering truth that is sad?", "question_concept": "discovering truth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["asking questions", "learning", "crying", "surprise", "feeling hurt"]}, "answerKey": ""}
{"id": "633e38c3ad3eaff6fbd006399d22b336", "question": "Where kind of structure are you in if there is a fancy chandelier next to a staircase?", "question_concept": "staircase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mansion", "school", "house", "palace", "cellar"]}, "answerKey": ""}
{"id": "9b75b7316b6f6fd1c28430fabf918502", "question": "Asking and answering questions is essential for a lively what?", "question_concept": "answering questions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["teaching", "panic", "discussion", "correct", "funeral"]}, "answerKey": ""}
{"id": "98efaf3f3a9c78e4c56e2880a4e7d451", "question": "Sally was writing a program in basic.  It wouldn't complete or stop.  It was stuck in what?", "question_concept": "writing program", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bugs", "mud", "unexpected results", "loop", "need to integrate"]}, "answerKey": ""}
{"id": "57accd1c707c9e7b390991cee7e9fc3d", "question": "She bought a home alarm to try to feel safe, but she still felt vulnerable and what of herself?", "question_concept": "safe", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["in danger", "unsafe", "harmful", "insecure", "obesity"]}, "answerKey": ""}
{"id": "2951a7f61d0f4825138930fecc2df16f", "question": "Joe stood in front of the backdrop and watched as the hands prepared the what?", "question_concept": "backdrop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["a tornado", "photography studio", "theater", "photo studio", "stage setting"]}, "answerKey": ""}
{"id": "e9651dacc35dfe3a0428cbe8c77b1047", "question": "The trucks transporting the fruit had refrigerated trailers, this was to help do what to the produce?", "question_concept": "fruit", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go off", "decay", "grow mold", "taste sweet", "keep fresh"]}, "answerKey": ""}
{"id": "eeab50c5a049aef9f1ecfe8f7a872b05", "question": "It's important to keep your software current or the security features may be what?", "question_concept": "current", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["old news", "tomorrow", "outdated", "now", "later"]}, "answerKey": ""}
{"id": "fb3c859b32e6ee649504ea10def6256d", "question": "If a baby is typical, what does it not do?", "question_concept": "babies", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sleep soundly", "like spinach", "giggle", "clap hands", "cry"]}, "answerKey": ""}
{"id": "bc7e740fe5e8935a28bdfe40760cd3aa", "question": "What do you have to be to pass class?", "question_concept": "pass class", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["study hard", "smart", "study", "homework", "turn in assignments"]}, "answerKey": ""}
{"id": "b2e103d4b84cfb586a17628c98e10608", "question": "What might prolonged kissing lead to?", "question_concept": "kissing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get cold", "runny nose", "herpes", "catch cold", "sexual stimulation"]}, "answerKey": ""}
{"id": "a086e49b7864619b40e2b96e4463ca03", "question": "Where might a horse wander around?", "question_concept": "horse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["washington", "farmyard", "glue factory", "race track", "painting"]}, "answerKey": ""}
{"id": "04a281e57410baffbbff0b6f94d948f8", "question": "The lady sought enlightenment and belonging, where did he go?", "question_concept": "lady", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grocery store", "church", "supermarket", "retreat", "bathroom"]}, "answerKey": ""}
{"id": "2da8b0db736e3566400346d97c87ed3d", "question": "A person wanting to be successful needs to work on many things, the most important of which is having high what?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["self esteem", "acquire wealth", "own house", "delicious food", "beautiful flowers"]}, "answerKey": ""}
{"id": "3dc9043ab70ad772e965abea20eedbed", "question": "In order to have a hot lunch what do you need to do first?", "question_concept": "have lunch", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["prepare food", "find food", "have time for", "buy food", "get food"]}, "answerKey": ""}
{"id": "ff504d5cebb63437d7fe4ae3f4b4fea6", "question": "James did a research report on reading books. It is one of the oldest forms of what?", "question_concept": "reading book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enlightenment", "philosophy", "learn things", "nightmares", "entertainment"]}, "answerKey": ""}
{"id": "631da190b213e7333fca35ad459b72be", "question": "What place might have a lot of trash that is not supposed to be there?", "question_concept": "trash", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garbage dump", "subway", "hospital", "dumpster", "dustbin"]}, "answerKey": ""}
{"id": "8c29212609efe7c3a29f5edcc0bb5e1e", "question": "How can a student learn about a marmot?", "question_concept": "marmot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["study hall", "great plains", "north america", "countryside", "encyclopedia"]}, "answerKey": ""}
{"id": "ce1d9ce5e1a06446c90cfbc4d7c3157d", "question": "What may a person get to help them feel respected?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["acknowledgment", "receive gifts", "headache", "third degree burns", "understand themselves"]}, "answerKey": ""}
{"id": "49f17af7f55cdd6a29731589d3a45ed4", "question": "Danny liked to watch birds.  Most birds have an organ that they use to eat.  What is this organ called?", "question_concept": "most birds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wings", "ability to fly", "learn to fly", "beak", "learn to fly"]}, "answerKey": ""}
{"id": "8010142a704066cf4482b89565c5b546", "question": "What does someone have the most of if they lost someone and are resentful?", "question_concept": "most", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mournful", "bitterest", "corrupt", "representative", "cleverest"]}, "answerKey": ""}
{"id": "1d5fc356f8a6bdeba0d39adfb3e5e8bc", "question": "Where do you usually receive a bill at the end of your experience?", "question_concept": "bill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["congress", "restaurant", "table", "envelope", "wallet"]}, "answerKey": ""}
{"id": "374364d492f4852fff1618870d508349", "question": "John was meeting a friend who had a depressing day.  What did he want to do to her?", "question_concept": "meeting friend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["panic", "cheer", "relaxation", "make her feel better", "talk"]}, "answerKey": ""}
{"id": "970761496497d7eca7678e1d1c918a00", "question": "If you exercise to intensely what can happen?", "question_concept": "exercise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shortness of breath", "death", "injury", "fitness", "need for food"]}, "answerKey": ""}
{"id": "6a45cdc3296259f0ae27ad772de92ab0", "question": "What will a student do after graduating college?", "question_concept": "student", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["complete test", "solve equation", "study book", "begin teaching", "finish college"]}, "answerKey": ""}
{"id": "e1ed7f714f9bccb1007fe027c819951c", "question": "There are four main artery in the what?", "question_concept": "main artery", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neck", "torso", "human body", "heart", "body of animal"]}, "answerKey": ""}
{"id": "6ca2a844a53521a8a54f7efe0ec1b10a", "question": "How might someone be going to market?", "question_concept": "going to market", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["that you", "apples", "stress", "walking", "bankruptcy"]}, "answerKey": ""}
{"id": "fa6af9a16599a30d9897f78771dfd9e1", "question": "What does exercise usually hope to improve?", "question_concept": "exercise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fitness", "shortness of breath", "weight gain", "injury", "need for food"]}, "answerKey": ""}
{"id": "43a150efaf18bd859831ab4761dc2436", "question": "WHen do most people pray even when note religious?", "question_concept": "pray", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["you're scared", "in the morning", "talk to god", "salvation", "were religious"]}, "answerKey": ""}
{"id": "7c3c7e958629444608ce15bbfc662498", "question": "Where in South Asia can you find a temple?", "question_concept": "temple", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["india", "middle east", "buddhism", "china", "jerusalem"]}, "answerKey": ""}
{"id": "b82493e188ab497ee2195d4a3ed82c6d", "question": "James didn't like Whoppers, but he still wanted hamburgers.  What type of place might he go to?", "question_concept": "hamburgers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cooked on grill", "mcdonalds", "hockey game", "burger king", "fast food restaurant"]}, "answerKey": ""}
{"id": "fa720b796d0748ff58fe29d37b1e38d7", "question": "What is it called when snow falls fast enough to obscure vison?", "question_concept": "snow", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shovelling", "avalanches", "blizzard", "christmas", "blackout"]}, "answerKey": ""}
{"id": "a4995ea9c7441383d68651cf536e35c6", "question": "What's one of the things you do a lot while attending school?", "question_concept": "attending school", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play games", "taking tests", "get smart", "colds and flu", "boredom"]}, "answerKey": ""}
{"id": "97201bbafa7af5854a3b0c5ee7107fb9", "question": "James looked around the showroom but didn't see anything he liked.  The salesman talked him into trying some things, though.  He felt that he shouldn't have come to this place.  Where is he?", "question_concept": "showroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appliance store", "car dealership", "car lot", "vegas", "city"]}, "answerKey": ""}
{"id": "7f757725231983b77b37efc43bf0d6c4", "question": "John loved visiting the museum.  He spent his time considering what the paintings represented.   He didn't understand all of them, but he did a lot of what?", "question_concept": "visiting museum", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pondering", "tired feet", "being bored", "standing", "gaining knowledge"]}, "answerKey": ""}
{"id": "67590f4b9f40e72bb343b644a827c167", "question": "James had to use the bathroom, but he couldn't do it in a place he wasn't familiar with or in a public place. So he  waited until he got back to his what?", "question_concept": "bathroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus station", "school", "flat", "neighbor's house", "at hotel"]}, "answerKey": ""}
{"id": "04fbbcd5fdf697b61cfd3d6932661029", "question": "Where do you cook the pie?", "question_concept": "pie", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fridge", "refrigerator", "windowsill", "freezer", "oven"]}, "answerKey": ""}
{"id": "4ccad7e34e499faf745bf6d8102e13c8", "question": "What is a fun thing to do when you get too hot on a hike?", "question_concept": "hike", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take photos", "seeing bear", "get wet", "pick berries", "see beautiful views"]}, "answerKey": ""}
{"id": "88c0e5de2977ef959a8c6b759a848dab", "question": "When you get a second chance to live life, an opportunity was what to you?", "question_concept": "live life", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["help people", "have no other choice", "luck", "all know", "was given to"]}, "answerKey": ""}
{"id": "5c95114e3958b40d45d45a0af7e8bd8f", "question": "Bring home fish, clean it, and then you you eat it. What is missing that you have done to it?", "question_concept": "bring home fish", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sushi", "rice", "hungry", "have cooked", "else to eat"]}, "answerKey": ""}
{"id": "4d3db7523e35f1877b05ac93153ba6ba", "question": "Water flows through what when not above ground?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["underground stream", "sump pump", "clouds", "sink", "hydroelectric dam"]}, "answerKey": ""}
{"id": "1fc4b3f15eb693ffe178448d9451de57", "question": "The roof was sagging and leaking, one could say it was in a what?", "question_concept": "roof", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["all buildings", "every house", "shack", "garage", "state of disrepair"]}, "answerKey": ""}
{"id": "deeb58cba711012734fd660378061823", "question": "If you were eating dinner and then you ran to a toilet, what is most likely to happen?", "question_concept": "eating dinner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["throwing up", "heartburn", "indigestion", "sleepiness", "be sick"]}, "answerKey": ""}
{"id": "e1de2aaaa0cb88e3ce797a75079a2269", "question": "Where can you see their performance?", "question_concept": "performance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert hall", "commercial", "show", "movies", "theatre"]}, "answerKey": ""}
{"id": "35c635a87dbbdbfcc7195613f9c8faa3", "question": "There are many semi-wild animals at this place.  What might this place be?", "question_concept": "animal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jungle", "cafe", "pet store", "north america", "park"]}, "answerKey": ""}
{"id": "a9246f1c130a4bfecdfb37f1c7c21f5b", "question": "A human is hitch hiking in Lousiana, where is he likely heading?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment", "new orleans", "department store", "new jersey", "workplace"]}, "answerKey": ""}
{"id": "0e7cc1ba6d074b01b246cb38f9b2f4e3", "question": "What type of place has very expensive wine in their wine cellar?", "question_concept": "wine cellar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["good restaurant", "vineyard", "fancy restaurant", "dark cool place", "italy"]}, "answerKey": ""}
{"id": "59208301872f02855a96b69c82d997d3", "question": "Where could you get something from a company?", "question_concept": "company", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["yellow pages", "phone book", "armed forces", "market place", "flea market"]}, "answerKey": ""}
{"id": "82b9d59b3f1631240fb17764bc7b459e", "question": "Billy wanted a new puppy to bring back to his place.  Where might he find one?", "question_concept": "puppy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kennel", "fire station", "dog house", "table", "home"]}, "answerKey": ""}
{"id": "0e934757a64f62e6d1adc60571b290e3", "question": "Where is a cymbal likely to be used in a finale?", "question_concept": "cymbal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["symphony orchestra", "field", "music store", "drumkit", "marching band"]}, "answerKey": ""}
{"id": "edb522b4d494f62d0b6a344c8f101fbb", "question": "Men and women go out dancing hoping to find someone to do what with?", "question_concept": "dancing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["intercourse", "listen to music", "exhaustion", "expression", "become tired"]}, "answerKey": ""}
{"id": "cf863025cbeb86080dfa7823bb857867", "question": "What could cause friends to all have the same opinion about something?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["group together", "understand each other", "keep secrets", "convincing", "part company"]}, "answerKey": ""}
{"id": "bb665a14e6849e2f9ee62d7334a1afc8", "question": "What would happen if you are expressing yourself illegally?", "question_concept": "expressing yourself", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["empowerment", "misunderstanding", "repercussions", "suffering", "pain"]}, "answerKey": ""}
{"id": "1188320488a3f487ed629dc4732a423e", "question": "The nearsighted man could tell the object took a form, but largely what did he notice about it?", "question_concept": "form", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["large", "change shape", "function", "chaos", "shapeless"]}, "answerKey": ""}
{"id": "f2b949d24975c6ea3b439acec84d5e4f", "question": "Where is a surface where cats usually touch daily?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["warm place", "floor", "barn", "roof", "beam of sunlight"]}, "answerKey": ""}
{"id": "15cc733b75a1a23a9173f172039c21bd_1", "question": "The barber shop was always busy due to foot traffic, where was it located?", "question_concept": "barber shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["small town", "canada", "high street", "mini mall", "neighborhood"]}, "answerKey": ""}
{"id": "7b30bd9183624d5e7268055311883a8a", "question": "John loved wrestling with big sweaty men.  He was very skilled at it, and was thrilled when he pinned them to the mat.  What did John receive when he pinned people to the mat?", "question_concept": "wrestling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bruises", "blood", "erections", "wins", "medal"]}, "answerKey": ""}
{"id": "bfbbf3d49d0918ff31eebaec57edfa7e", "question": "Where at your yard would you store a large container?", "question_concept": "large container", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["juice", "shed", "cabinet", "backyard", "warehouse"]}, "answerKey": ""}
{"id": "9147d8333fd8003dadf286385d8547dc", "question": "What is the probability that a remote will turn on the TV?", "question_concept": "remote", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["attached", "companionable", "close", "enough", "likely"]}, "answerKey": ""}
{"id": "3dd16f0d72e4175ec8f610baa2dddbf2", "question": "Where would you use a changing room along side many types of products for sale?", "question_concept": "changing room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["schools", "nursery", "clothing store", "gym", "department store"]}, "answerKey": ""}
{"id": "443422ec4a92076b1fe8c492e8b27624", "question": "On a long, hard, winter day Joe felt warm water on his face.  It took him a few minutes to realize that the water was coming from him.  They were what?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hockey", "typhoon", "ocean", "snowflake", "teardrops"]}, "answerKey": ""}
{"id": "e811d9d4c23690b0270a29caaac47ce5", "question": "The showroom had all sorts of booths.  He was glad he went to the convention, even if it was all the way in what location?", "question_concept": "showroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appliance store", "convention center", "city", "car dealership", "vegas"]}, "answerKey": ""}
{"id": "53c1362f7d6a6de25c1759517dbe316a", "question": "Where are you most likely to be using a condom?", "question_concept": "condom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["purse", "pharmacy", "bedroom", "drugstore", "on your hand"]}, "answerKey": ""}
{"id": "3961ded51daf0ccca7e3b2e57bb52bb2", "question": "When you're paying with cash and not using bills you'd be using what?", "question_concept": "bill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["restaurant", "paycheck", "coins", "straight", "medium"]}, "answerKey": ""}
{"id": "e63e77860c73a26808022d9f99ed851f", "question": "The forecast called for freezing rain and ice, needless to say it would be what?", "question_concept": "ice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water cooler", "ground", "antarctica", "mixed drinks", "cold weather"]}, "answerKey": ""}
{"id": "9517fcd6781d7e2e504464415c37d34d", "question": "What will always happen when getting in line with many people?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["linearity", "long wait", "late", "irritation", "tempers flare"]}, "answerKey": ""}
{"id": "9c231d9160b4e235121a4b7a965f3c3d", "question": "The friends were troubles, why did they hang out?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["comfort", "borrow money", "borrow money", "part ways", "leave"]}, "answerKey": ""}
{"id": "f75deb6bb2b0a3c4960bf69319dee601", "question": "What could someone make if he or she is inexperienced at making bread?", "question_concept": "making bread", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gratifying", "allergic reactions", "pride", "mess", "loniliness"]}, "answerKey": ""}
{"id": "32ca8ef1dd7ddd83da0bc78cee966980", "question": "In times of turmoil she began praying, it brought her a serene what?", "question_concept": "praying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unrest", "relief", "sense of peace", "feel safer", "wasted time"]}, "answerKey": ""}
{"id": "0a79eabcac09059922ccc9529fc37a10", "question": "Where is a tank type weapon likely to be found?", "question_concept": "weapon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["holster", "concealed place", "police station", "battlefield", "war"]}, "answerKey": ""}
{"id": "1a21a3b77a805f7a453ff7dc69efb845_1", "question": "She was curled up on the couch to watch her show, she used both hands to what when she wanted a sip?", "question_concept": "hands", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["glass of water", "articulate", "soft", "cup water", "sign language"]}, "answerKey": ""}
{"id": "e285435dbbbde6d5627f37b4e21a21a5", "question": "On a ship a cannon is used, what is used on a plane?", "question_concept": "cannon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["body armor", "fire grapeshot", "ungulate", "bomber", "missile"]}, "answerKey": ""}
{"id": "621d6f1302960d2708aae3af062a08ee", "question": "Jenny enjoyed going on vacation, but she didn't do it often. She hated what came with it. What might be a consequence of a vacation?", "question_concept": "going on vacation", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drinking", "debt", "relaxing", "relaxation", "peace"]}, "answerKey": ""}
{"id": "b24bab0aa46be6f5c924b42924b06e7b", "question": "Sally joined the army in peacetime.  There wasn't any combat, but she went many places.  Where did she go?", "question_concept": "army", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fort", "battlefield", "other countries", "war", "sailboat"]}, "answerKey": ""}
{"id": "8dc34875c30acf9084c8dae964f0a69d", "question": "It was his first marathon and he wasn't ready, running the twenty six miles caused him what?", "question_concept": "running twenty six miles", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feel tired", "getting tired", "slight discomfort", "excruciating pain", "tiredness"]}, "answerKey": ""}
{"id": "d92be8de139dc4efe3f6abd9db0e30a7", "question": "After George broke his leg, George's best friend, Tim, gave him the biggest gift box that could be found.   How might George's response be described respond?", "question_concept": "biggest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["effusive", "enabled", "accidental", "detestable", "gigantic"]}, "answerKey": ""}
{"id": "ea36a782b046baa86f103b2743de0024", "question": "If an animal is in the wild, what is its prime directive?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nesting", "procreate", "need to eat", "keep alive", "lie down"]}, "answerKey": ""}
{"id": "8bd918a62468b731fe0862d07481e856", "question": "James wants to talk to people, but he can't because he lacks people.  What might people do when someone talks?", "question_concept": "talk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["express opinions", "open mouth", "will listen", "make sound", "not listen"]}, "answerKey": ""}
{"id": "8c60c8e3e08636401ff86cc30b3b1cae", "question": "Where would you go if you want to get some bread and ice cream, but nothing else?", "question_concept": "goods", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["grocery store", "hardware store", "supermarket", "shop", "mall"]}, "answerKey": ""}
{"id": "1bf4c6b5bd870b1a079106e1e97e5d09_1", "question": "It was a long and dusty thoroughfare, but it passed peacefully through some beautiful what?", "question_concept": "thoroughfare", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["passing through estate", "country", "town", "scenery", "city"]}, "answerKey": ""}
{"id": "bad1c19a2ef6337dd37e036037b3e793", "question": "What does a female flea do after it mates?", "question_concept": "flea", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["attack", "suck blood", "bite", "jump", "lay eggs"]}, "answerKey": ""}
{"id": "164f12b9b6207767703c4328b463afe6_1", "question": "Not all astronauts are pilots, but as a pilot is to aircraft an astronaut is to what?", "question_concept": "astronauts", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["space shuttle", "spaceship", "outerspace", "outerspace", "orbit"]}, "answerKey": ""}
{"id": "ffdb366c7c56fa2e4b99a11ef764d528", "question": "Sam was a servant of Josh.  He pledged himself to Josh years ago.  For this reason, Sam lacked what?", "question_concept": "servant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hired help", "lord", "freedom", "rich person", "in charge"]}, "answerKey": ""}
{"id": "439e5c11eed63b2a2adc859d2be7d93e", "question": "Joe's sink was clogged so he had to ask Brenda for permission to wash his dishes.  Where is he washing dishes?", "question_concept": "sink", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chemistry lab", "neighbor's house", "gym", "laundry room", "home"]}, "answerKey": ""}
{"id": "f6f49e53bfc9c8769bf1bfcdb351f041", "question": "Where in your office would you store an extra pencil?", "question_concept": "pencil", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pocket", "in mid-air", "classroom", "university", "desk drawer"]}, "answerKey": ""}
{"id": "3cc314bf2cabcce9e929361e98493510", "question": "Where is likely to have a checkout desk?", "question_concept": "desk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["schoolroom", "a fast food restaurant", "study", "office building", "library"]}, "answerKey": ""}
{"id": "27f4d2626bc7ec1cf98e235f48a2a35e", "question": "Where is a cow likely to cry?", "question_concept": "cow", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slaughter house", "countryside", "barnyard", "stable", "trailer"]}, "answerKey": ""}
{"id": "8e033d10e04eef8c69f9ed56f2216c1d", "question": "The water ran through the toilet, what is its next stop?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fish tank", "the bus station", "sink", "reflecting pool", "sewage treatment plant"]}, "answerKey": ""}
{"id": "944340d4a80de14c9386f9629c63c7a1", "question": "The record spun on the turntable. What else did it do?", "question_concept": "record", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tape", "melt", "erase", "play music", "compact disc"]}, "answerKey": ""}
{"id": "3da502d3d6e997ac27632292401ddc76", "question": "What would you need if you did not know what a marmoset is?", "question_concept": "marmoset", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["veterinarian", "underground", "dictionary", "rainforest", "colorado"]}, "answerKey": ""}
{"id": "3d716eaf3dede711bc675c5ad3b8f6e7", "question": "Where do people play baseball?", "question_concept": "baseball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "sporting goods store", "break window", "america", "skating rink"]}, "answerKey": ""}
{"id": "33791ed38d9a78fadc5da4079322a87d", "question": "Bobby hated competing but his parents didn't see. He failed and they just said he should be doing what?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["working harder", "death", "winning or losing", "trying harder", "pressure"]}, "answerKey": ""}
{"id": "399c34b2d835e8b9a8ed648055e829ea", "question": "Where would you find a row of seats while watching men holding sticks and wearing pads?", "question_concept": "rows of seats", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hockey game", "theater", "bus", "train", "theatre"]}, "answerKey": ""}
{"id": "b778c1f82c3b0ad5527933d1d209a416", "question": "What has a heart that is good to eat?", "question_concept": "heart", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chicken", "all mammals", "turkey", "artichoke", "person"]}, "answerKey": ""}
{"id": "f22baf2383e46c60da210971ca2d4bc6", "question": "With what would you see a soprano doing the exact same performance multiple times?", "question_concept": "soprano", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["performance", "choir", "movie", "opera house", "choit"]}, "answerKey": ""}
{"id": "6ed9e23231b59fa7e6572f98f9a51cad", "question": "What would you pick up with a spoon", "question_concept": "spoon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["serving dish", "dishwasher", "glass of iced tea", "dinner", "lunch"]}, "answerKey": ""}
{"id": "64f0ea7a464183f008983c6c10482136", "question": "From where would you take a magazine home without paying?", "question_concept": "magazine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["library", "bed", "bookstore", "shop", "magazine rack"]}, "answerKey": ""}
{"id": "e6e46bddd22054d29e5418ecf08f2909", "question": "Where can you a letter opener and a document shredder?", "question_concept": "letter opener", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office supply store", "stationery store", "storage room", "sharp", "dek"]}, "answerKey": ""}
{"id": "76b27670cba4f44a8ee9b34bd1030b7c", "question": "The ranch house was build in a gated community, where was it located?", "question_concept": "ranch house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["countryside", "town", "subdivision", "montana", "desert"]}, "answerKey": ""}
{"id": "e89d4ceb93cdd638d8c1db5755cbe892", "question": "What is a reason that you should be careful when using a candle?", "question_concept": "candle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["light house", "burn brightly", "emit light", "wax build-up", "fire hazard"]}, "answerKey": ""}
{"id": "0da335602a9f5f9fea0a79d5e746085d", "question": "Why do I need air conditioning in the summer?", "question_concept": "summer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cold", "weather", "nice", "winter", "fall"]}, "answerKey": ""}
{"id": "e2949cc8dbfa8a2f176555a2a4556ed5", "question": "Space objects need something to travel, what is it?", "question_concept": "comets", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["solid nucleus", "set orbits", "ice", "universe", "space"]}, "answerKey": ""}
{"id": "b0a19f9e45e314a9aff80f9ece6c7e84", "question": "Where would you put coal if you do not want to give anything else to someone?", "question_concept": "coal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bed", "fire", "under the tree", "underground", "stocking"]}, "answerKey": ""}
{"id": "7ad5bd16e8bbd6632688fec8a699fde5", "question": "What should a person do before talking?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ask question", "think", "walking", "write", "sneeze"]}, "answerKey": ""}
{"id": "3ca421f7c946fbcd376c177770bc4559", "question": "What could happen if you are not good at driving a car?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rush away", "gain speed", "suffer damage", "brakes will be in frequent action", "go fast"]}, "answerKey": ""}
{"id": "1c4cd8b5346c300b3bc0713d40802bcd", "question": "Where do people sit in a chair to pray?", "question_concept": "chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["friend's house", "synagogue", "office", "auditorium", "at the beach"]}, "answerKey": ""}
{"id": "efda7a003898940f989b0599d57dcf77", "question": "The man was walking many miles a day despite not enjoying it, what was his goal?", "question_concept": "walking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["blisters", "getting somewhere", "locomotion", "staying fit", "lose weight"]}, "answerKey": ""}
{"id": "b2a15146ca85877d4c6299253a97a285", "question": "The man told the people the time for deliberation was over and they must what?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["welcome change", "believe in god", "make choice", "hate each other", "talk to each other"]}, "answerKey": ""}
{"id": "390ff70035f08854ccc394303b7de309", "question": "A person is working, why is he doing that?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["source of income", "fair trial", "to get a paycheck", "white teeth", "own house"]}, "answerKey": ""}
{"id": "8a302864a78cbdf6302f6965d43fd76b", "question": "What does the sun do that allows everything to be seen clearly?", "question_concept": "sun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shine brightly", "brown skin", "solar eclipse", "warm room", "dry ground"]}, "answerKey": ""}
{"id": "4ba4704f07b6b94260db144c9a550f40", "question": "The volunteer had been working very hard, the boss noticed and decided to offer him a what?", "question_concept": "working", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concentration", "bonus", "energy", "ambition", "job"]}, "answerKey": ""}
{"id": "e442c6202814bb6d886e96138d36841c", "question": "The soldier was listening intently, what was he achieving?", "question_concept": "soldier", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fight enemy", "report for duty", "guard border", "receive orders", "fight for freedom"]}, "answerKey": ""}
{"id": "6af8f65914f793cca04594f4746a5493", "question": "Why is fresh food harder to get in the winter?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["provide comfort", "lack of availability", "cost money", "increase in price", "cost lot"]}, "answerKey": ""}
{"id": "43e79adbca4bcb2cc1d1693636961ef1", "question": "Where is someone likely to keep a cardboard box?", "question_concept": "cardboard box", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bathroom", "back alley", "shoe store", "warehouse", "storage area"]}, "answerKey": ""}
{"id": "a2d8f152f1d52293bdf208a86d38b54d", "question": "She had to run errands, she wrote a list of them so she could mark off each time she what?", "question_concept": "run errands", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["complete tasks", "finished", "helpful", "supplies", "get things done"]}, "answerKey": ""}
{"id": "a58f47fb7651c4e982ad5fd7332f3623", "question": "The group thought the were changing society, but in all honesty they didn't even have a full what of the issue?", "question_concept": "changing society", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mouth", "understanding", "change yourself", "action", "revolution"]}, "answerKey": ""}
{"id": "89d61c6df56dfbb42115c416424f94c0", "question": "What would a kid who is going to play not want to do?", "question_concept": "going to play", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rush", "happy", "sit", "meet", "being entertained"]}, "answerKey": ""}
{"id": "0735cd64e9e6633036ac20c0eea026d9", "question": "An android wants to become human in a place where the water quality is questionable.  Where is this place?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["detroit", "elevator", "downtown", "space shuttle", "moon"]}, "answerKey": ""}
{"id": "123abb29c3eabce97512c706df944895", "question": "In what state of being are you most likely to see a monkey?", "question_concept": "monkey", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["captivity", "barrel", "thailand", "south africa", "new mexico"]}, "answerKey": ""}
{"id": "bc750462fdc2741200e336e6e977b234", "question": "Glass is used to build these so you can see outside.", "question_concept": "glass", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "dishwasher", "window", "cabinet", "water cooler"]}, "answerKey": ""}
{"id": "bdd544ab468a0fc7642f013aaf486f05", "question": "What negative effect can happen when you are buying products that are dangerous and then use the products by yourself?", "question_concept": "buying products", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["owning", "agony", "pleasure", "disagreements", "spending money"]}, "answerKey": ""}
{"id": "e4203cee7598be2e33d05e17a7cd6b2c", "question": "What could happen to you after cleaning for a long time?", "question_concept": "cleaning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take trash out", "injury", "neatness", "getting tired", "allergies"]}, "answerKey": ""}
{"id": "8f7417ffb74e058bbf98a8929fdfd5ed", "question": "Where would a computer user use someone else's computer?", "question_concept": "computer user", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office", "house", "office building", "school", "hell"]}, "answerKey": ""}
{"id": "05d9acb5acdd07070eb6c5749f40bca1", "question": "Where could you find a toilet a large number of people use, but that would require permission to use?", "question_concept": "toilet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["market", "apartment", "rest area", "hospital", "motel room"]}, "answerKey": ""}
{"id": "eb42b61dcdafe19082304a5516b8c36e", "question": "Where is one likely to find a tennis court?", "question_concept": "court", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["palace", "gymnasium", "school", "ontario", "public building"]}, "answerKey": ""}
{"id": "be28cb55ee9566bb1ec25bbc8f310024", "question": "John felt that there was an anaconda snake in his room.  He thought it slithered over him.   It was huge.  But when he turned on the lights there was no snake.  It was only what?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nightmare", "tropics", "feild", "wyoming", "a feather"]}, "answerKey": ""}
{"id": "305d7ec91951db58e7bd5980718f431f", "question": "Where should you take your jeans if you can't clean them at home?", "question_concept": "jeans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bedroom", "laundromat", "closet", "prom", "shopping mall"]}, "answerKey": ""}
{"id": "4cb6a36274c46e5c975b38a9ea55782f", "question": "When people are hot and seeking a cold treat, what do they do?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["like ice cream", "lower expectations", "dance well", "believe in god", "sleep"]}, "answerKey": ""}
{"id": "fd28207dc1418cfb60f06d41760b1268", "question": "If somebody just bought a new car, they might keep the title secure in a what?", "question_concept": "title", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["describing", "magazine article", "safe deposit box", "shoe", "library"]}, "answerKey": ""}
{"id": "63fec9936cf86ded4224881945aea93f", "question": "What are found at every bus station that show you how to get around?", "question_concept": "bus station", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["street", "signs", "busy city", "city centre", "maps"]}, "answerKey": ""}
{"id": "6f00a6ae7cf38be4915b025f92f27135", "question": "What is likely to be full of small grids?", "question_concept": "grid", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["template", "graph paper", "mathematics", "electrical system", "coordinate plane"]}, "answerKey": ""}
{"id": "7f1d80b9ccb9617c347ebb8e681a610e", "question": "What is orange juice often kept in?", "question_concept": "orange juice", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["carton", "fridge", "pulp in", "vitamin c", "refrigerator"]}, "answerKey": ""}
{"id": "0bd0b376a6c139ca18841e949ed0efe5", "question": "Where can you buy a can of soda while at a highway rest stop?", "question_concept": "can of soda", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vending machine", "restrooms", "picnic cooler", "store", "liquid"]}, "answerKey": ""}
{"id": "77c16232cc9261bc4bbb4a3648339f49", "question": "Why are children scared of going to bed?", "question_concept": "going to bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get pregnant", "monsters", "sleepiness", "bad dreams", "insomnia"]}, "answerKey": ""}
{"id": "1f343bc5d0bace3534f458d2f41a67d5", "question": "She enjoyed relaxing and being taken away into another world, she spent a lot of time doing what?", "question_concept": "relaxing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["listening to music", "flying", "reading", "falling asleep", "listen to music"]}, "answerKey": ""}
{"id": "dd739adc57acd93b3cad4aeb87f3602e", "question": "What happens when cashing in a lot of chips at a casino?", "question_concept": "cashing in", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["increase in money", "making money", "drink hot drinks", "getting money", "get rich"]}, "answerKey": ""}
{"id": "f30ccf756f489ff1a35c32116a94c624", "question": "You're like to find a gazelle in Africa or Asia, both of which are where?", "question_concept": "gazelle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["asia", "television program", "eastern hemisphere", "open plain", "great outdoors"]}, "answerKey": ""}
{"id": "31f9bf65926574c9a6dd30a2fdf25047", "question": "My boss is a messy man, where does he carelessly toss all of his junk mail?", "question_concept": "junk mail", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "mail box", "waste bin", "trash", "post office"]}, "answerKey": ""}
{"id": "3b5d321d08bdb1d3247b7dd59b6dce17", "question": "Where would a hooker give services?", "question_concept": "hooker", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["town", "at hotel", "at hotel", "corner of two streets", "street corner"]}, "answerKey": ""}
{"id": "1a866fc699a5a6a70995c406737dd908", "question": "The paper towels are sometimes kept in the office, where did the dad look for them?", "question_concept": "paper towels", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabinet", "grocery store", "pantry", "waste bin", "locker room"]}, "answerKey": ""}
{"id": "e8d41d7f57974294aa7210873c6b53fd", "question": "If you're standing in line for a long time what will you likely experience?", "question_concept": "standing in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anger", "parking ticket", "wait turn", "order", "fatigue"]}, "answerKey": ""}
{"id": "f52ce7e62d99c29aac88b0b4268fd546", "question": "Where is an unused garden hose likely stored?", "question_concept": "garden hose", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["front yard", "bedroom", "garage", "backyard", "back yard"]}, "answerKey": ""}
{"id": "d6144b08728acb3252d62b54ca946d8c", "question": "Where can someone get a sweater on sale?", "question_concept": "sale", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["k mart", "outlet store", "department store", "clothing store", "classified adverisements"]}, "answerKey": ""}
{"id": "92eb20796bc60593751040c2eef90f90", "question": "The mechanic has many tools at his?", "question_concept": "tools", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neighbor's house", "grocery store", "drawer", "garage", "repair shop"]}, "answerKey": ""}
{"id": "a5a94b02b0fb9359159bfa70390acbd7", "question": "Mandy had an affair with Chris while wed  to Robert.  Mandy broke her promise of what?", "question_concept": "affair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["discipline", "divorce", "marriage", "relationship", "fidelity"]}, "answerKey": ""}
{"id": "e6050629eb052764030e252973e40976", "question": "When you're working for someone else what should you expect?", "question_concept": "working", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["blisters", "getting paid", "creation", "make money", "making money"]}, "answerKey": ""}
{"id": "e6abf05fe7dea9997692461560a35a17", "question": "The reporter was set on discovering truth, he tracked down leads and kept what?", "question_concept": "discovering truth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learning", "calling", "feeling hurt", "asking questions", "crying"]}, "answerKey": ""}
{"id": "e58eb0ec4197c29e961a7bdd4d67de4e_1", "question": "He believed in competing honestly, being humble in victory and gracious in what?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["defeat", "aggression", "sweat", "race", "winning or losing"]}, "answerKey": ""}
{"id": "7cd143a621339234947098d6b3a1f343", "question": "Where would I put some chocolate after buying it?", "question_concept": "chocolate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["box", "movies", "supermarket", "mouth", "restaurant"]}, "answerKey": ""}
{"id": "bc8327e16bba023dd651816180d178d4", "question": "If people start going the wrong way what must they do?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talk to each other", "waste paper", "travel abroad", "change direction", "keep goin"]}, "answerKey": ""}
{"id": "65acf7c01dd3e7745ff7d7ad0d63f911", "question": "The rescue was very difficult, but id led to what?", "question_concept": "rescue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arrest", "kidnap", "corrupt", "finish", "abandon"]}, "answerKey": ""}
{"id": "6991339de4302a6acbe1ba29c15860eb", "question": "The fruit of an apple tree becomes ripe when?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spring", "valley", "new york", "summer", "fall"]}, "answerKey": ""}
{"id": "2d349f2faa1354c3f8beeb8cb14bf317", "question": "He thought of his recovering sick mother and began praying, he hoped that she was what?", "question_concept": "praying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["religiosity", "feel better", "pain free", "relief", "feeling better"]}, "answerKey": ""}
{"id": "6756267f644f22b0289b3b8610d130fb", "question": "What is a common emotion that takes place during a date at a skate rink?", "question_concept": "skate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling down", "romance", "jumping around", "grab side railing", "spin"]}, "answerKey": ""}
{"id": "858f75e8b59f0c85145abea802b4f27d", "question": "Where could you find out how to use a chess board?", "question_concept": "chess board", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["internet", "park", "retirement community", "cabinet", "library"]}, "answerKey": ""}
{"id": "d9fd0b1631c1ab93a39d4c11a71331e5", "question": "What would happen to flowers if they are watered?", "question_concept": "flowers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["plant themselves", "continue to grow", "drawn", "many colors", "smell good"]}, "answerKey": ""}
{"id": "1b7d859e723437af25854d12f332c51e", "question": "What is constantly judging others likely to lead to?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lot of pain", "feeling bad", "controversy", "being blind about other people", "hurt feelings"]}, "answerKey": ""}
{"id": "d53ac158543a16c054f8e5d8912ff0bc", "question": "A new beauty salon had just opened up in her what, so she decided to walk over and check it out?", "question_concept": "beauty salon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beautifying hair", "strip mall", "functions", "neighborhood", "clerk"]}, "answerKey": ""}
{"id": "a08ae1f55e872ec5d9c2a14a4b2ced1c", "question": "Visiting museum that is a memorial to war made the person appreciate what?", "question_concept": "visiting museum", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["seeing artifacts", "education", "peace", "wonder", "being bored"]}, "answerKey": ""}
{"id": "fb3e5c284e6c222345cd0222082cecfa", "question": "In the shower many find happiness with what activity?", "question_concept": "happiness", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["live life", "play games", "jump in a puddle", "fiddle", "sing"]}, "answerKey": ""}
{"id": "bd64d02c00bcac6d11f1a4574a8957f0", "question": "When the dad discovered the killing of his son, what did he feel?", "question_concept": "killing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["suicide", "death of", "angry", "grief", "not living"]}, "answerKey": ""}
{"id": "ae74c12e30a5a4d6536fac0e82a7424a", "question": "If you use too much water when preparing nourishment what may you end up with?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dribble", "wet clothes", "drink", "thin soup", "power turbine"]}, "answerKey": ""}
{"id": "93c929a5e786422facac96d31b07b3b2", "question": "Where would you find a trunk containing a spare blanket or pillow?", "question_concept": "trunk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "automobile", "bedroom", "car", "zoo"]}, "answerKey": ""}
{"id": "f4471a8ea531bdd319ec6fd7ec78ed65", "question": "What do most adults have to do?", "question_concept": "adult", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pay bills", "work", "dress himself", "dress herself", "drink beer"]}, "answerKey": ""}
{"id": "61d0e01a9c6de4cfc3193f81e981c904", "question": "Joe enjoyed playing chess, but he was not a master.  He lost often.  This taught him something. What did it teach him?", "question_concept": "playing chess", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forgiveness", "headaches", "boredom", "humility", "frustration"]}, "answerKey": ""}
{"id": "e75db3ed7e3ea670df3da47400787d73", "question": "Why do people think dogs are so smart?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["do tricks", "bark", "jump up", "do many things", "own people"]}, "answerKey": ""}
{"id": "2c150e59838e17e8889493439d84a18a", "question": "Where do you have someone who is always refilling your cup?", "question_concept": "cup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment", "restaurant", "kitchen cabinet", "driveway", "dishwasher"]}, "answerKey": ""}
{"id": "346ece7dfa9695a98fade1d2401a3c09", "question": "Love, like justice, can be said to be what?", "question_concept": "love", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["contagious", "last forever", "balance scales", "painful", "blind"]}, "answerKey": ""}
{"id": "a73d40fc2c189d34fbdc4dfd70db6b8a", "question": "Why is a person not hungry?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meet expectations", "have enough food", "sleeping", "own house", "knowledgable"]}, "answerKey": ""}
{"id": "9634281c655779e843a56aa1ee09e79a", "question": "What does standing in line for a long time lead to?", "question_concept": "standing in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["upset", "agitation", "fainting", "frustration", "boredom"]}, "answerKey": ""}
{"id": "3c0c20a7234a7ce62902e5e9a58bf33d", "question": "The instructor was teaching dance, she started the class turning on music and telling everybody to just start what?", "question_concept": "dance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["partner", "celebrate", "sitting still", "like dancing", "moving"]}, "answerKey": ""}
{"id": "ccdddee8fb94797d0839befd4e1ae40e", "question": "Where are villages typically located?", "question_concept": "village", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["third world countries", "big city", "ohio", "rural area", "africa"]}, "answerKey": ""}
{"id": "7eef0b3ded8e872cb6b21acab5feaa7e", "question": "After she finished washing clothes, what did the woman do with them?", "question_concept": "wash clothes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bubble bath", "use water", "use soap", "get dirty", "gather up"]}, "answerKey": ""}
{"id": "a522f36d0afb3f11dc5ee1123c7685fc", "question": "Where can you find more than one table?", "question_concept": "table", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rug", "corner", "demonstration", "library", "under the bed"]}, "answerKey": ""}
{"id": "e3c9699b46e09e3ae60dd247b9818ce7", "question": "Why do people traveling from one place to another and bring bed and other furniture with them?", "question_concept": "traveling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motion sickness", "fatigue", "jet lag", "school", "relocation"]}, "answerKey": ""}
{"id": "c618331ce270b929cb00872a56e5845a_1", "question": "The service was incredibly slow, he used the toilet twice during the what for his order where?", "question_concept": "toilet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stadium", "house", "school", "space shuttle", "restaurant"]}, "answerKey": ""}
{"id": "961e8f1d17cd8c68b3d05aed557a9c3a", "question": "There is a lot of noise in what group of people's homes and businesses?", "question_concept": "noise", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["carnival", "big city", "factory", "classroom", "concert"]}, "answerKey": ""}
{"id": "26c1497e4026356a97416dc8a6334667", "question": "What is a harpsichord likely to be a part of?", "question_concept": "harpsichord", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert hall", "make music", "music shop", "museum", "band"]}, "answerKey": ""}
{"id": "7ef358fa222e8a4d32dbf94b2a889baa", "question": "Getting up early is about the only way you're going to be able to get what on a project?", "question_concept": "getting up early", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["starting early", "bloodshot eyes", "good weather", "feeling tired", "sleepiness"]}, "answerKey": ""}
{"id": "abf9ffb83096337e77a8327d256632f5", "question": "Where would you find the icebox in your home?", "question_concept": "icebox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["antique store", "kitchen", "junk yard", "house", "basement"]}, "answerKey": ""}
{"id": "415bdaba073e29b5cbc4f14df8c50056", "question": "Danny was great at socializing.  He found it easy to do what?", "question_concept": "socialising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enjoy himself", "making friends", "anxiety", "having fun", "have fun"]}, "answerKey": ""}
{"id": "31d1daee2ca5aa7666b6c20ceee7489d", "question": "What can I put water in, for drinking?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pool", "soup", "puddle", "glass", "sink"]}, "answerKey": ""}
{"id": "4ffe455df39aef3d817e56afd5dc8df0", "question": "What happens to someone after being caught killing people?", "question_concept": "killing people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feelings of guilt", "going to the slammer", "jailed", "prison sentence", "going to jail"]}, "answerKey": ""}
{"id": "198d038d5105e7810948e5bbfa2d1096", "question": "Where is a good place for a bureau?", "question_concept": "bureau", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home office", "office building", "french government", "bedroom", "bedroom"]}, "answerKey": ""}
{"id": "4fc4ffec37124cdfe7245f0f4a816c4b", "question": "How do kids form ideas about unknown things?", "question_concept": "kids", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["color", "become adults", "wonder about", "open door", "distracting"]}, "answerKey": ""}
{"id": "bcb09fcd6fadd79356f80d9e0da5b139", "question": "What do most people have that help them to read?", "question_concept": "most people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ears", "two eyes", "two arms", "two hands", "fingers"]}, "answerKey": ""}
{"id": "6969c77289aab12ab399f2783ac56a60", "question": "If a dog is very active, it gets around a lot on what?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["four legs", "feet", "teeth", "paws", "two ears"]}, "answerKey": ""}
{"id": "507f6fd160b9ca199c39f2d5be58aa85", "question": "Vance wanted to buy a big ficus.  Where might he have looked for one?", "question_concept": "ficus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["large pot", "park", "street", "tropical forest", "green house"]}, "answerKey": ""}
{"id": "88a1d42f52a5f64a174d52e3dc96da9b", "question": "The shark got stuck in the sand, where was he?", "question_concept": "shark", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["court room", "pacific ocean", "shallow waters", "poker game", "sand box"]}, "answerKey": ""}
{"id": "********************************", "question": "Gasoline is a polluting way of moving car, many hope to replace it one day with a cleaner what?", "question_concept": "moving car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["force", "energy", "strength", "obesity", "drive"]}, "answerKey": ""}
{"id": "44e6167c3a2e91a671f267a7182f9d29", "question": "WHere are most office buildings located?", "question_concept": "office building", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["high tower", "city", "downtown area", "business park", "industrial complex"]}, "answerKey": ""}
{"id": "e09bdf84babdcedc7c9a3bc332138add", "question": "A city is surrounded by lots of different countries within a few hours drive, where is it located?", "question_concept": "city", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["county", "wilderness", "united states", "germany", "meadow"]}, "answerKey": ""}
{"id": "4241c618ad14e404005bae050d808142", "question": "I am Weasel could talk.  Since most weasels can't talk, he was probably what sort of thing?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["law office", "cartoon", "woodland", "own home", "ferret"]}, "answerKey": ""}
{"id": "ddbb83cc14429e67d6a391827cecfdae", "question": "If a dog escaped from your property, where would be the closest place to look?", "question_concept": "dog", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dog pound", "back yard", "farmyard", "park", "neighbor's house"]}, "answerKey": ""}
{"id": "305eb7fbf0257e009c40a00bfeb62110", "question": "What will someone have to do once they have began to start a family?", "question_concept": "start family", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sleep", "stressed", "have sex", "need more money", "spend money"]}, "answerKey": ""}
{"id": "fbd32871913d534e3371e1672e78e164", "question": "When James was passing sentence on the killer, he took into account something about the survivors.  What might James have taken into account?", "question_concept": "passing sentence", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appeals", "death", "loss", "anger", "grief"]}, "answerKey": ""}
{"id": "a6ebd2b924e3863b5aa7ac8032daf25b", "question": "The protest was peaceful, but what did it become after someone started throwing rocks?", "question_concept": "peaceful", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["violent", "belligerent", "nonsense", "warring", "nonpeaceful"]}, "answerKey": ""}
{"id": "4b06d650a437e367b9f077be054ee005", "question": "Where would you put some pennies if you do not plan to use them soon?", "question_concept": "pennies", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water fountain", "pocket", "purse", "drawer", "jar"]}, "answerKey": ""}
{"id": "6497e30a12e2b9172360e14250f0d878", "question": "The odd things kept happening to him that day, their happenings became what?", "question_concept": "odd", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["normal", "frequent", "habitual", "common", "regular"]}, "answerKey": ""}
{"id": "7c2464c8b30cf987f4625be73def05ee", "question": "Where are you if you go up a staircase before hearing a lecture?", "question_concept": "staircase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["multistory building", "mansion", "school", "cellar", "library"]}, "answerKey": ""}
{"id": "727ea894ef83f61674c4bd1f8da3b031", "question": "What does learning accomplish?", "question_concept": "learning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gaining knowledge", "knowing more", "anger", "growth", "headaches"]}, "answerKey": ""}
{"id": "0615ba4a7b8dca151eb6e92749f147d2", "question": "The person used a sponge, what were they trying to do?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["catch cold", "deceive himself", "take a nap", "experience pain", "absorb moisture"]}, "answerKey": ""}
{"id": "899a9a65d3abe7ac507266d5612dba01", "question": "Where can litter ruin your bbq or Frisbee game?", "question_concept": "litter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["roadsides", "street corner", "park", "ground", "playground"]}, "answerKey": ""}
{"id": "9dbb818a6a343b6fb98807f05ff3ee29", "question": "Where can you find a younger woman in a rocking chair?", "question_concept": "rocking chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["old folks home", "the womb", "child's room", "front porch", "parlor"]}, "answerKey": ""}
{"id": "31f43125aabe5653a1eda342a61aeb82", "question": "Where would you hear a french horn play?", "question_concept": "french horn", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["music class", "music store", "participate in orchestra", "parade", "concert hall"]}, "answerKey": ""}
{"id": "563de3554defac91d0699c683908398c", "question": "What might someone do to rights to stop someone from feeling empowered?", "question_concept": "empower", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dishearten", "prohibit", "belittle", "forbid", "ban"]}, "answerKey": ""}
{"id": "2fdd987a56351f5177b2117267c77399", "question": "If a place specializes in drinks that requier a decanter, what is it unlikely to be?", "question_concept": "decanter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["restaurant", "chemistry lab", "kitchen cupboard", "brewery", "mens club"]}, "answerKey": ""}
{"id": "6adbf046ba4652eecd8cd7509f504d5a", "question": "He was locked in a room against his will, where did he long to be?", "question_concept": "room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "outside", "wireroom", "understand themselves", "hallway"]}, "answerKey": ""}
{"id": "95f86a6f1de9b29b96ee5d7d054864a2", "question": "Passengers are walking the passageway to an airplane, where are they?", "question_concept": "passageway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airport", "cave", "maze", "store", "house"]}, "answerKey": ""}
{"id": "a5542221fb2a633c43b9c983aa0806e4", "question": "Ever since he got with his girlfriend he slowly lost all his friends, she was now his only what?", "question_concept": "girlfriend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["enemy", "foe", "buddy", "friend", "comrade"]}, "answerKey": ""}
{"id": "3a2e8c6d8241cc272280a3c8efd8f969", "question": "What do you have to do while you're at work?", "question_concept": "work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have job", "get dressed", "concentrate", "get going", "make money"]}, "answerKey": ""}
{"id": "5a8ddb276c52d37fbcd6004a3fe4e614", "question": "If you have to bend over frequently while doing housework, what are you likely to experience?", "question_concept": "doing housework", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tiredness", "backache", "get tired", "headache", "sneezing"]}, "answerKey": ""}
{"id": "f81c78911520f7ee32b7a09abd35d249", "question": "Where can you buy a bullet?", "question_concept": "bullet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bandolier", "bullet store", "casing", "gun shop", "magazine"]}, "answerKey": ""}
{"id": "a749db34e710945120155ace0fecb965", "question": "Talking in front of a class means you are delivering a?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["presentation", "voice", "being alive", "tongue", "speech"]}, "answerKey": ""}
{"id": "776aa3de19bcd9d07dfd196dd1657817", "question": "The person spent more money than he had, what is the result?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["experience pleasure", "value life", "wash dishes", "go into debt", "catch cold"]}, "answerKey": ""}
{"id": "fd5ecbfe7061f2e1f55c4d70a0541684", "question": "What is the spine a part of in the human body?", "question_concept": "spine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["skeleton", "ribcage", "back", "human body", "book"]}, "answerKey": ""}
{"id": "9ab2f6ed68f47a467ca0b468491783e8", "question": "What can a player do after reaching a tentative agreement?", "question_concept": "reaching tentative agreement", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relax", "calmness", "breathe", "uncertainty", "satisfaction"]}, "answerKey": ""}
{"id": "e4c035ca674187417feda8a48499e824", "question": "If you see a marmot on a flat plain, you're probably where?", "question_concept": "marmot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ontario", "north america", "sierra nevada mountains", "mountainous region", "great outdoors"]}, "answerKey": ""}
{"id": "44fc31666c8948dff76073fc61216614", "question": "What is the opposite of mountain?", "question_concept": "mountain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["another mountain", "molehill", "flat land", "valley", "prairie"]}, "answerKey": ""}
{"id": "6840dda956bad3a218b75c00bf31b88a", "question": "What is the best result of having a heart attack?", "question_concept": "having heart attack", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bypass surgery", "recover", "fear of death", "consuming pain killer", "loss of life"]}, "answerKey": ""}
{"id": "50a6b5deecffa5eee9129cdf75371232", "question": "After surgery the lady was resting, it was part of her what?", "question_concept": "resting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["healing", "feeling better", "relaxation", "recuperation", "doing nothing"]}, "answerKey": ""}
{"id": "687cae0a8e97186060ffdefc2d5c0adf", "question": "How does a person begin to attract another person for reproducing?", "question_concept": "reproducing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["marry", "have sex", "kiss", "birth of new person", "genetic mutation"]}, "answerKey": ""}
{"id": "89cf15542e98e890baa21260ca92c412", "question": "How are plants able to take water and nutrients from the soil?", "question_concept": "plant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bloom", "flower petals", "with a sponge", "have roots", "bottle drinks"]}, "answerKey": ""}
{"id": "0358949aebf0f5ecd92f3f33ff3f50d3", "question": "If someone is in a southern state and watching a game at a baseball field, what genre of music is likely playing?", "question_concept": "baseball field", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["baseball stadium", "japan or america", "detroit", "country", "loud"]}, "answerKey": ""}
{"id": "1e10346e34ea0f34828da9f2bfe34d64", "question": "What's an important reason that a person looks for a job?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["headache", "high wages", "party", "stay alive", "husband or wife"]}, "answerKey": ""}
{"id": "f62f43f23c42211d8fa5d95ec5ab3cc7", "question": "Kids who have a modern taste in music find that their parents are a bit what, when it comes to their music tastes?", "question_concept": "modern", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classical", "obsolete", "bland", "old fashioned", "historical"]}, "answerKey": ""}
{"id": "bf2c0495e7edfb81433f9dbef3ad21dc", "question": "Where could there be a battle that does not involve words and is real?", "question_concept": "battle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["war", "movie", "court room", "stadium", "video game"]}, "answerKey": ""}
{"id": "3e54c0771cc05ddef8b4101c180939df", "question": "What is the basic motivation for flying in an airplane on business?", "question_concept": "fly in airplane", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go far", "like bird", "to read", "get home", "go someplace"]}, "answerKey": ""}
{"id": "89b8b5dd885960200a646357d1e21530", "question": "The student misspelled both words on the test, he didn't realize choose and pool had what next to each other?", "question_concept": "both", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["do with money", "shells", "two vowels", "same letters", "two syllables"]}, "answerKey": ""}
{"id": "023576e9f2a9d5ff777b154963116abf", "question": "Billy loved the evening.  It was his favorite part of what?", "question_concept": "evening", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["day time", "daytime", "early morning", "night", "afternoon"]}, "answerKey": ""}
{"id": "1f4a7b36f38f4ae813e674077bf3b2e0", "question": "If someone was breathing in to their cold hands, they are using what to combat the cold?", "question_concept": "breathing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relaxation", "warm air", "living", "body heat", "stay alive"]}, "answerKey": ""}
{"id": "b918cbbda399c54288f9373401e5112d", "question": "Somebody wants to look at monument in a natural setting. Where do they go?", "question_concept": "monument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["national park", "big city", "public gardens", "large city", "state park"]}, "answerKey": ""}
{"id": "d4ad368b772bf814c61a2b38783e6f7c", "question": "John walked into his surprise birthday party wearing his birthday suit.  He was sad that all of his friends saw him when he was not what?", "question_concept": "sad", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cheerful", "gleeful", "happy", "crying", "decent"]}, "answerKey": ""}
{"id": "ca77c633155bb01dad5d8e9d0923fd20", "question": "From where did you receive a fortune after eating a meal?", "question_concept": "fortune", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["imagination", "treasure chest", "waiter", "cookie", "bank"]}, "answerKey": ""}
{"id": "771e99f1720f2a782d11a1a981edf95b", "question": "Where can you find ketchup packets?", "question_concept": "ketchup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fast food restaurant", "parking lot", "refridgerator", "grocery store", "refrigerator"]}, "answerKey": ""}
{"id": "6c05171d59f3a46a7ce294809a6a4ef4", "question": "The man owned a large property with a ranch house, but there wasn't much landscaping besides spiky stuff because it was located where?", "question_concept": "ranch house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desert", "countryside", "texas", "mountain range", "montana"]}, "answerKey": ""}
{"id": "d3dddc43ad1dbd197652eafbade03d68", "question": "How did immigrants historically head to other countries?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["board ships", "believe in god", "suffer hunger", "study books", "driving"]}, "answerKey": ""}
{"id": "66f946d9069927d1ebec0f8a883fc718", "question": "Having immortality means you will not be?", "question_concept": "immortality", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dead", "dying", "mortal", "death", "mortal"]}, "answerKey": ""}
{"id": "079f259c68211ca0be0d9510ce7c8ddc", "question": "I was having a bath after a day at a construction site, what am I aiming for?", "question_concept": "having bath", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have fun", "use water", "swimming", "being clean", "wrinkled skin"]}, "answerKey": ""}
{"id": "27afc3c8e62e6638421ba7bf33bec730", "question": "What should you do before and after play sports?", "question_concept": "play sports", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take off uniform", "get in shape", "practice", "run", "stretch"]}, "answerKey": ""}
{"id": "76d87daedf5019dd928890bba42a2036", "question": "Where could a marmot walk from one continent to another?", "question_concept": "marmot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["north america", "countryside", "south america", "new zealand", "africa"]}, "answerKey": ""}
{"id": "f770f65efda710aba448ffe93fa4a106", "question": "where do students typically make friends?", "question_concept": "friend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["at school", "workplace", "social atmosphere", "playground", "neighbor's house"]}, "answerKey": ""}
{"id": "73f4e17433320c171fb8d9e6c9f5b457", "question": "I always keep a picture of my wife in my room, where might I set it?", "question_concept": "picture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["frame", "shelf", "art show", "table", "desktop"]}, "answerKey": ""}
{"id": "742e9e86f0e615e6d81c36ceebd24d91", "question": "Where might someone store homemade soup?", "question_concept": "soup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cupboard", "supermarket", "jar", "bowl", "container"]}, "answerKey": ""}
{"id": "82b573bc9b84b1afd744ae15cbe76a79", "question": "What do children do after all day at school?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wash dishes", "need care", "come home", "watch television", "walk the dog"]}, "answerKey": ""}
{"id": "6143fbb00ba8ed3e0df6fb31a68c42e8", "question": "Where is an address label often used?", "question_concept": "address label", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["envelope", "parcel", "trash", "cell phone", "desk drawer"]}, "answerKey": ""}
{"id": "058771de8f47ed828bde7a01b783fbdd", "question": "The patient was still dying, in spite of the fact that the treatment made her what?", "question_concept": "patient", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appear better", "take pills", "will to survive", "visit doctor", "feel better"]}, "answerKey": ""}
{"id": "8adc90ed047cb821793c84567a14d2e2_1", "question": "They just needed a drop, it was just a small prick of the skin on the what?", "question_concept": "skin", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["finger", "own family", "good health", "body", "hand"]}, "answerKey": ""}
{"id": "00d6944c50f8eded35ca81a0ba4a401a", "question": "What is the goal for most people engaging in procreating?", "question_concept": "procreating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pregnancy", "having children", "children born", "population increase", "family"]}, "answerKey": ""}
{"id": "e7857fbf32e51f2af5b9e1237166698a", "question": "What is often wrong with the paycheck of someone doing housework?", "question_concept": "doing housework", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["become tired", "flu symptoms", "low pay", "boredom", "nice home"]}, "answerKey": ""}
{"id": "747e081856a7afa2ebe675548eaab9eb", "question": "If you see a mouse scuttling across the floor, you are almost certainly not where?", "question_concept": "mouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garden", "department store", "addition", "cupboard", "small hole"]}, "answerKey": ""}
{"id": "d43aa9ef38f0f66e8a58cad271ba1175", "question": "The children were fighting in their shared area and crashed down on the bean bag chair, it burst spreading it's filling all over the what?", "question_concept": "bean bag chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living room", "floor", "family room", "carpet", "person's home"]}, "answerKey": ""}
{"id": "e062243582b62469d611416407bd03f9", "question": "The child was fascinated by the way the one person moved their hands, when the child saw the other person understood he too wanted to learn what?", "question_concept": "hands", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["articulate", "cup ball", "sign language", "writing", "cup water"]}, "answerKey": ""}
{"id": "5664448835978e05b6618397d4027d85", "question": "The garage was small and opened right up to the main thoroughfare, where was it located?", "question_concept": "garage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["downtown", "neighbor's house", "stubhub", "modern house", "car"]}, "answerKey": ""}
{"id": "1e0f85e8e7125d9e2e5f1d7c7b58fb44", "question": "The insect needed to escape the predator on the ground, what did it do?", "question_concept": "insects", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["move", "sense vibrations", "flutter", "fly", "buzz"]}, "answerKey": ""}
{"id": "ed7b62b75c5bd500f272eefd02c0082c", "question": "The planners had just broken ground on the new locations, the aisle layout was something they had to consider while what?", "question_concept": "aisle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["department store", "church", "grocery store", "planning", "building"]}, "answerKey": ""}
{"id": "15c2dfa273bccab70a82705ef92a3103", "question": "Where might a person get fungus?", "question_concept": "fungus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["salad", "closet", "basement", "locker room", "grocery store"]}, "answerKey": ""}
{"id": "82ccc66cfbaa8cd46620068b1bf4db95", "question": "What might someone do with 52 things out of boredom?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["surf net", "play chess", "play cards", "watch film", "yawn"]}, "answerKey": ""}
{"id": "61c2e79926ae7de0491e074dc5449e62", "question": "If a train is ahead of schedule it is likely to do what?", "question_concept": "train", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arrive late", "arrive on time", "arrive early", "transport mail", "slow down"]}, "answerKey": ""}
{"id": "5c1930c58a1e7c23d7179bbbf467775e", "question": "If a person is in the middle of a track during a race with a clock, what are they likely doing?", "question_concept": "clock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fixing a clock", "stop working", "working correctly", "time event", "fail to work"]}, "answerKey": ""}
{"id": "4f4f7b1a0bccde7a4f24ad46c0990ae1", "question": "The birds stay in the nest, waiting for their kids to do what?", "question_concept": "birds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat cake", "roof", "mate", "peck", "hatch"]}, "answerKey": ""}
{"id": "1abd2c5e8982d999e228cd7b4414dd7b", "question": "A bird is in the sky, looking around, what is it looking for?", "question_concept": "sky", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["photo", "outside", "looking up", "outdoors", "place on earth"]}, "answerKey": ""}
{"id": "06430138ca8d06d34a470601a0406c67", "question": "What can the sun turn a muddy patch into?", "question_concept": "sun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shine brightly", "dry clothes", "brown skin", "dry ground", "warm ground"]}, "answerKey": ""}
{"id": "97c5f6ab01ad3cf9e36ae4d7f81e50e9", "question": "When someone is talking but its too low, you tell them to do this louder?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sharing of ideas", "talk", "speak", "sneeze", "debate"]}, "answerKey": ""}
{"id": "baa5513abb40b2a5c0a9dd23fa9a7b16", "question": "The man wanted an authentic pastrami deli restaurant, where should he go get one?", "question_concept": "restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new york", "canada", "building", "town", "ice cream stand"]}, "answerKey": ""}
{"id": "ee227b3dcca746954dd3e678aae6693c", "question": "Where would a clerk give you some keys?", "question_concept": "clerk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["airport", "post office", "at hotel", "valet", "bookstore"]}, "answerKey": ""}
{"id": "b09b779fc2a413e8e80b35bebf037b70", "question": "A grain of sad becomes the core of something precious inside of what?", "question_concept": "grain of sand", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beach", "create pearl", "clam", "sand pile", "bathing suit"]}, "answerKey": ""}
{"id": "43d7c90107b096f90648e90143a0cec0", "question": "The bachelor didn't have much furniture, but it at least made his small what look more spacious?", "question_concept": "furniture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "apartment", "bed", "floor", "neighbor's house"]}, "answerKey": ""}
{"id": "fe2d561c9041b8e16ac82ea63ae007d5", "question": "What sort of vendor sells an assortment of fruits such as grapes in an outside location?", "question_concept": "grape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["winery", "supermarket", "painting", "bowl of fruit", "fruit stand"]}, "answerKey": ""}
{"id": "d162be2dd37f5e5666814b40d7770809", "question": "Where would you put an iron after you use it?", "question_concept": "iron", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["house", "golf bag", "linen closet", "bathroom", "laundry room"]}, "answerKey": ""}
{"id": "eeeeb573acedcf03ff4526f037c486c7", "question": "It was now her time to take stand in the trial, she swore to what?", "question_concept": "take stand", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["to cry", "witness", "testify", "tell truth", "runaway"]}, "answerKey": ""}
{"id": "6945b3c8b372fa7e328d0bb64c6827d0", "question": "Death has a relationship to everyone, what is that relationship?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fast", "happen to", "happen quickly", "last forever", "surprise everyone"]}, "answerKey": ""}
{"id": "19b9b63fc69d357c0e68cad2b2270d5f", "question": "What is it called when you remember something?", "question_concept": "remembering", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["recall", "brain cells", "former life", "intelligence", "learning about"]}, "answerKey": ""}
{"id": "830513648a1ac1472a1c2b2aa39d2411", "question": "If you were on safari in this country and saw a gazelle, where would you be?", "question_concept": "gazelle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wildlife refuge", "ivory coast", "asia", "open plain", "america"]}, "answerKey": ""}
{"id": "d602595c008cd703a51134de370ff992", "question": "After jumping up and down and starting to sweat, what sensation would you be feeling?", "question_concept": "jumping up and down", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exhaustion", "lot of noise", "hiccups", "fatigue", "getting warm"]}, "answerKey": ""}
{"id": "18513e42111fbaabf96f890769937005", "question": "A beaver is unlikely to be found in what?", "question_concept": "beaver", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["great outdoors", "american forests", "the ocean", "pair of pants", "zoo"]}, "answerKey": ""}
{"id": "dcfbe580bf0157007804711d44f1eaaf", "question": "What do you need to do before you eat vegetables?", "question_concept": "eat vegetables", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clean", "get gas", "stand", "prepare", "open mouth"]}, "answerKey": ""}
{"id": "02f80bcd213706e073576a9caafad173", "question": "His faith was often tested, but he put what in the gospel and where it would take him?", "question_concept": "faith", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["belief", "trust", "faith", "light way", "experience"]}, "answerKey": ""}
{"id": "4e3f62f98e9fcd5dea7bdb8acfaea4ec", "question": "Where do many people live along with their neighbors, outside of the city center?", "question_concept": "neighbor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "next house", "ditch", "suburbs", "house next door"]}, "answerKey": ""}
{"id": "e850d800d88ad2871fb13aea72d4df88", "question": "where do nerds keep a pen?", "question_concept": "pen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "desk drawer", "pocket", "alone in room", "office supply store"]}, "answerKey": ""}
{"id": "6b1a2a487fe7cd225216a77e88b745d9", "question": "When learning about the world, if you really want to understand the current situation, you should learn history and this requires what?", "question_concept": "learning about world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["intelligent", "smartness", "open mind", "pleasure", "enlightenment"]}, "answerKey": ""}
{"id": "e447a391c921ebbf85afe4ec106399c1", "question": "What might a field filled with flowers be called?", "question_concept": "field", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wavefield", "countryside", "flower road", "rural area", "meadow"]}, "answerKey": ""}
{"id": "2603a533f5d3cb54e331815cbb308276", "question": "James was not an orthodox person.  Whenever he was asked to conform, he responded in what way?", "question_concept": "orthodox", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["recalcitrant", "conservadox", "heretical", "recalcitrant", "liberal"]}, "answerKey": ""}
{"id": "cbca29aba3613de3525839da8bdaa5b5", "question": "How do fast moving children affect adults?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rush around", "teach parents", "calm them", "watch television", "play video games"]}, "answerKey": ""}
{"id": "fd6bb314184c0388a62a4e62bba5e3de", "question": "What did people with a good conscience do in the 1800s?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["appear beautiful", "suffering pain", "free slaves", "talk to each other", "be friendly"]}, "answerKey": ""}
{"id": "d71cc8879853399a302e2cadcd0a180a", "question": "Where could you see a mammoth?", "question_concept": "mammoth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desert", "movie", "forest", "zoo", "wild"]}, "answerKey": ""}
{"id": "f2a01f771003e67d9ea2df8321ad2128", "question": "the gambler was attempting bring suit against the slot machine manufacturer, he claimed the odds were fixed and the machine didn't have true what?", "question_concept": "bringing suit", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["randomness", "aggravation", "great expense", "work ethic", "resentment"]}, "answerKey": ""}
{"id": "de2dbc5c01f08fccc62181ea4a926da6", "question": "He was competing for the third time, he really enjoyed what?", "question_concept": "competing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["scorekeeper", "opponent", "competition", "effort", "skill"]}, "answerKey": ""}
{"id": "52f292c7f9d1e4ad2d10e7477df87746", "question": "When he screws in a cable, what is he fixing?", "question_concept": "screw", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tool box", "motorcycle", "computer", "monitor", "wall outlet fixture"]}, "answerKey": ""}
{"id": "d1b1f231e3396f106ff6cff0b54111c2", "question": "What prevents someone from learning language?", "question_concept": "learning language", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["problems", "frustration", "better communication", "confidence", "trouble"]}, "answerKey": ""}
{"id": "7fce5aac8a95de2dc2e770389e466128", "question": "Where is the gift shop that people by city souvenirs at?", "question_concept": "gift shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["museum", "hotel", "disneyland", "grocery store", "airport"]}, "answerKey": ""}
{"id": "3e7fb680404ec490cb6b46bcbd0a469d", "question": "Where is the closest place you might find a yoyo?", "question_concept": "yoyo", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["brother's room", "own home", "pocket", "toy shop", "in a relationship"]}, "answerKey": ""}
{"id": "3f7927d73b867d3a63d82f15f13db63c", "question": "Even after discovering the truth, what did the stalwart child exhibit?", "question_concept": "discovering truth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pain", "wars", "distraught", "denial", "startled"]}, "answerKey": ""}
{"id": "28c89cc4ef19608bd87686909235f90f", "question": "He enjoyed chatting with friends honestly, they were all true friends and didn't need any what from each other?", "question_concept": "chatting with friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["social approval", "truthfulness", "laughter", "exchanging information", "will laugh"]}, "answerKey": ""}
{"id": "602f06c342bf0e1e7fdccdfeabb4c104", "question": "What place might a child feel secure?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["most homes", "mother's womb", "in a pen", "orphanage", "school"]}, "answerKey": ""}
{"id": "3cd8ebdfec1dc64ec21a70122db49134", "question": "What is likely to have metal feet?", "question_concept": "feet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shoes and socks", "table", "floor", "desk", "boots"]}, "answerKey": ""}
{"id": "c8eea772c0d3e495fb27a421c1321741", "question": "Sally and James bought new shirts and packed them. Where might have they packed their shirts?", "question_concept": "shirts", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buttons", "suitcase", "closet", "sporting goods store", "suitcase"]}, "answerKey": ""}
{"id": "966ea002d044e753cbda6f403444de98", "question": "What is someone likely to feel after wrongly judging someone?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["feeling bad", "lot of pain", "hurt feelings", "happy", "responsibility"]}, "answerKey": ""}
{"id": "6f66187e200d73b6fb1a03c124764286", "question": "Tim had a small gas stove.   He cooked on it a lot.  Where would Tim have a small gas stove?", "question_concept": "stove", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["living room", "apartment", "porch", "friend's house", "tent"]}, "answerKey": ""}
{"id": "014d62152b64376e0e8cdf0e0ccb4158", "question": "How could someone go to work?", "question_concept": "go to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get out of bed", "have job", "take bus", "get ready", "taxi"]}, "answerKey": ""}
{"id": "8193f811b54a2f64503e690a81766c6b", "question": "Where is the safest place for eyeglasses?", "question_concept": "eyeglasses", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["case", "breast pocket", "desk drawer", "shirt pocket", "michigan"]}, "answerKey": ""}
{"id": "a737c2fae5656a33e9e31aad363e7d09", "question": "Be aware of our own mortality is hard for many, for some of those believing in their god can what?", "question_concept": "god", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["less grief", "work miracles", "anything", "give peace", "judge men"]}, "answerKey": ""}
{"id": "a5151e21152ffeba204a51d702077002", "question": "What is a place with many walls that could have a chess board?", "question_concept": "chess board", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["germany", "game shop", "library", "house", "room"]}, "answerKey": ""}
{"id": "25020186c6eb047b65c31665b8fce5d9", "question": "Bob read the magazine while he waited in line.  He wanted to get a novel, but he didn't want to read it here.  Where is he?", "question_concept": "magazine", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["waiting room", "flea market", "shop", "bookstore", "train station"]}, "answerKey": ""}
{"id": "f6c519334c8b155f8837598c95d809ac", "question": "Where do you see signs showing you where to set up a tent?", "question_concept": "signs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["state park", "freeway", "private property", "fork in road", "demonstration"]}, "answerKey": ""}
{"id": "fab873bb6dc4f2c9056baad9c4f857cd", "question": "Why do philosophers spend so much time learning about the world?", "question_concept": "learn", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["become knowledgeable", "improve yourself", "have tools", "have more knowledge", "intelligent"]}, "answerKey": ""}
{"id": "8472891d2e2baa3e95284fb66715b600", "question": "What could happen if you are listening to someone who is answering questions with very long sentences?", "question_concept": "answering questions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["polite", "satisfaction", "boredom", "cheering", "irritation"]}, "answerKey": ""}
{"id": "9f635b0e76ae817314d74eb9f2ecad09", "question": "Where might just one map be kept?", "question_concept": "map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "atlas", "gas station", "amusement park", "backpack"]}, "answerKey": ""}
{"id": "43f4543887ad603c7d9a187ddcc0e827", "question": "Where could you see some chocolate that is not real?", "question_concept": "chocolate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "movies", "candy factory", "restaurant", "candy store"]}, "answerKey": ""}
{"id": "ceb4ff8c131bfce127531b536430d70a", "question": "James is writting a program but he doesn't understand why it's giving him the output that it is.  He knows that complex code can often produce what?", "question_concept": "writing program", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["loop", "unexpected results", "frustration", "errors", "need to integrate"]}, "answerKey": ""}
{"id": "1b8259b7332bad1cf8e56a08049fea3b", "question": "The lizard wanted to learn about the world, where did it look?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["costa rica", "encyclopedia", "lizard school", "garden", "captivity"]}, "answerKey": ""}
{"id": "4cd1daf1aeca6931e6aeaca9661e2896", "question": "What does a person have to do before going to work?", "question_concept": "going to work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy shoes", "malaise", "leave home", "making money", "stress"]}, "answerKey": ""}
{"id": "66a64a67308088a5581d185d8815b662", "question": "He was making friends from all walks of life, he liked to keep an what about everyone?", "question_concept": "making friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["smiling", "open mind", "smile", "common interests", "talking"]}, "answerKey": ""}
{"id": "8879bd744e04d698b552484101d4d8b2", "question": "What makes a human similar to a sea sponge?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["two arms", "consciousness", "muscles", "one body", "body and mind"]}, "answerKey": ""}
{"id": "9f0111a457b4b7dab1da310415d647a9", "question": "Where would you put a wedding ring after proposing?", "question_concept": "wedding ring", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["finger", "church", "in a box", "box", "jewelery shop"]}, "answerKey": ""}
{"id": "a0fc7a4636b11c3b6b08873f3503ef38", "question": "Where could a car become damaged and need a mechanic?", "question_concept": "mechanic", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["highway", "machine parts", "repair shop", "race track", "garage"]}, "answerKey": ""}
{"id": "cdb458320801b3a89777f34c8cdd5a54", "question": "What might someone do as a hobby that will keep them from being cold?", "question_concept": "being cold", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["make patchwork quilt", "light fire", "go swimming", "get warm", "knit"]}, "answerKey": ""}
{"id": "761538adda5a35d30d3f41e354c640c7", "question": "WHat doesn't often happen to trees naturally?", "question_concept": "tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fall down", "cast shadow", "burn", "branch out", "provide shelter"]}, "answerKey": ""}
{"id": "f333000ded17e62b87355949d8b96c32", "question": "Most everyone at least partially agrees on some basic human what that society has?", "question_concept": "everyone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unique personality", "feelings", "electrical circuit", "make living", "values"]}, "answerKey": ""}
{"id": "727b07571c58d6cb98865961aa15b1c9", "question": "Where would you find a forest that has a large amount of rain?", "question_concept": "forest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["africa", "countryside", "national park", "amazon basin", "temperate zone"]}, "answerKey": ""}
{"id": "44fd884b7a03d5ff6b8a0fa5126e1f19", "question": "A gentleman was giving a tour of the greatest palaces and museum, where was he giving them>", "question_concept": "gentleman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["paris", "club", "church", "suit", "europe"]}, "answerKey": ""}
{"id": "c7e4e5829ee9ad26b801fe0889d721a0", "question": "If you raise food animals where would you put a shed?", "question_concept": "shed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["farm yard", "hold things", "backyard", "backyard", "ranch"]}, "answerKey": ""}
{"id": "0638db6a74413dbae26b1d65725110e5", "question": "The man made a living using computer to create software, everybody at work uses a what by him?", "question_concept": "using computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["program created", "stress", "keyboard", "pleasure", "increased efficiency"]}, "answerKey": ""}
{"id": "be6ef28148974512686aacd1a7463c22", "question": "Sam pretended to go to work every day, because he couldn't tell his wife about his what?", "question_concept": "work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relaxation", "unemployment", "desk", "do nothing", "workhour"]}, "answerKey": ""}
{"id": "8cd59b279b5af91fb0cb8335fd5516b6", "question": "Where does a street performer keep their harmonica?", "question_concept": "harmonica", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rock band", "its case", "music store", "pocket", "mouth"]}, "answerKey": ""}
{"id": "379a3a817a2b225dd849efc173c4df4d", "question": "What would be the result of hiring cooks?", "question_concept": "cooks", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["better food", "not your own choice of foods", "bread fish", "prepare meals", "season with salt"]}, "answerKey": ""}
{"id": "b8cd5bc0ed1f45290d180c6468e73583", "question": "What periodical has articles and pictures?", "question_concept": "picture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wall", "table", "newspaper", "desktop", "book"]}, "answerKey": ""}
{"id": "15ad2d0c4ceb47701ed34a549311aa1f", "question": "What American area is likely to have many kosher restaurants?", "question_concept": "kosher restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new york city", "jerusalem", "jewish neighborhoods", "seattle", "boston"]}, "answerKey": ""}
{"id": "fd491ed1457a6b37f1e246498bf07d3e", "question": "What is a place that you can usually see a pillow in regardless of where you are in it?", "question_concept": "pillow", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["make seat softer", "home", "bedroom", "rest area", "motel"]}, "answerKey": ""}
{"id": "c364577af9adffb9e7920a772b8e36e5", "question": "Where can someone pick up a drug from a licensed professional?", "question_concept": "drug", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pharmacy", "cupboard", "bottle", "medicine cabinet", "grocery story"]}, "answerKey": ""}
{"id": "2e5bf096d00e72abca2fd344b9902a5f", "question": "What becomes more prevalent on white people as they spend more time in the sun?", "question_concept": "sun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dry clothes", "dry ground", "brown skin", "shine brightly", "hot feet"]}, "answerKey": ""}
{"id": "8494f80ab586308db6247293662555aa", "question": "Where do the best athletes go?", "question_concept": "athlete", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stadium", "locker room", "sporting event", "olympics", "bench"]}, "answerKey": ""}
{"id": "b1c3c73bc40f8c79f6731f845088d713", "question": "What are students getting when they read book in school?", "question_concept": "read book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["experience", "education", "open up", "knowledge", "learning"]}, "answerKey": ""}
{"id": "7faa58c01932e22f23783a15f00afe41", "question": "Politicians run what part of the country?", "question_concept": "politicians", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["washington d.c", "legislate", "government", "field questions", "parliament"]}, "answerKey": ""}
{"id": "7f4a224827a03149f5c3ba86a561dfcd", "question": "What is fancy mustard stored in?", "question_concept": "mustard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["glass", "fast food restaurant", "refrigerator", "jar", "fridge"]}, "answerKey": ""}
{"id": "bddfdd79ebbc32ce047b967cd6ab5e09", "question": "Where are you likely to find a large closet?", "question_concept": "closet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["store", "bedroom", "house", "school", "coats"]}, "answerKey": ""}
{"id": "2309b5e442bcfbc45c3b22fda39b9f99", "question": "The surgeon traced the nerve to its source, where did he end up?", "question_concept": "nerve", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["human body", "brainstem", "nose", "person's body", "animal"]}, "answerKey": ""}
{"id": "815870bc57582b783e9e5246e275b2a6", "question": "What do you get the day after drinking a lot of alcohol?", "question_concept": "alcohol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drunkenness", "present", "amnesia", "hangover", "burn"]}, "answerKey": ""}
{"id": "86ac1713b4e09b1f8bbce8228af15885", "question": "She insisted it would be entertaining, but still some felt what?", "question_concept": "entertaining", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boredom", "favors", "gratification", "laughter", "fatigue"]}, "answerKey": ""}
{"id": "3a9be28536949a1b9a2b2914bbf6dee2", "question": "If a person works hard and applies themselves, what is the end result for them?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["will succeed", "thank god", "death", "own property", "bring home"]}, "answerKey": ""}
{"id": "50b48381f8367c4b4b19d7606f6a78dc", "question": "Where would a police officer be likely to stop a car?", "question_concept": "police officer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["underground", "case", "city", "street", "beat"]}, "answerKey": ""}
{"id": "2e307ed8cdc5f3a74b97f865fef2d434", "question": "What waiting area is typically next to a trash can?", "question_concept": "trash can", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motel", "alley", "bus stop", "park", "corner"]}, "answerKey": ""}
{"id": "7dc62c5e6733eedaea1db5c221b9e518", "question": "What do you typically need to keep spitting up when youi're sick?", "question_concept": "spitting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["snot", "saliva nd mouth", "spittle", "disease", "phlegm"]}, "answerKey": ""}
{"id": "fef4406ca970fbb91c9fcb52b753ca9a", "question": "Where may a Jewish person go to worship?", "question_concept": "god", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["compassion", "mosque", "heaven", "synagogue", "church"]}, "answerKey": ""}
{"id": "f3ea1ea425cfe1ea2bff6589976fc29a", "question": "Over one hundred thousand fans filled the stadium to watch the football field, it was a big rivalry match up for the what?", "question_concept": "football field", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["state", "players", "city", "high school", "university"]}, "answerKey": ""}
{"id": "4b6c5407673e33a691ab081ff99ea87e", "question": "Where should you take a crab delivery invoice?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["red lobster", "stew pot", "human resources", "chesapeake bay", "boss's office"]}, "answerKey": ""}
{"id": "1884a3fb3f50879e8c7432c649b96880", "question": "What likely has one bench?", "question_concept": "bench", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus stop", "garden", "train station", "bus depot", "rest area"]}, "answerKey": ""}
{"id": "d01e4fd41aa0a4ab2b2cf1a6341ccee5", "question": "Where would you find an audience watching football?", "question_concept": "audience", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["television", "arena", "theatre", "concert hall", "presentation"]}, "answerKey": ""}
{"id": "ea619f8fa41694248f52a71e44c99b54", "question": "There were endless things to see during the trip, but the child's favorite exhibit was the mammoth at where?", "question_concept": "mammoth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["antique store", "antarctica", "forest", "pleistocene", "smithsonian institution"]}, "answerKey": ""}
{"id": "7d56afe44746efe099c883255dafacf3", "question": "If a ferret was being affected by a hurricane in September 2018 he probably lives where?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["out of doors", "comic book", "moon", "outdoors", "north carolina"]}, "answerKey": ""}
{"id": "035336786850646c673a6b3f0953347f", "question": "In an Irish pub what is likely to happen when people are drinking alcohol?", "question_concept": "drinking alcohol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["do a jig", "have fun", "inebriation", "vomit", "start singing"]}, "answerKey": ""}
{"id": "fd9c4345ae1dbae62ff4b3398b38a20e_1", "question": "She dug quarters out of her jeans to wash her jeans, the next couple hours would be spent where?", "question_concept": "jeans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clothing store", "gap", "supermarket", "shopping mall", "laundromat"]}, "answerKey": ""}
{"id": "6339b9156ee1ff029eebfa60e2b77148", "question": "What does a cat do when the sun is on it?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat food", "see at night", "sun itself", "run away", "cast shadow"]}, "answerKey": ""}
{"id": "4d9a83c32929d05d5302f3417be8a3fb", "question": "What old type of building is likely to have no ventilation system?", "question_concept": "ventilation system", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["house", "hospital", "office building", "large building", "shed"]}, "answerKey": ""}
{"id": "205e5e205ced0e8c7a16f40aaf110314", "question": "What does a person do after a long day of work?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hurry home", "feel loved", "gain weight", "feel rested", "laugh out loud"]}, "answerKey": ""}
{"id": "e6493a7784066498ca71ff5f0a267414", "question": "Name some mediums for literature.", "question_concept": "literature", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "public library", "shelf", "books and magazines", "own home"]}, "answerKey": ""}
{"id": "3084dbc71b99d87eee99fc1f45d5d40a", "question": "Danny knew they were talking, but he couldn't make out what they were saying. He wished he could read lips.   At this distance he couldn't even be sure if they were doing what?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sore throat", "attempting to communicate", "making sound", "people to listen", "hanging out"]}, "answerKey": ""}
{"id": "30ec8fef21199b46599ae9c10a3476ce", "question": "What do people do when they are enjoying the process of procreate?", "question_concept": "procreate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["moaning", "die", "std", "kiss", "leave will"]}, "answerKey": ""}
{"id": "c72bd9d5562b02645694f7e9014749f4", "question": "A shareholder in a manufacturing firm is part owner of what structure that creates products?", "question_concept": "shareholder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "large company", "annual general meeting", "factory", "research center"]}, "answerKey": ""}
{"id": "856c44264a264bdd45da0d74b70f79d1", "question": "What is the place in the atlantic ocean where its tectonic plates meet?", "question_concept": "atlantic ocean", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["submarines", "earth", "planet", "basin", "atlas"]}, "answerKey": ""}
{"id": "f2d0bd0551aca2858d66e3a631b5a005", "question": "Where would you see a weasel that is not real?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fairytale", "law office", "great outdoors", "cartoon", "court room"]}, "answerKey": ""}
{"id": "68794a034db481b68ef68dd36251811c", "question": "It only had one bedroom but that was fine for the bachelor, he paid for the month at the what?", "question_concept": "bedroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment", "chairs", "loft", "dwelling", "at hotel"]}, "answerKey": ""}
{"id": "bf65a026a37c3a74c77980907791b2bb", "question": "What differentiates humans from each other?", "question_concept": "humans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["animal", "brains", "two legs", "one mouth", "two ears"]}, "answerKey": ""}
{"id": "2c6c6cb26c15a62a12af86c1e9c14aac", "question": "Where can you find a plastic mouse?", "question_concept": "mouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk drawer", "computer lab", "cupboard", "old barn", "research laboratory"]}, "answerKey": ""}
{"id": "a64f862ebb15edf91ef94165a4a1c6f0", "question": "Jake didn't know the name of his father, so he just put what into the search?", "question_concept": "name", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["unknown", "pseudonym", "anonymous", "dad", "call person"]}, "answerKey": ""}
{"id": "b72b40d6e66fcdbc623babd7235cdc7c", "question": "Mom wanted to make her son's birthday party entertaining, so what did she provide?", "question_concept": "entertaining", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["favors", "pain", "laughter", "boredom", "gratification"]}, "answerKey": ""}
{"id": "517edd06965a60513e728b985b024d85", "question": "where do you find dry seaweed ?", "question_concept": "seaweed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beach", "water", "sandbar", "ocean", "found in ocean"]}, "answerKey": ""}
{"id": "11016649709a58b835e1c942aae85f5a", "question": "A crab is scurry along the shore where it can be seen where is it?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pet shop", "beach sand", "fishmongers", "intertidal zone", "chesapeake bay"]}, "answerKey": ""}
{"id": "5e5a5ea6eaeb327fa33619a08f247200", "question": "What happens when we don't succeed at something we attempt?", "question_concept": "attempt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fail", "trying", "fail", "leave", "give up"]}, "answerKey": ""}
{"id": "d9938d5c680c2d4a9ed0ce3036995986", "question": "Friends come and go but true friendships never what?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meet for lunch", "part ways", "disenfranchise", "leave", "comfort"]}, "answerKey": ""}
{"id": "65bc9dec9ead8c35f21eedcd7036546b", "question": "Waiting for the results require doing the same thing over and over, he didn't think it was necessary and found the task what?", "question_concept": "waiting for", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anxiety", "have fun", "tedious", "draining", "time consuming"]}, "answerKey": ""}
{"id": "dab4b31552d36394a1642512fa08c021", "question": "Where would you find a fast food restaurant along side other merchants out of doors?", "question_concept": "fast food restaurant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["center of town", "populated area", "theatre", "shopping mall", "strip mall"]}, "answerKey": ""}
{"id": "98205e970f763ed2b588413d02b37056", "question": "What does a person at a charity have?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["family and friends", "own house", "english house", "cotton candy", "meaningful work"]}, "answerKey": ""}
{"id": "13d601f5ec430a33d3e0912d3fb1d856", "question": "There was nothing to do, so the child was using television, why would he do that?", "question_concept": "using television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["visual", "boredom", "laziness", "falling asleep", "sick"]}, "answerKey": ""}
{"id": "5341e0f6463882349a68c98a9c962ee1", "question": "The woman lost her keys, where should she look?", "question_concept": "keys", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelf", "front pocket", "back pocket", "purse", "piano"]}, "answerKey": ""}
{"id": "c7462a96e9cfc9c261c2806e4e8d95bc", "question": "Where in an excavation would you need a flashlight to see?", "question_concept": "excavation", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["street", "construction site", "under ground", "city", "cemetary"]}, "answerKey": ""}
{"id": "a65e24918f04b6c5514db689017ecb12", "question": "What do people want to spend time talking to their elders?", "question_concept": "talking to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["boredom", "learn", "persuaded", "nursing home", "communication"]}, "answerKey": ""}
{"id": "bcbad4675403e58142f0f14249c08935", "question": "Text on a page will do what if exposed to sunlight for too long?", "question_concept": "text", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sequence of words", "fade", "field", "explaining", "book"]}, "answerKey": ""}
{"id": "1851f5d560962ba4ae8c35bae8189a0b", "question": "Where does a poet go when they're done at work?", "question_concept": "poet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garden", "home", "bedroom", "university", "classroom"]}, "answerKey": ""}
{"id": "0ef7dabdb46fb90189ff5a8188def6e1", "question": "Where would you use a floral rubber stamp?", "question_concept": "rubber stamp", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["office", "art class", "desk", "made", "indiana"]}, "answerKey": ""}
{"id": "e1c8cb2ace35327a283c050cac2257e9", "question": "How can a person makes sure they aren't overtired?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nice family", "headache", "balanced diet", "get enough sleep", "strong bones"]}, "answerKey": ""}
{"id": "54eb46b65cca90e5a2b29afd642f2e2b", "question": "They were at a museum for the band kiss, their what was a huge fan?", "question_concept": "kiss", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["show affection", "companion", "manipulate", "smile", "lips"]}, "answerKey": ""}
{"id": "c3c7df7de3d26d4410b45d3737a2c52d", "question": "james doesn't like seeing people play games.  He doesn't have any what in it?", "question_concept": "seeing people play game", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sadness", "entertainment", "interest", "entertaining", "stress"]}, "answerKey": ""}
{"id": "8955775b2a550c80fefe08c009183ca2", "question": "What is required if you want to make a purchase when shopping?", "question_concept": "shopping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cash", "money and time", "having money", "travel", "spending money"]}, "answerKey": ""}
{"id": "d7c73d54e02c2fa9d75afa67484f12bd", "question": "Where do homeless people take shelter?", "question_concept": "homeless people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus depot", "bridge", "street", "require donations from passersby", "hotel"]}, "answerKey": ""}
{"id": "70d45d88e5155327914c4752046148e7", "question": "What can't ants do that other bugs can?", "question_concept": "ants", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sleep", "circle food", "crawl", "fly", "follow one another"]}, "answerKey": ""}
{"id": "52a0005c91e59133e979f7b93ddfc0da", "question": "John was an urban citizen, he didn't know how to deal with the what?", "question_concept": "citizen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["countryfolk", "subject", "vote", "alien", "fish"]}, "answerKey": ""}
{"id": "4d13ee7deb7702b64a356a2a4a47f122", "question": "Bobby bought a lot of new clothing. When he got home he put the clothes on hangers and stored them where?", "question_concept": "clothing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["closet", "mall", "house", "suitcase", "drawer"]}, "answerKey": ""}
{"id": "bcedebfc8d41b24067f05d726352d376", "question": "John  wanted some grapes and went looking for the. He found some California grapes in several what?", "question_concept": "grape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sunshine", "cart", "deserts", "bowl of fruit", "shops"]}, "answerKey": ""}
{"id": "8cd9c2f52af984783bb78f77548c0b1d", "question": "Promotional literature is often handed out at what work gathering?", "question_concept": "literature", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["public library", "meeting", "conference", "shelf", "smoking break"]}, "answerKey": ""}
{"id": "0f0a8f5d3eeeee6570a9f3c061b64f79", "question": "The parents were listening when their kids complained, what was their goal?", "question_concept": "parents", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["guide children", "control children", "ignore complaints", "understand children", "care for children"]}, "answerKey": ""}
{"id": "65daaa5d6ca769cb3a4e97a682f39991", "question": "What type of show are the people going to see?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apartment", "conference", "opera", "supermarket", "musical"]}, "answerKey": ""}
{"id": "deb782fe7943418e06387d8ca551350a", "question": "A bird flew but couldn't find any building to land on, where was it?", "question_concept": "bird", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["roof", "cage", "sky", "nest", "countryside"]}, "answerKey": ""}
{"id": "9cc1f39777572dcc97c9f798b41b63e1", "question": "Why would a gardener want rain?", "question_concept": "rain", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water garden", "kill weeds", "wet ground", "wet clothes", "make shoes wet"]}, "answerKey": ""}
{"id": "279fd34967b1daae045cdd89686ed3ac", "question": "Where can you see a showroom that does not exist?", "question_concept": "showroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "theater", "ballet", "electronics store", "appliance store"]}, "answerKey": ""}
{"id": "ea50d11ebbbbdf785dd5284231324ace", "question": "Sports don't have to be for a prize they can just be what?", "question_concept": "sports", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fun", "games", "recreational", "competitive", "violent"]}, "answerKey": ""}
{"id": "b10c073f314ccbab72306b6f304fb9a4", "question": "The lizard had stowed away aboard the RV as the family traveled to the beach for vacation, the extremely popular beach was located where?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bermuda", "south carolina", "new mexico", "utah", "florida"]}, "answerKey": ""}
{"id": "0ae08150b20f9b7d8c81480fd9b09fe2", "question": "What organization is likely to work with explosives?", "question_concept": "explosive", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["war", "army", "landmine", "fireworks display", "military"]}, "answerKey": ""}
{"id": "a3af4e3e46d67230e3b5acf801b1ae2b", "question": "James saw a person in the street.  He tried to give them some change, but they didn't want it.  Instead they told him that they didn't need anything because they had protection.  What might they have meant?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lend money", "feel sorry", "pray", "cross street", "trust god"]}, "answerKey": ""}
{"id": "a526e2ae2cd79061d55e83336d802087", "question": "If you want to meet people who share one of your hobbies you can do what which also lets you learn more about your hobby?", "question_concept": "meet people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go to the movies", "go outside", "friendly", "go to parties", "take class"]}, "answerKey": ""}
{"id": "9f73b5a6a23412bcfd7e72c411814d30", "question": "What is a healthy reason for walking?", "question_concept": "walking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["exercise", "lose weight", "move", "getting somewhere", "enjoyment"]}, "answerKey": ""}
{"id": "88222b0214335167580536de94163f8e", "question": "The soldier was given honour but he struggled being the only one who survived, he felt like he had shown fear and what?", "question_concept": "honour", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["worry", "dishonor", "disgrace", "shame", "cowardice"]}, "answerKey": ""}
{"id": "7bff3e77b8c6ef7cc4d23ec7fee91d70", "question": "Where do you use a printer for the morning paper?", "question_concept": "printer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk", "contra costa times newspaper company", "school", "newspaper office", "home office"]}, "answerKey": ""}
{"id": "b3fdaa01a794dc528173b6144cbbed8a", "question": "He told everybody to shut up and listen, with everybody quiet they began to what in the distance?", "question_concept": "listen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["you'll learn", "concentrate on sounds", "dance", "stop speaking", "hear things"]}, "answerKey": ""}
{"id": "9c816bd4f0517ea0f16dffe7d86123ac", "question": "John loaded the projectile ball into his weapon. What type of weapon might it be?", "question_concept": "projectile ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["arcade", "slingshot", "gun", "motion", "flintlock"]}, "answerKey": ""}
{"id": "88f4dd070b5c210727104424f3e568b9", "question": "Where is someone likely to be unhappy to find a mouse nibbling on snacks?", "question_concept": "mouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refridgerator", "kitchen", "cupboard", "sewer", "garage"]}, "answerKey": ""}
{"id": "45ea75fe3ea82b829e6e306134bcdb81", "question": "When going to a party, what did the agoraphobic person fear?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["many people", "animal", "own house", "headache", "put together"]}, "answerKey": ""}
{"id": "e712b032a3d40d425cd35e6e3a876d2d", "question": "What might happen with too much learning?", "question_concept": "learning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["try new", "headaches", "fall asleep", "intelligence", "pride"]}, "answerKey": ""}
{"id": "58a0402c423758da760ed2deb5669be0", "question": "What can happen if you are dreaming when you should be watching your surroundings?", "question_concept": "dreaming", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wake up", "none", "fantasy", "inspiration", "car accident"]}, "answerKey": ""}
{"id": "a8c3331e20d1c4d347a597a0a0f16f25", "question": "John had a great deal of curiosity about all things.  So when all of his friends were getting jobs, he decided to continue doing what?", "question_concept": "curiosity", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["travel", "find truth", "go to market", "attend school", "examine thing"]}, "answerKey": ""}
{"id": "6134ef4ec50da0d5c436c866c02c6a6d", "question": "What happens when spending money without paying someone back?", "question_concept": "spending money", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bankruptcy", "clutter", "debt", "birthday party", "poverty"]}, "answerKey": ""}
{"id": "6c2cb78f9689a40b4a7ad660fbf06bf5", "question": "A car was going from a full stop to pulling on to an on ramp, what must it have done?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["head north", "slow down", "speed up", "quicken", "heading north"]}, "answerKey": ""}
{"id": "6abae2ffdbe0732b481918e896fe10c1", "question": "James was sitting quietly in a hard metal chair with sharp edges.  He wanted to express something, but remained silent due to fear of annoying others.  What didn't he express?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling asleep", "calm", "discomfort", "solitude", "relaxation"]}, "answerKey": ""}
{"id": "c0c1a0111bf39056c48d6b5877247c77", "question": "Which city's baseball stadium does the team who won the world series in 2016 play?", "question_concept": "baseball stadium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["san francisco", "chicago", "las vegas", "urban areas", "phoenix"]}, "answerKey": ""}
{"id": "f73032836e6a82c3364131c057fe3411", "question": "What could a condominium be?", "question_concept": "condominium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["florida", "towels", "pirates", "michigan", "complex"]}, "answerKey": ""}
{"id": "76d448608a97ed0bf0779d4b4861c1a1", "question": "What is the opposite of someone who is mighty and powerful?", "question_concept": "mighty", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["insignificant", "powerless", "weakling", "helpless", "unimportant"]}, "answerKey": ""}
{"id": "c0885e5dfc3133811852d7a51476b004", "question": "A person that is out of shape might experience what physical effect while running after ball?", "question_concept": "running after ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["laughter", "sweating", "pregnancy", "breathing heavily", "tiredness"]}, "answerKey": ""}
{"id": "5a1417149279533eda065aa76c081a06", "question": "Where do families usually enjoy food?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fridge", "kitchen", "cooking show", "supermarket", "table"]}, "answerKey": ""}
{"id": "b9e58e5799de17a178f3bd48aa94c730", "question": "The police moved in to rescue the little girl, it was a good ending to the what?", "question_concept": "rescue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["corrupt", "hide and seek", "endanger", "kidnap", "arrest"]}, "answerKey": ""}
{"id": "522259b020466e10b1032a5ae74e6162", "question": "The park was packed, he came to contemplate but it was clear he was going to what?", "question_concept": "contemplate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["become distracted", "get ideas", "thinking", "hatred", "daydream"]}, "answerKey": ""}
{"id": "60cc899ed3644ec0a792141f530a3ac2", "question": "The house didn't have a driveway, so where did its inhabitants have to park?", "question_concept": "house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["residential area", "across the street", "street", "subdivision", "city"]}, "answerKey": ""}
{"id": "aa080b8da2748f0caeb124f55e03941a", "question": "The story started out good, but it was terrible.  How might you describe the story?", "question_concept": "story", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["floor", "communicating moral", "near end", "end badly", "exciting to the end"]}, "answerKey": ""}
{"id": "7e6d1ea97171b01c39ad8d382d05b502", "question": "Mary is changing into a period costume in her dressing room.  What sort of building is she most likely in?", "question_concept": "dressing room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["theatre", "church", "swimming pool", "gym", "department store"]}, "answerKey": ""}
{"id": "66109f126c1a3975eb96110ff7d933ab", "question": "Grim sure, but living things will what?", "question_concept": "living", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reproducing", "food consumed", "eventually die", "grow leaves", "respiration"]}, "answerKey": ""}
{"id": "6fb014460f0f5ca8161ca8fabbb799c4", "question": "Where can you find little packets of mustard?", "question_concept": "mustard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fast food restaurant", "refrigerator", "fridge", "jar", "grocery shops"]}, "answerKey": ""}
{"id": "932de3920bbfd30c898549490dc39ad4", "question": "There was too much dirt being tracked in from the unpaved road.  The what was covered with it?", "question_concept": "dirt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["subway", "garden", "street", "fingernails", "bed"]}, "answerKey": ""}
{"id": "38803891b0daa4025b532e33fa99f5fd_1", "question": "The night owl begin coo his song as night fell, the songbirds of the day, such as the what, took a rest?", "question_concept": "night owl", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["early bird", "morning person", "early riser", "lark", "night owl"]}, "answerKey": ""}
{"id": "ab3c9e63a6533e9b769d613945d0eaaa", "question": "Danny couldn't find the novel he wanted in his local bookshop or any other stores near him, so he decided to drive where to look for it?", "question_concept": "bookshop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["big city", "mall", "department store", "amazon", "student union"]}, "answerKey": ""}
{"id": "444763de101ec847c2a7a20ce3b8607a", "question": "The CEO is kind of like the president of a what?", "question_concept": "president", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["whitehouse", "pta", "store", "corporation", "government"]}, "answerKey": ""}
{"id": "8e1f85ef0d83b461de75db88587f8d61", "question": "Sarah was very slow at making judgments.  She didn't like being what?", "question_concept": "slow", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["slowplay", "hasty", "prompt", "rapid", "okay"]}, "answerKey": ""}
{"id": "783b8fd37dfc3e8f4d10cfb055f8f5f2", "question": "The snake generally left people alone, but what kinds of hikers did it bite?", "question_concept": "snake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ditch", "rude", "sun itself", "pet", "obesity"]}, "answerKey": ""}
{"id": "dad94fba9d1f2aac768d69cf5df60e39", "question": "Where does the weasel go pop?", "question_concept": "weasel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["the yard", "children's song", "washington d.c", "congress", "chicken coop"]}, "answerKey": ""}
{"id": "1881bbb2ed61353bf039152ce4c5c284", "question": "A person specializing in computer science and using a computer will likely be doing what?", "question_concept": "using computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anger", "happiness", "program created", "stress", "carpal tunnel syndrome"]}, "answerKey": ""}
{"id": "5db775543ce96716ac31d591aa64812a", "question": "What do you want to show by thanking someone?", "question_concept": "thanking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["joy", "shake hands", "smile", "show your face", "appreciation"]}, "answerKey": ""}
{"id": "05367f6c6b1714bc9d0cb41b9ca128dc", "question": "Remembering your parents death can lead to what?", "question_concept": "remembering", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sleep", "depression", "knowledge", "pleasure", "knowing"]}, "answerKey": ""}
{"id": "ea9864dc2740c60cbdc2214021fce5e8", "question": "What does having sex in a marriage lead to?", "question_concept": "having sex", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting pregnant", "unwanted pregnancy", "aids", "making babies", "boredom"]}, "answerKey": ""}
{"id": "4c382cd0591c57700ae2784bd92878dc", "question": "What country on the coast of Europe is the setting of Hamlet?", "question_concept": "coast", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["united states", "florida", "denmark", "map", "california"]}, "answerKey": ""}
{"id": "3f137996718762973ce2d50ad77d6fb0", "question": "Where can travellers go to catch a flight?", "question_concept": "travellers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rest area", "airport", "bus depot", "train station", "subway"]}, "answerKey": ""}
{"id": "f0d8e473755a2fb68da4edd0feac06a1", "question": "The merchant had to open up shop, what kind of license did he apply for?", "question_concept": "merchant", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["business", "driving", "store", "mall", "shopping center"]}, "answerKey": ""}
{"id": "16acdb35889b9051b7c2ead8d2c279ba", "question": "Where does toilet water go in a house?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sewer", "drenching", "ocean", "septic tank", "planet earth"]}, "answerKey": ""}
{"id": "846caa7353e40434bb2de5a0e1837bd8", "question": "What was my neighbor growing a plant behind his house for before he got arrested for it?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn from each other", "use weapons", "poppy flower", "smoke marijuana", "desire to travel"]}, "answerKey": ""}
{"id": "3c26098ec2326c220b979859b1c6add5", "question": "Where does you nearest neighbor live?", "question_concept": "neighbor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ditch", "being friends with", "suburbs", "house next door", "china"]}, "answerKey": ""}
{"id": "d7fb6c1ce12e566e2c77f0465a0756bd", "question": "The couple was having fun with the activity, when they won they celebrated with a what?", "question_concept": "having fun", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["loony tune", "visiting friends", "being understood", "playing around", "hug"]}, "answerKey": ""}
{"id": "ea392b261239d4458de93591d19bbf0f", "question": "Where would you find a ficus in a hotel?", "question_concept": "ficus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "lobby", "good health", "arboretum", "own home"]}, "answerKey": ""}
{"id": "3a9bc8e3f50ef67cbe0e47973a224dc6", "question": "In times of peace, in what building is it most likely that one encounters death?", "question_concept": "death", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["war", "hospital", "funeral", "battlefield", "morgue"]}, "answerKey": ""}
{"id": "17b59d353476da2d245a03b1f4a1a201", "question": "James thought that killing people was dangerous.  He didn't want to waste his life by doing what?", "question_concept": "killing people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["going to jail", "get arrested", "going to prison", "murder", "die"]}, "answerKey": ""}
{"id": "14fb57e6a178bab1e09e2a6ccc69ad27", "question": "Even if he tried to print it was still chicken scratch to others, his what was just illegible?", "question_concept": "print", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cursive writing", "online media", "shifting work", "handwriting", "handwritten"]}, "answerKey": ""}
{"id": "dc894375cc67fcf6e118db41c3800812", "question": "He had been a janitor for years, so long that the toilet no longer even what?", "question_concept": "toilet", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refill", "smell bad", "flush", "flushed", "got dirty"]}, "answerKey": ""}
{"id": "94bc39d5a2975424ebfdb84d05a803ec", "question": "Who would have a wound on their body?", "question_concept": "wound", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["injured person", "emergency room", "hospital", "patient", "soldier"]}, "answerKey": ""}
{"id": "cafaad910e33d823e518671b10d0bda4", "question": "Where are you likely to find only a bar stool for seating?", "question_concept": "bar stool", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "cafeteria", "tavern", "restaurant", "drunker"]}, "answerKey": ""}
{"id": "75d45b9ae60a5f2688e45e17155717c0", "question": "It was hours before his performance, he set up his music stand on stage and stared out into the empty what?", "question_concept": "music stand", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["orchestra", "practice room", "music room", "concert hall", "music store"]}, "answerKey": ""}
{"id": "9548bd24b87d582eb62519eb752368fe", "question": "After perusal, a magazine will need such a resting place.", "question_concept": "magazines", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "library", "market", "old book shop", "doctor"]}, "answerKey": ""}
{"id": "b2dde0637b822f66c2ab2643361c525f", "question": "What do students who are crossing guards help other students do?", "question_concept": "student", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["read book", "think for himself", "cross road", "wait in line", "manage their emotions"]}, "answerKey": ""}
{"id": "f553595cece95ba38c7aaeb9c0ad9628", "question": "The bookworm would read book after book, she found the pastime what?", "question_concept": "read book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bored", "get comfortable", "open up", "learn new", "enjoyable"]}, "answerKey": ""}
{"id": "15b56862896c020f0d0084990dbb3ae8", "question": "Many singers might have to do this at some point?", "question_concept": "singers", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sound beautiful", "clear throats", "warm up", "use microphones", "create music"]}, "answerKey": ""}
{"id": "7378836a7a0d450cf2f3c1c4a74fc103", "question": "Where do people often go to leave?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["town", "train station", "conference", "on vacation", "apartment"]}, "answerKey": ""}
{"id": "a2a494e28e0a2d86677d07ef57fcbf20", "question": "Bob awakens in his bed.  His head is pounding from the alcohol he had last night but he has work to do and is determined to get it done.  What's the first thing he does?", "question_concept": "awaking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shock", "depression", "tiredness", "get up", "headache"]}, "answerKey": ""}
{"id": "fe9fb570190d1ca503092b0628880538", "question": "A contralto is a singer in what type of group?", "question_concept": "contralto", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert", "describe singing voice", "choir", "fun", "chorus"]}, "answerKey": ""}
{"id": "cc175eb8899f36839c4ac9ebf73073f0", "question": "What would I call a bunch of people I see in an auditorium?", "question_concept": "auditorium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concert", "crowd", "high school", "theater", "lights"]}, "answerKey": ""}
{"id": "4c54e3be4a1082aede3b92bf9ae30927", "question": "Where do you find an undiscovered river?", "question_concept": "river", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["country", "continent", "wilderness", "waterfall", "in a sink"]}, "answerKey": ""}
{"id": "c1e5dc0c00c863a7fca8296b92c35df7", "question": "Trees form what in the spring which eventually sprout leaves?", "question_concept": "tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bud", "provide shelter", "university", "produce fruit", "state park"]}, "answerKey": ""}
{"id": "626ec50bab94824e2450f88bfa081798", "question": "She was becoming inebriated, so what happened after stumbling on the cobblestone?", "question_concept": "becoming inebriated", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pass out", "unruly", "death and destruction", "fall down", "drunkenness"]}, "answerKey": ""}
{"id": "0486de1e9336bda4283baef0676347e3", "question": "What could cause your hiking trip to continue indefinitely?", "question_concept": "hiking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drink water", "get lost", "get tired", "enjoy nature", "new backpack"]}, "answerKey": ""}
{"id": "8c1f41b1d827900c819faf90eed4a962", "question": "He began to eat dinner, suddenly he stood up in a startle because he was starting to what?", "question_concept": "eat dinner", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["choke", "feel full", "watch tv", "chewing", "forget"]}, "answerKey": ""}
{"id": "bb21c30f99152d1ada4bef420cd1718c", "question": "What would a friend do if they wanted to a new car but couldn't afford it?", "question_concept": "friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hang out", "meet for lunch", "borrow money", "ask for money", "keep secrets"]}, "answerKey": ""}
{"id": "cf79b99398fe71d14cb03645098c2fa8", "question": "The referee was confused. There was no ice on the field, this wasn't what he expected.  But the stands were full of rowdy fans, he just had to read the rule book really quickly and figure out what those two big forks were for.   What game might he be officiating?", "question_concept": "referee", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gymnastics", "soccer game", "sporting event", "hockey game", "football"]}, "answerKey": ""}
{"id": "ad89eacbdad2db9876a7f7547f564295", "question": "What does committing suicide cause?", "question_concept": "committing suicide", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anguish", "death", "misery", "being dead", "sorrow"]}, "answerKey": ""}
{"id": "3c55495b467d885d679efbd585006a06", "question": "Where would you find out what a marmoset is?", "question_concept": "marmoset", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["underground", "dictionary", "latin america", "jungle", "wilderness"]}, "answerKey": ""}
{"id": "a0d623b4f633388137ec1d8e9bad824b", "question": "Before going into a trance, a hypnotist will ask you to make sure you have what?", "question_concept": "going into trance", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["closed eyes", "loss of control", "headache", "memory loss", "confusion"]}, "answerKey": ""}
{"id": "3873fc1072467cfc3b531a673af7cc01", "question": "Where are there usually a variety of peanut butter?", "question_concept": "peanut butter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "cupboard", "jar", "peanut farm", "container"]}, "answerKey": ""}
{"id": "b5b9997bfac08d775789e4fd4fedc325", "question": "Where might one fight a map of the world?", "question_concept": "map", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["amusement park", "truck stop", "classroom", "playground", "backpack"]}, "answerKey": ""}
{"id": "f66a61967465b701120a6db518d7dd4a", "question": "Sill thought that the channel would be fun to swim acroll, but she really just wanted to change the channel because she didn't like what was on.  What was Sill doing?", "question_concept": "channel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["england", "television", "river", "swimming", "waterway"]}, "answerKey": ""}
{"id": "eeb5d6cddca714f47655ba58b61f3720", "question": "Dogs get in heat when they feel it is time to what?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pant", "jump", "fleas", "bite", "mate"]}, "answerKey": ""}
{"id": "38d4136cd3ddf3d43534c0db6ff2f425", "question": "What does a company do when they need to add new business?", "question_concept": "company", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["branch out", "commit crime", "liquidated", "own resources", "ship goods"]}, "answerKey": ""}
{"id": "892dcba0b1ab8b0ac4223952e2e4aae7", "question": "After scoring the person what?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fell down", "celebrated", "fulfilled", "simplicity", "headache"]}, "answerKey": ""}
{"id": "d6cabec44619e891ff40e91c0d519777", "question": "How can a passenger get in the air?", "question_concept": "passenger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bus depot", "jumping", "bus stop", "arrive at destination", "airport"]}, "answerKey": ""}
{"id": "7892c4226d407fde1c81072dee548560", "question": "Where would you go if you need some groceries?", "question_concept": "groceries", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "bathroom", "cabinet", "trunk", "shelf"]}, "answerKey": ""}
{"id": "6c099e9401ecda2aaebb8e7224ed6d82", "question": "Some schools give an incentive to kids, if they read their books they can what?", "question_concept": "books", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["include information", "win prizes", "explain problems", "learn", "further knowledge"]}, "answerKey": ""}
{"id": "1b9076685d81e0e4034514a3b93b38a5", "question": "If there is a source of light, a person will do what opposite of it?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talk to himself", "be blinded", "cross street", "cast shadow", "continue learning"]}, "answerKey": ""}
{"id": "d747552c4295f369c56b16f2a8047a49", "question": "What do you clean dust off of to see more clearly?", "question_concept": "dust", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["attic", "television", "radio", "most buildings", "closet"]}, "answerKey": ""}
{"id": "67cf5df15145ba7612c75b9a6be321b3", "question": "How can a solicitor help in gaining an asset?", "question_concept": "solicitor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["free work", "claim compensation", "buy house", "write letter", "court proceedings"]}, "answerKey": ""}
{"id": "c0c1bf18eaba41cafe2951559f5df14d", "question": "As he contemplated the stars, James felt that he wanted to visit them, to fly into what?", "question_concept": "stars", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["night sky", "sky at night", "outer space", "airport", "universe"]}, "answerKey": ""}
{"id": "f1f8dc48a611b2f0a8bbb39304c7711d", "question": "Tim thought that helping people was nice.  He believed that it would make you what?", "question_concept": "helping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "better world", "complications", "feel good about yourself", "happy"]}, "answerKey": ""}
{"id": "feef006f2de256e69d3f24100fcac7c1", "question": "What room is a spider likely to be found in?", "question_concept": "spider", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["basement", "cellar", "mail box", "chatroom", "web"]}, "answerKey": ""}
{"id": "123ad6a93b3d2dc31418bb232386bb0f", "question": "Where do you find a librarian type at the cash register?", "question_concept": "cash register", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "bookstore", "craft store", "toy store", "shop"]}, "answerKey": ""}
{"id": "a086e49b7864619b40e2b96e4463ca03_1", "question": "The horse wanted to graze, where did it need to go?", "question_concept": "horse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["field", "washington", "painting", "minnesota", "farmyard"]}, "answerKey": ""}
{"id": "68dd334be2b3fbee17de657692a275c6", "question": "She was always living paycheck to paycheck, when she would get money she would what?", "question_concept": "money", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["buy christmas presents", "eat out", "pass course", "create art", "spend"]}, "answerKey": ""}
{"id": "a191779158f866faf7667e5ba1753756", "question": "When I cook, what is the first step to take to get my item out of its cylinder container?", "question_concept": "cook", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["add egg", "use scissors", "brown meat", "open can", "bake bread"]}, "answerKey": ""}
{"id": "2dc9c60cdef4f2692b236fd4596e37e7", "question": "When you watch a film and want to figure out a crime, you need to?", "question_concept": "watch film", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["use reasoning", "have fun", "pass time", "interesting", "see what happens"]}, "answerKey": ""}
{"id": "76f9327bab7464379db655233645dc71", "question": "Where should a finger not go?", "question_concept": "finger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hand", "hot stove", "glove", "nose", "ring"]}, "answerKey": ""}
{"id": "e1fcc1199dfe403add475549c08e06dc", "question": "The showroom featured slot machines, where was it located?", "question_concept": "showroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["electronics store", "appliance store", "car dealership", "vegas", "bathroom"]}, "answerKey": ""}
{"id": "5181ea8ed136015425f81d4abe57600e", "question": "The ball escaped from their yard.  What did the ball do?", "question_concept": "ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["toy", "earball", "bounces", "medium", "play with"]}, "answerKey": ""}
{"id": "0b75a0ccf3b43a1e5ea837b4fddd6680", "question": "How is cheese likely to be served at a restaurant?", "question_concept": "cheese", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["plate", "mouse trap", "chair", "fridge", "refrigerator"]}, "answerKey": ""}
{"id": "1e61dd7081801d655511345ca49bb8a5", "question": "It was a fun place to meet people, everybody was very what?", "question_concept": "meet people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go to parties", "go outside", "telling jokes", "take class", "friendly"]}, "answerKey": ""}
{"id": "08dc46bc31cf7bc6130ad428b8cb4461", "question": "What could someone be doing if he or she is sitting quietly with his or her eyes open and nothing is in front of him or her?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reading", "ponder", "fall asleep", "think", "enlightenment"]}, "answerKey": ""}
{"id": "358774bb18adcfeb49a8d5b2db08731b", "question": "What is often the end result of relaxing?", "question_concept": "relaxing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fall asleep", "sleeping", "invigorating", "feeling better", "stress"]}, "answerKey": ""}
{"id": "3a4e289bf712b7beea4a2b0ae30389ee", "question": "What is the lowest point of pain that running would cause?", "question_concept": "running", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["leg cramps", "knees", "becoming tired", "tiredness", "sore feet"]}, "answerKey": ""}
{"id": "5b37688a89cb0b33cab38956f8279db6", "question": "What do you usually call a person by?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["immune system", "butt", "one head", "fingernails", "name"]}, "answerKey": ""}
{"id": "1f064676170059b500a8f5d1d880c14f", "question": "If a terrorist wanted to blow something up, where would they keep their bomb?", "question_concept": "bomb", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["afghanistan", "arsenal", "suitcase", "aircraft", "arms depot"]}, "answerKey": ""}
{"id": "cc07972c47c3b286816d04098c6e91b7", "question": "John downed a glass table.  When he was setting his cup on the table he noticed something. What mgith he have noticed?", "question_concept": "setting cup on table", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["doesn", "tinkling sound", "kitty litter", "knocking over", "clutter"]}, "answerKey": ""}
{"id": "5a74721b0b6c9d6fd414d54e5b3e5efe", "question": "Where do many people choose not own a car?", "question_concept": "car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["race track", "city", "parking lot", "repair shop", "factory"]}, "answerKey": ""}
{"id": "c738af7a26aa04c9246a92d761897902", "question": "What do you call the person in charge of a project that you are working on?", "question_concept": "in charge of project", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["have knowledge", "president", "take charge", "success", "boss"]}, "answerKey": ""}
{"id": "ed62186380393bedf56b41f23601fd9a", "question": "Where is shelf stable food stored?", "question_concept": "food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pantry", "plate", "in a barn", "oven", "stomach"]}, "answerKey": ""}
{"id": "b88ce7680d1798e0798d639f81d6dc7f", "question": "The janitors job today was a break for the normal, he got to set up many a music stand for the entire what performing that night?", "question_concept": "music stand", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rehearsal", "music store", "orchestra", "concert hall", "practice room"]}, "answerKey": ""}
{"id": "b4bf290db8e532936d7f92479a159d80", "question": "What position are people in typically when they watch tv?", "question_concept": "watch tv", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sitting", "fall asleep", "eat", "have tv", "learn"]}, "answerKey": ""}
{"id": "26c20db2db6dd38f329e939c2b6313e5", "question": "What effect of tickling can be misconstrued as fun even in the person wants it to stop?", "question_concept": "tickling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["laughing", "giggling", "nausea", "crying", "getting kicked"]}, "answerKey": ""}
{"id": "57ddbce14bf86ca4c8d4cc7c925bdf47", "question": "When given two options, he chose the latter, what did he disregard?", "question_concept": "latter", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["aforesaid", "aforementioned", "earlier", "former", "prior"]}, "answerKey": ""}
{"id": "bb7368d2ab3b39d496f5f3c831b3dee2", "question": "The country has deserts, forests, and plains; You could say it has many a what?", "question_concept": "country", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["africa", "asia", "world", "dirt", "region"]}, "answerKey": ""}
{"id": "********************************", "question": "What is the final step of going to buy something?", "question_concept": "buy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["paying", "have in mind", "taking receipt", "spend money", "have enough money"]}, "answerKey": ""}
{"id": "afe7e803926401891cf902395991d029", "question": "A dictator wants to rule the country, but isn't popular, so how does he achieve this?", "question_concept": "dictator", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["take power", "demand respect", "control country", "making himself in charge", "subject people"]}, "answerKey": ""}
{"id": "39c0f19b5721a3de456909d7c7a55fcf", "question": "We don' understand how thinking works or why we do it.  It's a what?", "question_concept": "thinking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wonder", "mystery", "solution to problem", "knowledge", "solving problem"]}, "answerKey": ""}
{"id": "b33b85bf9dee53fd12ff3c8aa101f5bd", "question": "What kind of bike could you use to get in shape?", "question_concept": "get in shape", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work out", "swim", "water", "sweating", "excercise"]}, "answerKey": ""}
{"id": "6b69d1595d7a62adb79e74e7f3de96d0", "question": "Where is the best place for nice clothes?", "question_concept": "clothes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["closet", "suitcase", "car", "house", "dresser"]}, "answerKey": ""}
{"id": "8951012f08fc1576d232d51fe521e718", "question": "What is the goal of having the right amount of food?", "question_concept": "having food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["will not starve", "getting fat", "being full", "gas", "nausea"]}, "answerKey": ""}
{"id": "5b9f0746e7a074621ee386b7cf16aef3", "question": "What country is known for it's tigers?", "question_concept": "tiger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drink water", "jungle", "zoo", "india", "desert"]}, "answerKey": ""}
{"id": "fc02af638f03b8e9d5b675802c044c8a", "question": "What is necessary for someone who is remembering the answer to a question?", "question_concept": "remembering", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["knowing", "forgetting", "laugh", "problems", "nostalgia"]}, "answerKey": ""}
{"id": "660a37ab26ed40e2cce5f6a3ebe4cea6", "question": "Where do you keep apples?", "question_concept": "apples", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shop", "fridge", "grocery store", "farmers market", "orchard"]}, "answerKey": ""}
{"id": "4cc3d325bd8ed51b791254a68851ace3", "question": "James was getting in the line because he wanted to get tickets.  Getting tickets was his what?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["basic organization", "banking", "friend", "patience", "intention"]}, "answerKey": ""}
{"id": "aaa15bef3ba2446f35ee52dbbdfef7b0", "question": "He took his seat on the tour bus, it would be travelling through where?", "question_concept": "seat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["show", "theatre", "bus stop", "martorell", "in cinema"]}, "answerKey": ""}
{"id": "8427eb0831456ae7f6133659384ef116", "question": "Sometimes dreaming can be seem very real, a shock during it can even cause you to what?", "question_concept": "dreaming", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wake up", "car accident", "inspiration", "confusion", "nightmares"]}, "answerKey": ""}
{"id": "43f14d9b1977885dcd49ec37cb74758d", "question": "There still may be many undiscovered species of lizard in the jungle, especially on what continent?", "question_concept": "lizard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["new mexico", "new hampshire", "encyclopedia", "texas", "south america"]}, "answerKey": ""}
{"id": "dc5e1e5d196820aef741368274ac348f", "question": "The lawyers entered the courthouse in Lansing, what state were they in?", "question_concept": "courthouse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "center of town", "large citties", "wisconsin", "capital city"]}, "answerKey": ""}
{"id": "627498015a9b84e22ad185dae7c36a9a", "question": "Alzheimer's was affecting her short term memory, in day to day tasks she was suffering what?", "question_concept": "memory", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forgetting", "brainless", "forgetfulness", "forgotten", "memory loss"]}, "answerKey": ""}
{"id": "b2f24df7e64cb7cbee5600e36111a14a", "question": "You will find string, brass, percussion, and wind sections in what large group of performers?", "question_concept": "section", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["citrus", "band", "platoon", "orchestra", "acrobats"]}, "answerKey": ""}
{"id": "f632b32d5114ee2d9e38e9b9c7a63bae", "question": "John needed to get ready to work so he  put on his freshly washed jeans.  Where was he probably at when he put on his jeans?", "question_concept": "jeans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["laundromat", "bedroom", "shopping mall", "library", "thrift store"]}, "answerKey": ""}
{"id": "751136a3d00ac3606b3245bb5fd55900", "question": "why do people choose going to party?", "question_concept": "going to party", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hook up", "meet new people", "get drunk", "enjoy yourself", "having sex"]}, "answerKey": ""}
{"id": "6ee601df8d89d5b018601b99cb114c20", "question": "Judy lay on the examination table.  She fell off of her bed during vigorous sex and feels like she broke something.  Where might she be?", "question_concept": "examination table", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hotel room", "vets office", "doctor's office", "hospital", "school"]}, "answerKey": ""}
{"id": "77f502905f158f318ea4ab91933f7b14", "question": "They had finished baking the cookies for class, the mother told her daughter to get the plastic container out of the what?", "question_concept": "plastic", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stock yard", "everything", "own home", "garbage dump", "cupboard"]}, "answerKey": ""}
{"id": "7d0949a4f1046496220f59b6ecac1eed", "question": "Where would you put jewelry if you want to bring it with you?", "question_concept": "jewelry", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["vault", "department store", "suitcase", "handbag", "safe deposit box"]}, "answerKey": ""}
{"id": "********************************", "question": "It was getting very late and James needed to clean off the junk that covered what?", "question_concept": "junk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["television", "drawer", "attic", "bed", "counter"]}, "answerKey": ""}
{"id": "74702f28361501dfcf9d159a65266c4c", "question": "What facial expression shows your open to making friends?", "question_concept": "making friends", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talking", "open mind", "falling in love", "common interests", "smiling"]}, "answerKey": ""}
{"id": "a267964e677c708d81bacb6390772590", "question": "Where would you get some fungus if you are hungry?", "question_concept": "fungus", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["under rocks", "grocery store", "post office", "fallen tree", "toenails"]}, "answerKey": ""}
{"id": "363dbed038ecbd798f6d5c253fcc9a93_1", "question": "When you're making no progress while competing against someone, what might you feel?", "question_concept": "competing against", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["one winner", "frustration", "encouraging", "emotions", "happiness"]}, "answerKey": ""}
{"id": "e800f7dd1170be4d1ba1debcfa68739c", "question": "He brought an umbrella for a beach visit after the business meeting, where did he keep it?", "question_concept": "umbrella", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["beach", "destination", "charlie chaplin film", "seattle", "suitcase"]}, "answerKey": ""}
{"id": "701fd06e297df41ca0bb6405b1131304", "question": "He had an interest in a work by a new director, what did he want to do?", "question_concept": "interest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["see exhibits", "watch film", "see particular program", "study film", "have conversation"]}, "answerKey": ""}
{"id": "8f0ddcc4491b25e77382b2ff7c75582d", "question": "The athletes were sore and sweaty after working out at the gymnasium, where did they want to go afterward?", "question_concept": "gymnasium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spa", "high school", "school or ymca", "college campus", "lunch"]}, "answerKey": ""}
{"id": "e945f49906c9b14ba40e94bda6cf15a0", "question": "You can use a cup to build a castle tower when playing in a what?", "question_concept": "cup", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["imagination", "sand box", "water fountain", "dishwasher", "kitchen cabinet"]}, "answerKey": ""}
{"id": "12b84809cf67528e9d27d54c9aabf4f4", "question": "What will happen after running for a long time without drinking any water?", "question_concept": "running", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dehydration", "bone damage", "fall down", "breathing hard", "sweat"]}, "answerKey": ""}
{"id": "a83ade78504b53a5e082e6b7824c1c94", "question": "What can learning about world lead to ?", "question_concept": "learning about world", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["foolishness", "loss of innocence", "cynicism", "open mind", "smartness"]}, "answerKey": ""}
{"id": "9442cc92318a1ef3864fc72662323eeb", "question": "If a person wants to publicly express themselves, what can they write?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["watch television", "listen to radio", "open letter", "thank god", "say goodbye"]}, "answerKey": ""}
{"id": "627baeadbcf84a380ea4148efd43e38d", "question": "If you do well at an interview after applying for a job what do you want to receive?", "question_concept": "applying for job", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["offer", "rejection", "income", "hope", "sandwich"]}, "answerKey": ""}
{"id": "53528254fc37a69bf6dbe8483d1ce248", "question": "Where do people take their frying pans after purchase?", "question_concept": "frying pan", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["department store", "washing clothes", "kitchen cabinet", "homes", "hotel kitchen"]}, "answerKey": ""}
{"id": "43018c0d1bdbb473f981ceefeaa08fdd", "question": "Where might people gather to share information?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["conference", "train station", "town", "internet", "apartment"]}, "answerKey": ""}
{"id": "be98599b9e21d1fcabae14d0721fd1cb", "question": "If a person it talking to you what should you be doing?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["communication", "waving hands", "revelation", "listening to", "distraction"]}, "answerKey": ""}
{"id": "34ba43dba5de2ef7df1fd1e7bb003568", "question": "What does a guilty person do in a courtroom?", "question_concept": "courtroom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["courthouse", "confession", "maine", "court tv", "trial"]}, "answerKey": ""}
{"id": "9c70d7820ad3c5ca472764e6b83b1a73", "question": "He took meticulous notes, he wasn't naturally gifted but he worked very hard to do well at what?", "question_concept": "notes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "notebook", "meeting", "college", "desk"]}, "answerKey": ""}
{"id": "906bcca9852bc7f0bb9e271af9b24897", "question": "It was a hot day and they all wanted to get in the water.  Sam climbed up.  What might Sam have done?", "question_concept": "hot day", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat ice cream", "buy beer", "bathe", "dive", "cool off"]}, "answerKey": ""}
{"id": "f439e1878374900302fdc30269acb2a0", "question": "The so called nerd had earned respect by standing up for himself, a person can only take so much what?", "question_concept": "respect", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["belittlement", "slight", "dishonor", "being rude", "irreverence"]}, "answerKey": ""}
{"id": "0d40b9fb6cf3b4d2f4ef584ee6a3edf6", "question": "If the end of the world comes and people need to reproduce what is the main goal of the couple to do?", "question_concept": "reproduce", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["raise children", "an addition", "what people want", "offspring", "have children"]}, "answerKey": ""}
{"id": "12d50b3a82eb83154621b76e3d399a4b", "question": "What does driving a car use?", "question_concept": "driving car", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["car crash", "gas pedal", "speeding ticket", "use gas", "doze off"]}, "answerKey": ""}
{"id": "aab67cf6bf81258693498826f4907cae", "question": "Where is disease often spread rapidly?", "question_concept": "disease", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["not washing hands", "lab", "hospital", "third world country", "human body"]}, "answerKey": ""}
{"id": "7ac44c2766feb6b4f868815f91c1b59c", "question": "The two boys caused trouble together, so the teacher made them join what groups?", "question_concept": "join", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["separate", "leave", "quit", "opt out", "split apart"]}, "answerKey": ""}
{"id": "c1f19986690f34a065448f7e685226c3", "question": "Where is someone likely to be bored in a waiting room for something routine?", "question_concept": "waiting room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospitals", "maternity ward", "doctor's office", "school", "airport"]}, "answerKey": ""}
{"id": "cb2657f4073a4a2950f6303c8297960a", "question": "Sally sat too close to the orchestra pit and couldn't hear anything from the stage.  She was  really dissapointed because  the archetect didn't think about where to put the place where she was sitting. What is that place called?", "question_concept": "orchestra pit", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["theatre", "butt", "stadium", "auditorium", "opera house"]}, "answerKey": ""}
{"id": "9d0802f590601881a592c3e7fc1c8a48", "question": "What kind of furniture could you keep a thermometer in?", "question_concept": "thermometer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["box", "report on temperature", "cabinet", "drawer", "hospital"]}, "answerKey": ""}
{"id": "c949aae7dddff5f3421bd4f10326a057", "question": "What could you find a diaphragm in?", "question_concept": "diaphragm", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drugstore", "woman's body", "human", "drugstore", "person's chest"]}, "answerKey": ""}
{"id": "b399eb649f8b4a5405a20ed862d1f698", "question": "The space shuttle was stuck near the planet, what was it stuck in?", "question_concept": "space shuttle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["traffic", "solar system", "orbit", "universe", "cape canaveral"]}, "answerKey": ""}
{"id": "16db05e2eb539834abea0440edf95f42", "question": "When they leave home a student is responsible for what?", "question_concept": "student", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["class room", "college class", "library", "every aspect of life", "university"]}, "answerKey": ""}
{"id": "c22417169bc8dfd986b6ef7919288621", "question": "Where is meat often prepared?", "question_concept": "meat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kitchen", "freezer", "butcher shop", "frying pan", "fridge"]}, "answerKey": ""}
{"id": "0f3ee708d558f2de3fb6838b3eb8aebf", "question": "If you're talking a break from employment, then what are you probably doing?", "question_concept": "taking break", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["not working", "sitting down", "relaxation", "vacation", "renewal"]}, "answerKey": ""}
{"id": "938ae69a9c1a8e8d0a8522717dcc60dc", "question": "What heat source was commonly used in early steam engines?", "question_concept": "heat source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coal or wood", "solar energy", "energy", "fire", "house"]}, "answerKey": ""}
{"id": "28e3349754bba5df126075aa14492c53", "question": "What does a person do when they don't want someone to know that they know something?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hide", "read text", "play dumb", "enjoy learning", "cross street"]}, "answerKey": ""}
{"id": "ad0d5804fc7b844ccba97aac6fde3cae", "question": "When someone is talking, what would cause them to say bless you?", "question_concept": "talking", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sneeze", "sharing of ideas", "breathe", "speak", "story telling"]}, "answerKey": ""}
{"id": "389cf73d92b20d7c47bb024ae7876210", "question": "There are two leather chairs that swivel.  Where might you expect to find chairs like this?", "question_concept": "chairs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["building", "theater", "board room", "office", "meeting"]}, "answerKey": ""}
{"id": "21cc9197f963387ec5a1f6386e5f7e46", "question": "The rich man told his traders not to buy anymore, his gut told him it was time to start what?", "question_concept": "buy", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat cake", "steal", "selling", "save money", "paying"]}, "answerKey": ""}
{"id": "42af5c15612ed0123b10971dcc6579be", "question": "What do you put shades over?", "question_concept": "shades", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["forest", "home", "windows", "paint store", "house"]}, "answerKey": ""}
{"id": "df1c7eefd41f7402dc79444ba75ab82b", "question": "Fallen leaves covered the floor around the pot.  What might have been in the pot?", "question_concept": "fallen leaves", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fall season", "roof", "tree", "ground", "forest"]}, "answerKey": ""}
{"id": "ed3c021d8ee382e939de2d491aa7e196", "question": "Where would you find a hair salon along with many other merchants?", "question_concept": "hair salon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mail", "hotel", "metropolitan city", "shopping center", "post office"]}, "answerKey": ""}
{"id": "8b9d6ba87ab43b0054b6342b0b1ffe34", "question": "What is person who is ambitious focused on?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["passion", "get laid", "accomplish goals", "talk about themselves", "own house"]}, "answerKey": ""}
{"id": "440ddadefe24d2d3b589ed9706f9ba55", "question": "The control room opens and closes doors in what home for law breakers?", "question_concept": "control room", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["high school", "nuclear power plant", "recording studio", "prison", "building"]}, "answerKey": ""}
{"id": "76a0c2bb61952d0a9c726f857d866135", "question": "Jacob wants to get the freshest possible carrots at the lowest price, so he's going where?", "question_concept": "carrots", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refrigerator", "the field", "farmer's market", "store", "supermarket"]}, "answerKey": ""}
{"id": "df00680da989e19f64b8688d458fa911", "question": "What city that begins with a C is a part of Manchester?", "question_concept": "manchester", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["greater manchester", "lancashire", "cheshire", "england", "england"]}, "answerKey": ""}
{"id": "8fd247a41810a2bd43defcdb77701df2", "question": "Where are there likely to be yards side by side?", "question_concept": "yard", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neighborhood", "property", "michigan", "outside", "city"]}, "answerKey": ""}
{"id": "5c21aef49b441a7f5ee0c002152a4e96", "question": "Although many humans don't like it, what are many animals naturally in the wild?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drink", "fly", "reproduce asexually", "gay", "bite"]}, "answerKey": ""}
{"id": "840a09342c31539bc2c3577481759797", "question": "What is a common injury while wrestling?", "question_concept": "wrestling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get free", "blood", "bruises", "falling down", "competition"]}, "answerKey": ""}
{"id": "b3345fdcafe4ffb8890c2c1cceda4ece", "question": "He had never seen a common snow before, the locals were used to it but for him it was fun and what?", "question_concept": "common", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["extraordinary", "beautiful", "irregular", "unusual", "special"]}, "answerKey": ""}
{"id": "f394d14c37e5e03556e8bfc8cd554bdc", "question": "He had trouble getting sleep at night, he could never cool down and what?", "question_concept": "sleep at night", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relax", "clear conscience", "go to sleep", "die", "get into bed"]}, "answerKey": ""}
{"id": "fea6c3521d1878495289cbc3e195bad6", "question": "What can frogs do throughout their life cycle, from tadpole to adult?", "question_concept": "frogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jump several feet", "hop", "leap", "hop several feet", "swim"]}, "answerKey": ""}
{"id": "4023b99af4927af5f89a50e5b1fa3b75", "question": "Jame's actions were bad, but torturing him for it was now what?", "question_concept": "bad", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ocean", "true", "prime", "just", "excellent"]}, "answerKey": ""}
{"id": "3492541e574280c78189f20fba6a4dc2", "question": "Why would someone be getting in line?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["for fun", "have to wait for", "anxiety", "terrible", "wait turn"]}, "answerKey": ""}
{"id": "905f3ac066321bce5ee913dbbc2c41d6", "question": "The child was suffering boredom in the classroom, he began to do what with things?", "question_concept": "boredom", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "dream", "tamper", "fiddle", "play games"]}, "answerKey": ""}
{"id": "191e3154d4039c42e8b919934828db0f", "question": "Humans live in all sorts of regions and climates, they seem to be what?", "question_concept": "humans", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["live forever", "male or female", "accomadable", "very adaptable", "emotional"]}, "answerKey": ""}
{"id": "a8aed2095404a1880f0a3130d4ab5f44", "question": "What open space is a fine place to plant a rosebush?", "question_concept": "rosebush", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nature", "field", "vegetable garden", "neighbor's yard", "botanic garden"]}, "answerKey": ""}
{"id": "535e6cf97fda774c74f18b840e766709", "question": "What happens shortly after taking final exams?", "question_concept": "taking final exams", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anxiety", "success", "headaches", "graduating", "failure"]}, "answerKey": ""}
{"id": "9f090f5d97084f7fe8394386e182443d", "question": "James tried to preserve the floor as best he could, but the water seeped in causing it to do what?", "question_concept": "preserve", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ruin", "michigan", "use", "let spoil", "rot"]}, "answerKey": ""}
{"id": "a22a289f2f46a060639b5726a42d8da3", "question": "What is someone trying to have while playing ball?", "question_concept": "playing ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pleasure", "having fun", "losing", "injury", "lose"]}, "answerKey": ""}
{"id": "0bb575bc155f90b689fdec418b8c3f71", "question": "Where can you watch a show about an isle?", "question_concept": "isle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sea", "auditorium", "lake", "stadium", "ocean"]}, "answerKey": ""}
{"id": "6fc4827b5c15dffcf6a15d167fe93b8f", "question": "The spectators were watching the pitcher on the mound, where were the spectators?", "question_concept": "mound", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lakehouse", "hell", "baseball stadium", "africa", "baseball diamond"]}, "answerKey": ""}
{"id": "7a9acba71edab7d66fddce4f5bb8cb9c", "question": "The man was watching TV late at night, what happened as a result?", "question_concept": "watching television", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["looking stars through window", "entertainment", "wasted time", "relaxation", "falling asleep"]}, "answerKey": ""}
{"id": "a431fd9da3ab026b7df5aeb3b35c5ca1", "question": "When exercises for weight loss or to get in shape what might your body do?", "question_concept": "exercising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sweat", "weight loss", "get in shape", "scream", "relaxation"]}, "answerKey": ""}
{"id": "21a4a50050518573cf8d589e38b895ca", "question": "What is the best about living life with lots of smart people around you?", "question_concept": "living life", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["early death", "happiness", "acquiring knowledge", "book burnings", "existing"]}, "answerKey": ""}
{"id": "d7d58412eea308a64169aa74eeb9aba3", "question": "The man rushed in to help, he couldn't believe so many people would stand around and what?", "question_concept": "help", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["do nothing", "hurting", "observant", "hinder", "leave"]}, "answerKey": ""}
{"id": "3ac7cee0cb7d5aa9f0fbf0b36d281fb0", "question": "Where can you drink alcohol in the air?", "question_concept": "alcohol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["distillery", "fraternity house", "plane", "hot air balloon", "wine"]}, "answerKey": ""}
{"id": "e393cee2d51bf52a60e0aae945ed2b8e", "question": "Where would you pour a glass of milk by yourself?", "question_concept": "glass of milk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["accompaniment to meal", "home", "menu", "the kitchen", "cafeteria"]}, "answerKey": ""}
{"id": "653d0ef659899c4dc028a811e2b2b32c", "question": "John looked up while mowing his lawn and saw birds nesting.  Where might they be nesting?", "question_concept": "birds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["countryside", "tree", "forest", "roof", "sky"]}, "answerKey": ""}
{"id": "8da69153b21a133a269d46308d560727", "question": "A human in Japan has a habit of doing what?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["need money", "drink tea", "have to sleep", "say hello", "eat sushi"]}, "answerKey": ""}
{"id": "b73b323205a05d1abb3e90c2d3e6f027", "question": "Why would you be going for run if you are not fat?", "question_concept": "going for run", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "better health", "eat right", "losing weight", "breathlessness"]}, "answerKey": ""}
{"id": "6326fa333a82e76214d94c813befe727", "question": "If your legs are experiencing fatigue what can you do?", "question_concept": "fatigue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sit down", "run like 'run lola run'", "get physical activity", "sleep", "have rest"]}, "answerKey": ""}
{"id": "a8c961aa8a02d1743b363a1ab7252f65", "question": "If you're successful when performing a difficult task, how might you feel?", "question_concept": "performing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["butterflies", "energetic", "anxiety", "happiness", "pride"]}, "answerKey": ""}
{"id": "96711aefd640833f1fc4e4f669323786", "question": "Spending too much when buying christmas presents will cause you to go in to what?", "question_concept": "buying christmas presents", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["debt", "cardiac arrest", "stress", "happiness", "pleasure"]}, "answerKey": ""}
{"id": "ec98242bc2ee172d21fc247134e7e43c", "question": "A car thief is thwarted by a door with lock, what was he trying to take?", "question_concept": "door with lock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["locker", "bus terminal", "automobile", "garage", "file cabinet"]}, "answerKey": ""}
{"id": "089a564c57b4ce06d914614a17281ba5", "question": "Who are church's made for?", "question_concept": "church", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["city", "children", "populated area", "every town", "christian community"]}, "answerKey": ""}
{"id": "31bd28eb38bfc181a9e36c9210286ae9", "question": "Where might you find a stray tabby cat?", "question_concept": "tabby cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["home", "barn", "alley", "outside", "lap"]}, "answerKey": ""}
{"id": "db4bacb29cf87a3104626ba6de7be368", "question": "Where will you find black and white words?", "question_concept": "words", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["books", "mouth", "newspaper", "sentence", "page of book"]}, "answerKey": ""}
{"id": "b176f1e1326618ad159275c3c29ed273", "question": "Where can you a drop of blood from if nothing is around you?", "question_concept": "drop of blood", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["clinic", "vein", "battlefield", "street", "needle"]}, "answerKey": ""}
{"id": "a0f78af473fe64aeb2e4b9ee8eb235d3", "question": "He worked the belt loose, it was beginning to what against his hip?", "question_concept": "loose", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["close fitting", "bind", "engage", "fast", "tighten"]}, "answerKey": ""}
{"id": "08a88b77afcc2e64bffb52fd793a8f3e", "question": "What is known to be a very complex system?", "question_concept": "system", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["place", "computer store", "human body", "nature", "computer science"]}, "answerKey": ""}
{"id": "164cea08110f18b8f8ff9e1861f87fe6", "question": "The lady got her cats a new scratching post so they had something to what?", "question_concept": "cats", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["purr", "claw", "think", "eat meat", "drink water"]}, "answerKey": ""}
{"id": "d365ed78590354f1bda41068fa5cae27_1", "question": "The scientist was contemplating something he never saw before, what was he contemplating?", "question_concept": "contemplating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["meaning of life", "thinking", "revelations", "discovery", "reflection"]}, "answerKey": ""}
{"id": "7683d753ffd06f40801d4969d5d9d91f", "question": "She had an interest in a documentary show, what was she browsing for?", "question_concept": "interest", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["research", "watch film", "have conversation", "go to performance", "see particular program"]}, "answerKey": ""}
{"id": "48d17f25da85ed640dab53de309b7277", "question": "Where would you put a dollar if you want your money to be organized?", "question_concept": "dollar", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["table", "piggy bank", "purse", "pocket", "cash drawer"]}, "answerKey": ""}
{"id": "a173a77f7aeb99842068e4ad9238a390", "question": "What is a place inside of a building where you can find a teacher using glue?", "question_concept": "glue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["art room", "school", "desk drawer", "classroom", "library"]}, "answerKey": ""}
{"id": "c8333ef436e07e89129aad87808320f5", "question": "Where will a policeman stop you if you're going too fast?", "question_concept": "policeman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["freeway", "street", "donut shop", "sidewalk", "police station"]}, "answerKey": ""}
{"id": "a788bce17d49a733593ec4fc7f03c61c", "question": "The parking area was the site of a huge party, who was there?", "question_concept": "parking area", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["amusement park", "city", "people", "vehicles", "apartment complex"]}, "answerKey": ""}
{"id": "5fb53ef6493c635e3091e00f2ca97eca", "question": "At the end of official judging what is handed down?", "question_concept": "judging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["trophy or ribbon", "evaluating", "responsibility", "verdict", "prejudice"]}, "answerKey": ""}
{"id": "ab32b8150e7f87f6e5964889d9fdf562", "question": "Gambling debts that exceed your assets will cause you to declare what?", "question_concept": "gambling", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["casino", "losing money", "lose money", "penury", "bankruptcy"]}, "answerKey": ""}
{"id": "c54faa4d84678032beb4132da313daf5", "question": "What does a child do when walking out ?", "question_concept": "child", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ask many questions", "clean room", "wave goodbye", "play video games", "give a hug"]}, "answerKey": ""}
{"id": "7ffa0c121885c2aad8ca1f1f465ce564", "question": "If someone is angry and they're told to \"cool off\" what does that person want them to do?", "question_concept": "cool off", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["relief", "think", "calm down", "relax", "go swimming"]}, "answerKey": ""}
{"id": "ecc430c711c2f9a10d812ee8fe556e7c", "question": "Where do you typically buy accessories made out of felt?", "question_concept": "felt", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hat shop", "man's hat", "mall", "craft store", "clothes"]}, "answerKey": ""}
{"id": "46f0af4683b2d1803470c13c734c05b3", "question": "James lived in a rural area and wanted to go to a tennis court.  What state might he live in?", "question_concept": "tennis court", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "ghana", "town", "wimbledon", "park"]}, "answerKey": ""}
{"id": "56f58e4ac1635bb7d407bedd677319fe", "question": "He began using the soft setting to polish out a finish, this was after having started with a more what setting?", "question_concept": "soft", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["harsh", "abrasive", "hard", "sensible", "loud"]}, "answerKey": ""}
{"id": "ffb650d0a8d3bb8507648c36777a16bd", "question": "Where does someone use a drill on wood work?", "question_concept": "drill", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work shop", "tool store", "tool shed", "dentist office", "repair shop"]}, "answerKey": ""}
{"id": "a7003deda241e47c794142fb7a5c3b90", "question": "My company is strictly mom and pop.  How do I keep it that way?", "question_concept": "company", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["promote internally", "own resources", "commit crime", "own factory", "branch out"]}, "answerKey": ""}
{"id": "dcaf5377ed327720b278c6cd26d10974", "question": "The man wanted to serve crab, where should he put it first?", "question_concept": "crab", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cooking pot", "pot", "fish market", "tide pool", "beach sand"]}, "answerKey": ""}
{"id": "4e94dffb2f364b99d6ab323f35396992", "question": "The tiger is one of many dangers in the what?", "question_concept": "tiger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["zoo", "drink water", "jungle", "india", "canada"]}, "answerKey": ""}
{"id": "ba7cffc644dacb035c5ffbaeebdef207", "question": "While seeing people play a game what may you experience if you team is doing well?", "question_concept": "seeing people play game", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["excitement", "stress", "envy", "longing", "looking at wrist watch"]}, "answerKey": ""}
{"id": "41c92f0ef20e8f4c85616016cafa3190", "question": "What does needing to study usually require a great deal of?", "question_concept": "study", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["concentrate", "read books", "assignment", "have book", "concentration"]}, "answerKey": ""}
{"id": "aa36e26ef82a2bdfea163d2d06cb01d2", "question": "The bat needed a stereotypical place to live, what did it choose?", "question_concept": "bat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bridge", "new mexico", "place", "belfry", "dug out"]}, "answerKey": ""}
{"id": "51f9903c8635c9b9eb4c62a6119133ed", "question": "A snowflake with millions of friends and wind can cause all sorts of trouble, what is this called?", "question_concept": "snowflake", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["december", "cloud", "air", "snow storm", "winter"]}, "answerKey": ""}
{"id": "6ca12f315d3ecc822ff5bf570e63402d", "question": "Billy was terrible at socializing and didn't know many people at all.  What might make it difficult for Billy to socialize?", "question_concept": "socialising", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["making friends", "have fun", "having fun", "anxiety", "billys mom"]}, "answerKey": ""}
{"id": "c47b4cf797be3c52c4f464c98937c972", "question": "Getting something wrong that's obvious can really make a person what?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["water garden", "eat fruit", "catch cold", "jump for joy", "feel stupid"]}, "answerKey": ""}
{"id": "3dcb4aaff4e3b30e98b63b14d74ac678", "question": "Where does a policeman frequent for snacks?", "question_concept": "policeman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["freeway", "front door", "donut shop", "tire shop", "street"]}, "answerKey": ""}
{"id": "ca7cd3b440fefdea6c1eadf7ebb804af", "question": "If a guy wants accommodations to view a movie, where is he looking to go?", "question_concept": "accommodations", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["motel", "theater", "having someplace to stay", "comfort", "camp ground"]}, "answerKey": ""}
{"id": "597f8ea2efad999ba564f0341acb4dab", "question": "What will a person who plans for the future will do this more often than someone who lives in the moment?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["save money", "cry", "own house", "better job", "feel safe"]}, "answerKey": ""}
{"id": "37b9a196725f183f8a77151b732ddcb5", "question": "My friend and I were playing ball. We each wanted to win. What sort of game were we playing?", "question_concept": "playing ball", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["board game", "lose", "competition", "throwing", "having fun"]}, "answerKey": ""}
{"id": "ed135a39d9796af258c5c52245cd4a06", "question": "What does one do in bed?", "question_concept": "bed", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["snore", "awake", "sleep", "chair", "floor"]}, "answerKey": ""}
{"id": "bd99f9502fe6d39b5f65b17c1fb499b5", "question": "Where does a family eat at a table after cooking dinner?", "question_concept": "tables", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bathroom", "fast food restaurant", "house", "conference", "library"]}, "answerKey": ""}
{"id": "4025ea36e968e84def51e9c592314531", "question": "Where do you put personal air conditioning?", "question_concept": "air conditioning", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hot room", "waiting room", "car", "house", "offices"]}, "answerKey": ""}
{"id": "d8e9bc3054beedc0a24f8539edd9aae3", "question": "If you work hard, one can also be said to do what hard?", "question_concept": "work", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["play", "unemployment", "laziness", "unemployed", "fly"]}, "answerKey": ""}
{"id": "568016ad69dd9b0413a3bec91e563fc0", "question": "Where is bacteria most likely to grow?", "question_concept": "bacteria", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["petri dish", "finger", "ground", "septic tank", "water"]}, "answerKey": ""}
{"id": "c0a8030342bf84fd0ced5023b9d66029", "question": "If someone is going to skate in a restricted area what do they hope to do?", "question_concept": "skate", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling down", "prohibit", "fall down", "get away with", "maintain balance"]}, "answerKey": ""}
{"id": "631e3e91e685d80a1e1464ce5d098bf9", "question": "What would you do before pray in your house?", "question_concept": "pray", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["look up", "go to church", "speak to god", "kneel", "dip fingers in holy water"]}, "answerKey": ""}
{"id": "0aa87bbecdbad374acc1cc40d80a1641", "question": "Sally was answering John's questions honestly.  She enjoyed doing what?", "question_concept": "answering questions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["getting tired", "boredom", "sharing information", "spitting nails", "confusion"]}, "answerKey": ""}
{"id": "ab0e7bf706f1f94d7774ac06bef7fb20", "question": "What has water and mud in it?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["glass", "street", "puddle", "soup", "ocean"]}, "answerKey": ""}
{"id": "90c30007312d7ca23fb09d50529ce17e", "question": "Committing murder is difficult, but if you do it right someone will end up in what state?", "question_concept": "committing murder", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["go to jail", "die", "dead", "kill", "done"]}, "answerKey": ""}
{"id": "8803071f599c042e6d0d5a5b364b296b", "question": "The sloth's native habitat is in the area known as what?", "question_concept": "sloth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wilderness", "tropical jungle", "woods", "basement", "amazonia"]}, "answerKey": ""}
{"id": "61e6a5bef1507ef9be89c50ab46a3e0a", "question": "For what purpose does a person usually plan on having food?", "question_concept": "having food", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eating food", "weight gain", "getting fat", "being over weight", "being full"]}, "answerKey": ""}
{"id": "3c46d1d50b0900e7c9d3e6121cecaae2", "question": "where do most bankers work?", "question_concept": "banker", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pond", "country club", "monopoly game", "wall street", "michigan"]}, "answerKey": ""}
{"id": "585d0f04348fa72bcb4e91d9c7a4dfc9", "question": "He told the people he was a native, he figured after thirty years he was hardly an what?", "question_concept": "native", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["alien", "guide foreigners", "foreigner", "immigrant", "introduced"]}, "answerKey": ""}
{"id": "7337c9fb471c0ac7c5baa972ddc08c82", "question": "A roadway doesn't have many building around it, where is it likely traversing?", "question_concept": "roadway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["neighborhood", "city", "city", "subdivision", "countryside"]}, "answerKey": ""}
{"id": "60c35f7f62c4c0747ee5dfda27be72e8", "question": "If I spent my weekend resting, what might I tell a coworker I spent time doing?", "question_concept": "resting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["running", "feeling better", "doing nothing", "recuperation", "relaxation"]}, "answerKey": ""}
{"id": "6ce35cf429b2953db151ff919d4df01c", "question": "What does not have a lip?", "question_concept": "lip", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["mouth", "kentucky fried chicken", "human", "mouth", "jars and all faces"]}, "answerKey": ""}
{"id": "90154aad85582a0221299791f7eaaa59", "question": "If you're meeting a friend and you've already had lunch what can you do together?", "question_concept": "meeting friend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["eat dinner", "say hi", "greet", "shake hands", "have coffee"]}, "answerKey": ""}
{"id": "c22d15ed3823aecff8092299bb5e50e1", "question": "James was interested in examing the thing that they pulled out of the water.  He liked stuff from old shipwrecks. He probably thought that they were what?", "question_concept": "examine thing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["wet", "interests", "interesting", "complex", "learn more about"]}, "answerKey": ""}
{"id": "203fec2260e4fa484977524a8f7c8d92", "question": "Whenever we travel we burn fuel that creates what?", "question_concept": "travel", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["energy", "pollution", "go somewhere", "have money", "decide where to"]}, "answerKey": ""}
{"id": "d8430ac495bcf82323d70d5fc31b7b3a", "question": "If a person paddles in the water, where are they likely to be?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["rain cloud", "bath tub", "ocean", "wishing well", "lake or river"]}, "answerKey": ""}
{"id": "c4d2a6826ccfcbbbeaa538a656eb8980", "question": "A person injured themselves, where should they go?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["broken heart", "hospitalized", "annoyance", "ridiculous", "railway station"]}, "answerKey": ""}
{"id": "65d3f9cdcbe7ee81473715348513316c", "question": "The football coach conditioned the teenagers at the gymnasium, where was this located?", "question_concept": "gymnasium", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["high school", "spa", "pool", "college campus", "being healthy"]}, "answerKey": ""}
{"id": "f66db7b764e55002b375d46150224711", "question": "Where can you take a horse and carriage ride in a major U.S. city?", "question_concept": "horse", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["central park", "state fair", "american southwest", "nyc", "canada"]}, "answerKey": ""}
{"id": "d39f9785e678bbf45a5727d5f447138b", "question": "Why might you be getting in at the end of the line?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anxiety", "have to wait for", "longer lines", "fatigue", "late"]}, "answerKey": ""}
{"id": "590bf9fa97a3d5791c85767e25302c04", "question": "Bill put gold on the scale, where does BIll likely work?", "question_concept": "scale", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["post office", "assay office", "butcher shop", "music store", "university"]}, "answerKey": ""}
{"id": "5c6ae1d05b6344bf1be0057dc3be65f2", "question": "A painting has damage, what might be found on the canvas?", "question_concept": "painting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["great relief", "slashes", "beauty", "new look", "new color"]}, "answerKey": ""}
{"id": "e5febe99cbd3635a14688e0a0c89fae4", "question": "Where is someone likely to participate in an excavation of an Egyptian mummy?", "question_concept": "excavation", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cemetary", "city", "archeological site", "canada", "construction site"]}, "answerKey": ""}
{"id": "83a89f069b856fb093e1e011b282c3b5", "question": "What will happen when cancer does not go away?", "question_concept": "cancer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["spread", "result in death", "kill", "bad", "illness occur"]}, "answerKey": ""}
{"id": "a4bc3a9d50055ea3004a86ff29816837", "question": "Where is a strip shopping mall likely to be?", "question_concept": "shopping mall", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pennslyvania", "indiana", "suburbs", "forest", "downtown"]}, "answerKey": ""}
{"id": "07e3b3766539a6b7e0f4d28ab1a19cac", "question": "What does someone do to a curling iron before leaving the house?", "question_concept": "curling iron", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "bathroom", "hair salon", "use", "turn it off"]}, "answerKey": ""}
{"id": "f7476896a6e64c7b3f3904b4038948ef", "question": "What could you put a table on top of?", "question_concept": "table", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["corner", "rug", "library", "kitchen", "table"]}, "answerKey": ""}
{"id": "13c080abc144ed90d5e78748e347037f", "question": "Alcoholic refreshment is likely banned in what space for people without permanent dwellings?", "question_concept": "alcoholic", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "grocery store", "prison", "homeless shelter", "bar"]}, "answerKey": ""}
{"id": "0870ee9e3a47c384a484ae7cdc0a9d48", "question": "Where would you find a famous apple tree?", "question_concept": "apple tree", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["park", "washington state", "fields", "new york", "rain forest"]}, "answerKey": ""}
{"id": "e165beeb49b36551a4b8abc471ec2c8d", "question": "Some people do not care for milk because of what in it?", "question_concept": "milk", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["lactose", "water", "water", "calcium", "refrigerator"]}, "answerKey": ""}
{"id": "e918ba21a1018dd61e5a6fccd8f0527d", "question": "What is talking to an expert likely to result in?", "question_concept": "talking to", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn", "friendship", "persuaded", "listen", "communication"]}, "answerKey": ""}
{"id": "b87d8520559d06d2a54e06d27ea60eb2", "question": "How did the infirm man feel after getting in line at the pharmacy?", "question_concept": "getting in line", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["anxiety", "fatigue", "wait turn", "satisfied", "terrible"]}, "answerKey": ""}
{"id": "04e5f0a58c749ecc4f452a21fd94dc47", "question": "Dave was playing at the 18th hold near the Krispy Kerme and he lost his ball.  He looked in his backpack for a replacement and come up with a terrible idea.  There was a crinkling sound that alerted the other players that something was amiss, and his makeshift ball did not go far.  What did he use as a ball?", "question_concept": "hole", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["golf course", "swiss cheese", "play ground", "notebook paper", "doughnut"]}, "answerKey": ""}
{"id": "016324203f91f22d936eaa74773b4460", "question": "James got clothing for Christmas.  He folded it carefully and put it somewhere. Where might he has put it?", "question_concept": "clothing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "suitcase", "closet", "floor", "house"]}, "answerKey": ""}
{"id": "7a1fdff17d645dd9b83ff239e944290d", "question": "If your'e going to market what should you bring?", "question_concept": "going to market", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stress", "arriving at desination", "impulse buying", "an empty wallet", "carrying bags"]}, "answerKey": ""}
{"id": "898e890d6b44d1d29028177262813bd8", "question": "The TV host would eat hamburger and explain how what it was to the camera?", "question_concept": "eat hamburger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfy hunger", "cook one", "hungry", "good to eat", "protein"]}, "answerKey": ""}
{"id": "8205599dadfc8a9624cddb0a89d3e06a", "question": "Where would a passageway covered in vines be found?", "question_concept": "passageway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["jungle", "video game", "airport", "maze", "hotel"]}, "answerKey": ""}
{"id": "b693fc52a837fb99c79480ceaa2401f8", "question": "A bald eagle lifts into the air without flapping its wings, what did it take advantage of?", "question_concept": "bald eagle", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["minnesota", "thermal", "feathers", "photograph", "colorado"]}, "answerKey": ""}
{"id": "c8012ae18ae6dec2dce5780eec1bec27", "question": "Where is a convenient place to store paper clips when not in use?", "question_concept": "paper clips", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["work", "fashioned into simple tools", "drawer", "desktop", "university"]}, "answerKey": ""}
{"id": "c5dbe48097fb33ddd77d6aea77d983e7", "question": "Where can you occasionally still find a telephone booth?", "question_concept": "telephone booth", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["london", "urban areas", "restuarant", "gas stations", "city"]}, "answerKey": ""}
{"id": "9809509ba87af09ee945401653532406", "question": "When a horse picks up speed it begins to what?", "question_concept": "horses", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drink water", "buck", "eat oats", "canter", "trot"]}, "answerKey": ""}
{"id": "35d68bb6b9ec00475237433394156bc8", "question": "Where do you go to see the statue of liberty?", "question_concept": "statue", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["central park", "museum", "church", "water fountain", "new york city"]}, "answerKey": ""}
{"id": "dac773277f2cd5d7c520c953aaaae3db", "question": "Where are birds likely most happy?", "question_concept": "birds", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["roof", "air", "park", "swimming", "countryside"]}, "answerKey": ""}
{"id": "1310d3f4595280be53d11501c4d488c1", "question": "Sam didn't care what I did to him.  All he wanted was to know that I was trying to help fix what I caused to break.  But it wasn't easy doing what to it?", "question_concept": "break", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["electrical circuit", "putting together", "breakdance", "working", "accelerate"]}, "answerKey": ""}
{"id": "9cf23f629c3bbbb504a0c3d4283d37cf", "question": "What is a place where not all creatures live, but where it is possible to see a creature?", "question_concept": "creature", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["world", "dark place", "forest", "woods", "zoo"]}, "answerKey": ""}
{"id": "3b41f0bf3db2ebba1908d4f70df615ff", "question": "What would you do to animals if you do not want to eat them?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["roast", "keep alive", "lie down", "need to eat", "bite"]}, "answerKey": ""}
{"id": "********************************", "question": "Where are animals tested on?", "question_concept": "animals", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["state park", "trigonometry", "fairgrounds", "zoos", "laboratory"]}, "answerKey": ""}
{"id": "3de34881e026507dbb9d12ed07157e3c", "question": "While attending a lecture what can you do so that you can study important points later?", "question_concept": "attending lecture", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling asleep", "applaud", "hear about neat", "taking notes", "learning"]}, "answerKey": ""}
{"id": "f559fe00d347816ee89722dba7e4d35d", "question": "John was positive that he knew the truth.  Billy, on the other hand, felt what?", "question_concept": "positive", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falsely", "negative", "nonpositive", "bad", "uncertain"]}, "answerKey": ""}
{"id": "0fa12481bb43195ded7ab1b5ccf03216", "question": "What could a box be used for if it is not refrigerated and has moths in it?", "question_concept": "box", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["store objects", "can catch ball", "store clothes", "hold things", "store food"]}, "answerKey": ""}
{"id": "0add12d886f716f79808b73d611a22ab", "question": "He awaited the reply to his text, but all he got was an emoji for a what?", "question_concept": "reply", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["answer", "initiate", "ignore", "response", "question"]}, "answerKey": ""}
{"id": "379fb9160d4348692d1a5aa10556dab6", "question": "He really enjoyed renting time to play drums but he finally bought his own percussion instrument so he could play in his what?", "question_concept": "percussion instrument", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drum store", "own home", "sun dress", "orchestra", "music store"]}, "answerKey": ""}
{"id": "0fbafeab8d19ccfca5857c1c01c4956f", "question": "She was always there to comfort friend, if anybody was what, she was there?", "question_concept": "comfort friend", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["monetary", "money", "feeling bad", "sympathetic", "care"]}, "answerKey": ""}
{"id": "d43f3daa9631ba920192e5f174365ebb", "question": "He kept all his paper and plastic in a separate bin, this was so they could be what?", "question_concept": "paper", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["reused", "burnt", "written on", "printed upon", "recycled"]}, "answerKey": ""}
{"id": "7b73302fe5480ff969b7faeeb3f8b3b4", "question": "What do people participate in to go about changing society?", "question_concept": "changing society", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["social disruption", "accept society", "social activism", "change yourself", "argue"]}, "answerKey": ""}
{"id": "e1fa3519e0ff370988b5158dd64f4b74", "question": "You may want to lock what if you keep important documents inside?", "question_concept": "lock", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "ignition switch", "controlling whether door opened", "firearm", "door"]}, "answerKey": ""}
{"id": "5c92dc87f44998700d352ea224b19e68", "question": "A place where a lot of people live is called what?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["classroom", "hotel", "populated areas", "supermarket", "race track"]}, "answerKey": ""}
{"id": "2c248ffb5458cedf6881671bb96e8882", "question": "What type of common desk object can be used as a blade?", "question_concept": "blade", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["knife", "helicopter", "scissors", "tape dispenser", "lawn mower"]}, "answerKey": ""}
{"id": "5f8101c2b7ce5a57bd4d267b841e8444", "question": "She wanted to show of her front garden to everyone, so where did she put it?", "question_concept": "front garden", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["front yard", "michigan", "forest", "outside of house", "urban area"]}, "answerKey": ""}
{"id": "b450315e204252f3c1df6ae6e9719745", "question": "Jude was an opponent of Mr. Wiskers.  For this reason, Mr. June, who hated Mr. Miskers, was something for Jude.  What was he?", "question_concept": "opponent", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ally", "proponent", "advocate", "supporter", "companion"]}, "answerKey": ""}
{"id": "356336b2c101ea7a34eb98548c40c94e", "question": "He learned all about the radio for his extracurricular, it was his favorite memories of going to where?", "question_concept": "radio", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["school", "class", "shop", "space shuttle", "trunk"]}, "answerKey": ""}
{"id": "145c49ad28c153880693a99b5281d711", "question": "The teacher said to examine thing number one, it seemed simple but under the microscope it was quite what?", "question_concept": "examine thing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["learn about", "learned", "complex", "buy", "interesting"]}, "answerKey": ""}
{"id": "4931a13dd0dc1e2d2fb87e9aa78f0dad", "question": "Where is there a heat source in the house when people are sleeping together?", "question_concept": "heat source", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["solar energy", "bedroom", "body heat", "car", "coal or wood"]}, "answerKey": ""}
{"id": "78f8383b9ee9fba1779f4d42ea1edea6", "question": "You baby must crawl before they what?", "question_concept": "baby", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sleep", "talk nonsense", "boy or girl", "arrive early", "learn to walk"]}, "answerKey": ""}
{"id": "e307acb49938f9d0364120f10cb3edff", "question": "What would a person do if they do not succeed at something?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stand alone", "cross street", "fall asleep", "try again", "give up"]}, "answerKey": ""}
{"id": "b0748338b350225195774fcc4dab2b73", "question": "Where is the likely place in a house for a wardrobe?", "question_concept": "wardrobe", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["house", "bedroom", "mansion", "dressing room", "clothes cupboard"]}, "answerKey": ""}
{"id": "f13b10308db5c63f9453b5936bbb2c14", "question": "What do you do for a date?", "question_concept": "date", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pillage norway", "dress nice", "bathe", "go for haircut", "wait for"]}, "answerKey": ""}
{"id": "8a6a6783581db74ae107119281b6474b", "question": "What makes an accelerator go?", "question_concept": "accelerator", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["force", "car", "accelerate", "fuel system", "airplane"]}, "answerKey": ""}
{"id": "e0e0df0a4f682c5717e2ac81370eb4cb", "question": "James told Sam to stay calm.  It was a dark night in Chicago, but that wasn't important.  Harry was in his basement and had said that he didn't want to be what?", "question_concept": "calm", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["blustery", "stressed", "wild", "disturbed", "windy"]}, "answerKey": ""}
{"id": "60ad81e7f780c105d7337834308c8cd9", "question": "When a visitor asks me where to find the cereal, what do I likely tell them?", "question_concept": "cereal", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["at a store", "box", "shelf", "cabinet", "cupboard"]}, "answerKey": ""}
{"id": "e174c3538697c2286b7b4dbaedfa9ee8", "question": "Always wear protection if putting a fountain pen where?", "question_concept": "fountain pen", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["desk drawer", "lady's purse", "blotter", "behind ear", "shirt pocket"]}, "answerKey": ""}
{"id": "2ac0c61b6c04de3f8c3cf5dc23501ab5", "question": "Sarah wasn't good at lying.  She had an obvious tell.  Whenever she lied, her face would do what?", "question_concept": "lying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["avoid eye contact", "blush", "turn inside out", "feel guilty", "fall asleep"]}, "answerKey": ""}
{"id": "d391d07450e85d6d841a3e13ce93b9de", "question": "She was jogging for hours a day, what did her legs feel?", "question_concept": "jogging", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fitness", "lose weight", "fatigue", "socks", "getting in shape"]}, "answerKey": ""}
{"id": "b87b028cbe42e40e74b8f84f76c41f93", "question": "He was working hard on his sculpture, what was he practicing?", "question_concept": "working", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["prosperity", "earning money", "chiseling", "creation", "getting paid"]}, "answerKey": ""}
{"id": "279a90fda74c4251b2b67ea1f4723833", "question": "When you think of a gentleman you think of him wearing a formal what?", "question_concept": "gentleman", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tuxedo", "party", "big house", "men's room", "church"]}, "answerKey": ""}
{"id": "eda022d48cfb59230b21c8d632feb17f", "question": "John was sitting quietly after class. He was the only person in detention.  He enjoyed something about this.  What might he had enjoyed about his situation?", "question_concept": "sitting quietly", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["falling asleep", "solitude", "uniqueness", "relaxation", "discomfort"]}, "answerKey": ""}
{"id": "dade817bae98f69b39d59a63fbd7f49d", "question": "What is a place that usually has an elevator and that often has a telephone book?", "question_concept": "telephone book", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["house", "library", "at hotel", "salon", "telephone booth"]}, "answerKey": ""}
{"id": "970d5702610cae61d1980a3833003657", "question": "Where would you find a doctor caring for the elderly who are living together?", "question_concept": "doctor", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["personal home", "golf course", "emergency room", "nursing home", "medical school"]}, "answerKey": ""}
{"id": "9fbf966e07642db98799bcd509dda1ca", "question": "If you drink too much booze what are you likely to do?", "question_concept": "booze", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pass out", "stop bicycle", "examine thing", "stay in bed", "reach tentative agreement"]}, "answerKey": ""}
{"id": "2993d9aa7f3a164ec890187970fc3a97", "question": "What pig piece of furniture with cabinets can ferret damage?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["coffee table", "hutch", "north carolina", "own home", "great britain"]}, "answerKey": ""}
{"id": "d2ee3008cc48217e9755666214e5cad8", "question": "What are expectant mothers likely going to a party to celebrate?", "question_concept": "going to party", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dancing with kids", "babies", "getting drunk", "laughter", "happiness"]}, "answerKey": ""}
{"id": "aa2ff90ed49a7ffb5fe572f9d53fe1d2", "question": "A mom walks with laundry through the passageway, where is she?", "question_concept": "passageway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospital", "building", "cave", "house", "airport"]}, "answerKey": ""}
{"id": "8d8d31491e849cc3e7396e6ea7ffb8e2", "question": "The politician is answering questions, why is he doing that?", "question_concept": "answering questions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["teaching", "embarassment", "job interview", "people will understand", "children will learn"]}, "answerKey": ""}
{"id": "0aa07d7901c31086aae32dc4568a1740", "question": "Will something with a lot of value be cheap?", "question_concept": "value", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["expensive", "disrespect", "valuable", "worthlessness", "invaluable"]}, "answerKey": ""}
{"id": "886563af24c9626fd34fef097bd121ea", "question": "A human that tricks an artificial intelligence might what afterwards?", "question_concept": "human", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["write", "have fever", "die", "have to sleep", "smile"]}, "answerKey": ""}
{"id": "0183fb599bd83746869b7e3a08772e97", "question": "Where are people likely to put a chair at a counter?", "question_concept": "chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cubicle", "porch", "living room", "church", "kitchen"]}, "answerKey": ""}
{"id": "8b7d08b479a2d32476dcb31470241043", "question": "If you're not good at remembering things you should do what?", "question_concept": "remembering", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["nostalgia", "sneeze", "being prepared", "writing down", "phoning"]}, "answerKey": ""}
{"id": "753bd9bcdae10c2e02d1fd1d20e0da0f", "question": "It can sometimes be difficult for a person who just did something embarrassing to?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sail boat", "swim", "laugh at himself", "cross street", "hurry up"]}, "answerKey": ""}
{"id": "f56437a35e05a6c80918c8550d066c71", "question": "John is moving to a new place and has to dig a well so that he'll have water.  Where might he be living?", "question_concept": "well", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ground", "idaho", "countryside", "oil field", "kansas"]}, "answerKey": ""}
{"id": "c40d1dbc70fad69fe1fc0ca6aed2adab", "question": "What can too much shopping lead to?", "question_concept": "shopping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["standing in line", "debt", "spending money", "tiredness", "being broke"]}, "answerKey": ""}
{"id": "3a6e4e767bf96b077a9f814abde877b2", "question": "What do you do when flirting with someone?", "question_concept": "flirting", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sexual attraction", "smile", "cry", "personality", "getting excited"]}, "answerKey": ""}
{"id": "2341e9d91c8b5d394a45b2fbcebad0b3", "question": "Why does someone want to commit suicide?", "question_concept": "commit suicide", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["dying", "end life", "happy", "drive into oncoming truck", "pain"]}, "answerKey": ""}
{"id": "e3b5993657e75d9c914e132514ff5951", "question": "He was the only one in the room sleeping, this was because his what kept others awake?", "question_concept": "sleeping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["talking", "get cold", "snoring", "might dream", "running"]}, "answerKey": ""}
{"id": "41f6c1367149a77a400d685582059b7b", "question": "What in you cap is an expression that means an accomplishment that makes you look good?", "question_concept": "feather", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cap", "pillow", "style look", "bird cage", "birds nest"]}, "answerKey": ""}
{"id": "f433755e8e1519f69efb55ce81b10793", "question": "What place would a student use a pencil at?", "question_concept": "pencil", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["library", "desktop", "classroom", "desk drawer", "backpack"]}, "answerKey": ""}
{"id": "9f33a00d0afc321d2ca1f4bd350d5b7c", "question": "What is the result of not being careful when having sex?", "question_concept": "having sex", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stds", "unwanted pregnancy", "orgasm", "bliss", "getting pregnant"]}, "answerKey": ""}
{"id": "fe9d17c53bed2f836af3704b2f2f0b95", "question": "James has finished his classes, but he's studying intensely anyway.  Why might he want to study when his classes are finished?", "question_concept": "studying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["gathering information", "impress teacher", "headaches", "inspiration", "knowing more"]}, "answerKey": ""}
{"id": "7861e40cb6305c37cecddd49dc71e49d", "question": "What does the cat try to catch when it is shining from a windom?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["fly", "floor", "back yard", "warm place", "beam of sunlight"]}, "answerKey": ""}
{"id": "1c430edbdfb11363836142f044f02254", "question": "The couple wanted to buy house, but they didn't have enough to what it?", "question_concept": "buy house", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["negotiate", "settle down", "bargain", "lose money", "pay for"]}, "answerKey": ""}
{"id": "0f57857b75c023422a6bc27540c8fa21", "question": "How will cooling off help you when thinking about what to do?", "question_concept": "cooling off", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["chills", "better decisions", "swim", "calm down", "expansion"]}, "answerKey": ""}
{"id": "74af5a95ab64f32be515c5c73e29300c", "question": "Where would you find a pistol near people in blue uniforms?", "question_concept": "pistol", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["war", "security", "pants", "police station", "police officer's belt"]}, "answerKey": ""}
{"id": "77959fd44af8ca200d3690e0e1f3256d", "question": "What do students rarely have time to do for pleasure only?", "question_concept": "students", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ask questions", "estimate value", "better comprehension", "study books", "read books"]}, "answerKey": ""}
{"id": "8ddf2a704ddc4df5538dcecf14fde957", "question": "When reaching advantage in things, you are said to have superior?", "question_concept": "reaching advantage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["superior", "insight", "determination", "skill", "upper hand"]}, "answerKey": ""}
{"id": "9677d15134e8bdef90c16e91f9a67696", "question": "James wanted onions but Sarah didn't want any. Because of this,  they split the what in half?", "question_concept": "onions", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["refrigerator", "pizza", "table", "kitchen", "pantry"]}, "answerKey": ""}
{"id": "27896354b9e2d90b96b46a917c92de14", "question": "What would happen to someone if a killing is happening?", "question_concept": "killing", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["commit crime", "die", "being caught", "feel mad", "feel anger"]}, "answerKey": ""}
{"id": "61b6e331f3277fd70a2df08e762b3474", "question": "Behind a restaurant, where can a cat find food?", "question_concept": "cat", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["floor", "houseplant", "urban neighborhood", "container", "dumpsters"]}, "answerKey": ""}
{"id": "a121ce1fb19f8e89e36c38bbbbd4ba49", "question": "Where would you put a vase to make sure the flowers inside it get enough sun?", "question_concept": "vase", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cabinet", "shelf", "fall off shelf", "windowsill", "table"]}, "answerKey": ""}
{"id": "22d68f07d577eb599c44f1935461bdc9", "question": "where do children use glue sticks?", "question_concept": "glue stick", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["apply glue to surfaces", "office", "bedroom", "desk drawer", "classroom"]}, "answerKey": ""}
{"id": "33fc33503d33164b7f788d163ad7a8db", "question": "What emotion does habitual lying tends to cause?", "question_concept": "lying", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get caught", "mistrust", "being fired", "problems", "broken heart"]}, "answerKey": ""}
{"id": "a81a1c6aa70db5609a9fadb95e361755", "question": "What does being under water for a long time lead to?", "question_concept": "water", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["flooding", "blizzard", "drowning", "surface of earth", "cactus"]}, "answerKey": ""}
{"id": "e68302bbf0173ab999ee2ab74d991ef2", "question": "What can happen if you are cogitating successfully?", "question_concept": "cogitating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["crying", "headaches", "decision", "enlightenment", "new thoughts"]}, "answerKey": ""}
{"id": "9755fad2c2f39af584d2da77c011f5d8", "question": "Where can you find the most large container?", "question_concept": "large container", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["garden", "cabinet", "factory", "bedroom", "juice"]}, "answerKey": ""}
{"id": "3dc643db185338cfd8ec5a58f1cb6598", "question": "You can find paid parking lots in what part of a city?", "question_concept": "parking lot", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["town", "car", "pedestrian path", "business district", "city"]}, "answerKey": ""}
{"id": "8a1ef69ebc3a93b6c2e08202eeaea749", "question": "Babies must be careful when having a bath, what could happen with a distracted or absent parent?", "question_concept": "having bath", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["get wet", "wet hair", "wet skin", "drowning", "rash"]}, "answerKey": ""}
{"id": "b2e9ee51cf341b072b77e2e1d57b86e3", "question": "The music was loud do you know where it came from", "question_concept": "music", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["opera", "bathroom", "carnival", "night club", "elevator"]}, "answerKey": ""}
{"id": "ed2950219edc87d0bfacc41b8bf22217", "question": "Muslims believe that women should not have bare head but that they should be what?", "question_concept": "bare", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["ample", "bareword", "covered", "full", "dressed"]}, "answerKey": ""}
{"id": "9abefd2db791cdfd84f5faa0d7cbbf67", "question": "What is someone doing when contemplating the nature of the universe?", "question_concept": "contemplating", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sense of fulfillment", "clear thought", "thinking", "deep thoughts", "contemplating"]}, "answerKey": ""}
{"id": "7755512b863ce76553ba4c8ed7f9d4cf", "question": "Where could there be cold storage with millions of pieces of food in it?", "question_concept": "cold storage", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["warehouse", "laboratory", "meat van", "refrigerator", "grocery store"]}, "answerKey": ""}
{"id": "0e4e1db19c9080c10e266d102296d9c1", "question": "What does a student at a desk do first?", "question_concept": "student", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["stand", "complete test", "do mathematics", "begin to study", "learn language"]}, "answerKey": ""}
{"id": "e34aa4ac0dcd962eca8bedb461971043", "question": "Some people begin helping others because of guilt, others just do it out of the goodness of their what?", "question_concept": "helping", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["satisfaction", "kindness", "pay", "heart", "happiness"]}, "answerKey": ""}
{"id": "35265d63f143f358a8b513555909504e", "question": "James clicked what he thought was a menu, but it took him to a different place.  It turned out to be what?", "question_concept": "menu", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["cafe", "advertisement", "bar", "computer program", "internet sites"]}, "answerKey": ""}
{"id": "14363ea03a865beee43d3f72d1138996", "question": "The weather was threatening to postpone the what being done to the outside of the building?", "question_concept": "postpone", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hasten", "work", "stop", "act", "decide"]}, "answerKey": ""}
{"id": "94faa86c352cd71b613bef387ee22dcd", "question": "Where would you find people wearing uniforms and chasing a rubber disc?", "question_concept": "people", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["opera", "hockey game", "hospital", "water cooler", "office"]}, "answerKey": ""}
{"id": "ba0c611a9592548f6d90deb12b8db1ff", "question": "What sort of emotion would be experienced when out and about in an unfamiliar area?", "question_concept": "going somewhere", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["elapsed time", "happiness", "movement", "relaxation", "uneasiness"]}, "answerKey": ""}
{"id": "5ab5deb85596600323a0807c6ed4a2af", "question": "When you're finished with a chess set you'd put it in what?", "question_concept": "chess set", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["drawer", "cupboard", "dorm", "refrigerator", "sitting room"]}, "answerKey": ""}
{"id": "b5406e7a0fbaa7b8ee42fd256c7636a5", "question": "Where is a county highway likely to cut through?", "question_concept": "county highway", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["michigan", "country", "counties", "rural areas", "map"]}, "answerKey": ""}
{"id": "9c06a9149414c95e2237cfb88173c410", "question": "In what structure are you likely to find a bean bag chair?", "question_concept": "bean bag chair", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["palace", "floor", "den", "house", "family room"]}, "answerKey": ""}
{"id": "bcde206a472d28a043f7a46f7d7a772a", "question": "Residents of the small town feared a new shopping center, what didn't they want their town to become?", "question_concept": "shopping center", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["people", "stores", "suburbs", "populated area", "urban area"]}, "answerKey": ""}
{"id": "24db593eb9126849f7e07e88057260d2", "question": "Where would I be responsible for blowing up the toy balloon?", "question_concept": "toy balloon", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["supermarket", "circus", "birthday party", "toy store", "amusement park"]}, "answerKey": ""}
{"id": "93eb8f1347d48b7a5b39b78d7cdac7af", "question": "In what group of people's homes will you find a corner shop?", "question_concept": "corner shop", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["iowa", "kentucky", "miami", "town", "england"]}, "answerKey": ""}
{"id": "c0e1007d2b344552f19db83350fae592", "question": "The lust of one night can lead to a life altering situation if the two parties inadvertently what?", "question_concept": "lust", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["kiss", "copulate", "go to party", "procreate", "have sex"]}, "answerKey": ""}
{"id": "903d079abdb46a531cedf482ee70d407", "question": "The person was trying to ignore other passenger on the plane talking incessantly, what did that person feel?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["hospitalized", "broken heart", "schizophrenia", "cold", "annoyance"]}, "answerKey": ""}
{"id": "0f472940407eb31a1cbeb9ad33e4280f", "question": "The old couple wanted to see particular program at the theater but were unsure when it started, they went to the theater to what?", "question_concept": "see particular program", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["tape", "buy tickets", "find out when", "hatred", "stop channelsurfing"]}, "answerKey": ""}
{"id": "6b4e541bc7f12e38f4a8a15962c694ce", "question": "The doors closed to customers and he got to do his favorite part of the job, he began stocking product on the what?", "question_concept": "product", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelf", "demonstration", "store", "ground", "market"]}, "answerKey": ""}
{"id": "9ab8bd4cfff2d2b964b5fb9a4a57b074", "question": "Out relationship with dogs is very one sided.  We can do something to dogs, but they can't do the same thing to us.  What can't dogs do to us?", "question_concept": "dogs", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["own people", "go outside", "fed us", "reproduce", "need food"]}, "answerKey": ""}
{"id": "485feba10623280ddfbc7f99a6c8bc1a", "question": "Where is likely to have a small traffic artery?", "question_concept": "traffic artery", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["town", "freeway", "major city", "high traffic area", "highway"]}, "answerKey": ""}
{"id": "3bf15ba95e7233d8e81cd998308ccde8", "question": "The man's greatest weakness was salty snacks, he grabbed bag after bag of chips every trip to the where?", "question_concept": "chips", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pantry", "bar", "motherboard", "supermarket", "work"]}, "answerKey": ""}
{"id": "08ca0024cb29cbb8d6673175ec543d28", "question": "The person hadn't slept in 3 days, what did his brain experience?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["sadness", "arm himself", "receive mail", "feel sleepy", "catch cold"]}, "answerKey": ""}
{"id": "815a8367d08a14f150a6c777ad7f789a", "question": "Where would you put a laptop computer if you want to use it outside?", "question_concept": "computer", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["box", "office building", "backpack", "school", "table"]}, "answerKey": ""}
{"id": "9082b65f2bc5328ea991f734f930ddb5", "question": "If children were in a gym, would they be doing?", "question_concept": "children", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["watch television", "play basketball", "cut and paste", "swimming", "reach over"]}, "answerKey": ""}
{"id": "3abf430c8338c3a4cdaa3e26b96bcae2", "question": "What is a place where people live that has dishes?", "question_concept": "dishes", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["shelf", "apartment", "cabinet", "dining room", "pantry"]}, "answerKey": ""}
{"id": "fb46652b6016be675e301fafe03222f3", "question": "The situation was causing anger, but his wife subdued it with her heartwarming what?", "question_concept": "anger", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["punch", "calm", "field", "sweetness", "happiness"]}, "answerKey": ""}
{"id": "27a3f39930a7383a9723897eb0e88f20", "question": "John the ferret was a pet ferret of a Kindergarten.  It liked to get into things that it shouldn't get into.  One day the ferret went missing and the whole school looked for it.  Mr. Johnson felt something scurry up his leg and settle around his private area. Mr. Johnson did not like this one bit.  Where was the ferret?", "question_concept": "ferret", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["pair of trousers", "on holiday", "classroom", "great britain", "bad mood"]}, "answerKey": ""}
{"id": "c9a82c294ae81ca5f2b4dd7f4c031310", "question": "What does a person do at the end of every month?", "question_concept": "person", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["welcome change", "catch cold", "become depressed", "pay bills", "say hi"]}, "answerKey": ""}
{"id": "c96f53d1a064f277d805dd00f3d9402d", "question": "If someone is typically in an excellent mood but things just are not going well for them that day, what kind of day are they having?", "question_concept": "excellent", "choices": {"label": ["A", "B", "C", "D", "E"], "text": ["bad", "horrible", "poor performance", "awful", "poor"]}, "answerKey": ""}
