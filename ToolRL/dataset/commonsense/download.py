import os
import json
from datasets import load_dataset

# 设置保存目录
save_dir = "/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/commonsense"
os.makedirs(save_dir, exist_ok=True)

# 加载数据集
dataset = load_dataset("commonsense_qa")

# 将每个子集（train, validation, test）保存为 JSON 文件
for split in dataset:
    data_list = dataset[split]
    json_path = os.path.join(save_dir, f"commonsenseqa_{split}.jsonl")
    
    with open(json_path, "w", encoding="utf-8") as f:
        for item in data_list:
            json.dump(item, f, ensure_ascii=False)
            f.write("\n")

print("✅ 已将 CommonsenseQA 数据集保存为 JSON 格式：", save_dir)
