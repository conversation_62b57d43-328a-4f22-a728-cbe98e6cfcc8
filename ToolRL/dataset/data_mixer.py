import json
import pandas as pd
import numpy as np
from typing import List, Dict
import os
from collections import Counter
import random
import re

def load_json_data(file_path: str) -> List[Dict]:
    """Load data from JSON file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def convert_to_parquet_format(data: List[Dict], dataset_name: str) -> pd.DataFrame:
    """Convert data to parquet format with correct structure"""
    rows = []
    
    for idx, item in enumerate(data):
        # 创建prompt列
        prompt = [
            {"content": item["instruction"], "role": "system"},
            {"content": item["input"], "role": "user"}
        ]
        
        # 创建reward_model列
        reward_model = {
            "ground_truth": item["output"],
            "style": "search_efficiency",  # 使用效率搜索奖励模型
            "used_retrieval": '<tool_call>' in item["output"]  # 检查是否使用了工具
        }
        
        # 创建extra_info列
        extra_info = {
            "index": idx,
            "input": item["input"],
            "instruction": item["instruction"],
            "output": item["output"],
            "split": "train",
            "dataset": dataset_name
        }
        
        # 创建行数据
        row = {
            "data_source": dataset_name,
            "prompt": prompt,
            "ability": "search",
            "reward_model": reward_model,
            "extra_info": extra_info
        }
        
        rows.append(row)
    
    return pd.DataFrame(rows)

def count_tool_calls(text: str) -> int:
    return len(re.findall(r'<tool_call>.*?</tool_call>', text, re.DOTALL))

def split_into_pools(data: List[Dict], dataset_name: str):
    search_pool = []
    no_search_pool = []
    for item in data:
        if count_tool_calls(item["output"]) >= 1:
            search_pool.append(item)
        else:
            no_search_pool.append(item)
    return search_pool, no_search_pool

def sample_from_pool(pool, n, allow_float=0.1):
    # n允许±10%浮动
    min_n = int(n * (1 - allow_float))
    max_n = int(n * (1 + allow_float))
    if len(pool) >= min_n:
        actual_n = min(max_n, len(pool), n) if len(pool) < n else n
        return random.sample(pool, actual_n)
    else:
        return pool.copy()

def batch_sampler(hotpotqa_search, hotpotqa_no_search, strategyqa_search, strategyqa_no_search, commonsenseqa_search, commonsenseqa_no_search, batch_size=512, epoch_shuffle=True):
    # 调整采样比例，考虑实际数据分布
    # HotpotQA: ~99% 搜索, 总体占比约34%
    hotpotqa_search_n = 170      # 增加搜索比例
    hotpotqa_no_search_n = 2     # 减少不搜比例
    
    # StrategyQA: ~40% 搜索, 总体占比约42%
    strategyqa_search_n = 85     # 40% * 212
    strategyqa_no_search_n = 127 # 60% * 212
    
    # CommonsenseQA: 0% 搜索, 总体占比约24%
    commonsenseqa_search_n = 0    # 无搜索样本
    commonsenseqa_no_search_n = 128 # 全部不搜
    
    pools = [
        hotpotqa_search.copy(), hotpotqa_no_search.copy(), 
        strategyqa_search.copy(), strategyqa_no_search.copy(),
        commonsenseqa_search.copy(), commonsenseqa_no_search.copy()
    ]
    pool_names = [
        "hotpotqa_search", "hotpotqa_no_search", 
        "strategyqa_search", "strategyqa_no_search",
        "commonsenseqa_search", "commonsenseqa_no_search"
    ]
    
    if epoch_shuffle:
        for p in pools:
            random.shuffle(p)
            
    batches = []
    exhausted = False
    
    while not exhausted:
        batch = []
        batch_parts = [[], [], [], [], [], []]
        need = [
            hotpotqa_search_n, hotpotqa_no_search_n,
            strategyqa_search_n, strategyqa_no_search_n,
            commonsenseqa_search_n, commonsenseqa_no_search_n
        ]
        
        # 主采样
        for i in range(6):
            part = sample_from_pool(pools[i], need[i], allow_float=0.1)
            batch_parts[i].extend(part)
            pools[i] = [x for x in pools[i] if x not in part]
            need[i] -= len(part)
            
        # 同数据集内补采样
        # hotpotqa互补
        if need[0] > 0 and len(pools[1]) > 0:
            part = sample_from_pool(pools[1], need[0], allow_float=0.1)
            batch_parts[0].extend(part)
            pools[1] = [x for x in pools[1] if x not in part]
            need[0] -= len(part)
        if need[1] > 0 and len(pools[0]) > 0:
            part = sample_from_pool(pools[0], need[1], allow_float=0.1)
            batch_parts[1].extend(part)
            pools[0] = [x for x in pools[0] if x not in part]
            need[1] -= len(part)
            
        # strategyqa互补
        if need[2] > 0 and len(pools[3]) > 0:
            part = sample_from_pool(pools[3], need[2], allow_float=0.1)
            batch_parts[2].extend(part)
            pools[3] = [x for x in pools[3] if x not in part]
            need[2] -= len(part)
        if need[3] > 0 and len(pools[2]) > 0:
            part = sample_from_pool(pools[2], need[3], allow_float=0.1)
            batch_parts[3].extend(part)
            pools[2] = [x for x in pools[2] if x not in part]
            need[3] -= len(part)
            
        # commonsenseqa互补
        if need[4] > 0 and len(pools[5]) > 0:
            part = sample_from_pool(pools[5], need[4], allow_float=0.1)
            batch_parts[4].extend(part)
            pools[5] = [x for x in pools[5] if x not in part]
            need[4] -= len(part)
        if need[5] > 0 and len(pools[4]) > 0:
            part = sample_from_pool(pools[4], need[5], allow_float=0.1)
            batch_parts[5].extend(part)
            pools[4] = [x for x in pools[4] if x not in part]
            need[5] -= len(part)
            
        # 跨数据集补采样
        # 遍历每个需要补充的池
        for i in range(6):
            if need[i] > 0:
                # 从其他数据集的池中补充
                other_pools = list(range(6))
                other_pools.remove(i)
                other_pools.remove((i//2)*2 + (1-i%2))  # 移除同数据集的另一个池
                for j in other_pools:
                    if len(pools[j]) > 0 and need[i] > 0:
                        part = sample_from_pool(pools[j], need[i], allow_float=0.1)
                        batch_parts[i].extend(part)
                        pools[j] = [x for x in pools[j] if x not in part]
                        need[i] -= len(part)
                        
        # 合并batch
        for part in batch_parts:
            batch.extend(part)
            
        # 若总数不足batch_size，直接补满
        if len(batch) < batch_size:
            all_pools = sum(pools, [])
            fill = sample_from_pool(all_pools, batch_size - len(batch), allow_float=0.1)
            batch.extend(fill)
            # 从所有池中移除
            for i in range(6):
                pools[i] = [x for x in pools[i] if x not in fill]
                
        # 若总数超出，随机采样batch_size
        if len(batch) > batch_size:
            batch = random.sample(batch, batch_size)
            
        random.shuffle(batch)
        batches.append(batch)
        
        # 判断是否所有池都耗尽
        if sum(len(p) for p in pools) < batch_size:
            exhausted = True
            
    return batches

def verify_batch_distribution_batches(batches):
    print("\n=== 验证数据分布 ===")
    for i, batch in enumerate(batches[:5]):
        hotpot_count = sum(1 for x in batch if x.get('data_source', '') == 'hotpotqa')
        strategy_count = sum(1 for x in batch if x.get('data_source', '') == 'strategyqa')
        commonsense_count = sum(1 for x in batch if x.get('data_source', '') == 'commonsenseqa')
        search_count = sum(1 for x in batch if '<tool_call>' in x['output'])
        nosearch_count = len(batch) - search_count
        print(f"Batch {i}: HotpotQA={hotpot_count}, StrategyQA={strategy_count}, CommonsenseQA={commonsense_count}, "
              f"搜索={search_count}, 不搜={nosearch_count}, 搜索比例={search_count/len(batch):.1%}")

def overall_distribution(all_data):
    total = len(all_data)
    search = sum(1 for x in all_data if '<tool_call>' in x['output'])
    nosearch = total - search
    hotpot = sum(1 for x in all_data if x.get('data_source', '') == 'hotpotqa')
    strategy = sum(1 for x in all_data if x.get('data_source', '') == 'strategyqa')
    commonsense = sum(1 for x in all_data if x.get('data_source', '') == 'commonsenseqa')
    print(f"\n=== 全量数据统计 ===")
    print(f"Total: {total}, HotpotQA: {hotpot}, StrategyQA: {strategy}, CommonsenseQA: {commonsense}")
    print(f"Used Search: {search} ({search/total:.1%}), No Search: {nosearch} ({nosearch/total:.1%})")

def sample_val_and_save(hotpotqa_search, hotpotqa_no_search, strategyqa_search, strategyqa_no_search, 
                       commonsenseqa_search, commonsenseqa_no_search, output_dir):
    # 目标采样数 - 调整验证集比例
    n_hotpot_search = 25         # 增加搜索样本
    n_hotpot_nosearch = 1        # 减少不搜样本
    
    n_strategy_search = 15       # ~40%
    n_strategy_nosearch = 22     # ~60%
    
    n_commonsense_search = 0     # 无搜索样本
    n_commonsense_nosearch = 17  # 全部不搜
    
    # 实际采样
    hotpotqa_search_sample = random.sample(hotpotqa_search, min(n_hotpot_search, len(hotpotqa_search)))
    hotpotqa_no_search_sample = random.sample(hotpotqa_no_search, min(n_hotpot_nosearch, len(hotpotqa_no_search)))
    strategyqa_search_sample = random.sample(strategyqa_search, min(n_strategy_search, len(strategyqa_search)))
    strategyqa_no_search_sample = random.sample(strategyqa_no_search, min(n_strategy_nosearch, len(strategyqa_no_search)))
    commonsenseqa_search_sample = random.sample(commonsenseqa_search, min(n_commonsense_search, len(commonsenseqa_search)))
    commonsenseqa_no_search_sample = random.sample(commonsenseqa_no_search, min(n_commonsense_nosearch, len(commonsenseqa_no_search)))
    
    # 补足各池不足
    pools = [
        (hotpotqa_search_sample, hotpotqa_no_search, n_hotpot_search),
        (hotpotqa_no_search_sample, hotpotqa_search, n_hotpot_nosearch),
        (strategyqa_search_sample, strategyqa_no_search, n_strategy_search),
        (strategyqa_no_search_sample, strategyqa_search, n_strategy_nosearch),
        (commonsenseqa_search_sample, commonsenseqa_no_search, n_commonsense_search),
        (commonsenseqa_no_search_sample, commonsenseqa_search, n_commonsense_nosearch)
    ]
    
    for i, (sample, complement_pool, target) in enumerate(pools):
        if len(sample) < target:
            need = target - len(sample)
            can_fill = [x for x in complement_pool if x not in pools[i^1][0]]  # i^1 flips last bit (0↔1, 2↔3, 4↔5)
            fill = random.sample(can_fill, min(need, len(can_fill)))
            pools[i] = (sample + fill, complement_pool, target)
    
    val_samples = sum((sample for sample, _, _ in pools), [])
    
    # 去重
    val_samples = list({id(x): x for x in val_samples}.values())
    
    # 输出实际分布
    total = len(val_samples)
    search = sum(1 for x in val_samples if '<tool_call>' in x['output'])
    nosearch = total - search
    hotpot = sum(1 for x in val_samples if x.get('data_source', '') == 'hotpotqa')
    strategy = sum(1 for x in val_samples if x.get('data_source', '') == 'strategyqa')
    commonsense = sum(1 for x in val_samples if x.get('data_source', '') == 'commonsenseqa')
    
    print(f"\n=== 验证集分布 ===")
    print(f"Total: {total}")
    print(f"HotpotQA: {hotpot}, StrategyQA: {strategy}, CommonsenseQA: {commonsense}")
    print(f"Used Search: {search} ({search/total:.1%}), No Search: {nosearch} ({nosearch/total:.1%})")
    
    # 保存val.parquet
    df = convert_to_parquet_format(val_samples, "mixed")
    output_path = os.path.join(output_dir, 'val.parquet')
    df.to_parquet(output_path, index=False)
    print(f"已保存验证集到: {output_path}")
    
    # 剩余未采样数据
    used_ids = set(id(x) for x in val_samples)
    all_data = (hotpotqa_search + hotpotqa_no_search + 
                strategyqa_search + strategyqa_no_search +
                commonsenseqa_search + commonsenseqa_no_search)
    remaining = [x for x in all_data if id(x) not in used_ids]
    
    unused_path = os.path.join(output_dir, '未使用.json')
    with open(unused_path, 'w', encoding='utf-8') as f:
        json.dump(remaining, f, ensure_ascii=False, indent=2)
    print(f"已保存未使用数据到: {unused_path}")

def mix_datasets_v2(hotpot_path: str, strategy_path: str, commonsenseqa_path: str, output_dir: str, batch_size: int = 512):
    # 加载数据
    hotpot_data = load_json_data(hotpot_path)
    strategy_data = load_json_data(strategy_path)
    commonsenseqa_data = load_json_data(commonsenseqa_path)
    
    print(f"原始数据量:")
    print(f"HotpotQA: {len(hotpot_data)} 样本")
    print(f"StrategyQA: {len(strategy_data)} 样本")
    print(f"CommonsenseQA: {len(commonsenseqa_data)} 样本")
    
    # 六池分桶
    hotpotqa_search, hotpotqa_no_search = split_into_pools(hotpot_data, "hotpotqa")
    strategyqa_search, strategyqa_no_search = split_into_pools(strategy_data, "strategyqa")
    commonsenseqa_search, commonsenseqa_no_search = split_into_pools(commonsenseqa_data, "commonsenseqa")
    
    print(f"HotpotQA 搜索: {len(hotpotqa_search)}，不搜: {len(hotpotqa_no_search)}")
    print(f"StrategyQA 搜索: {len(strategyqa_search)}，不搜: {len(strategyqa_no_search)}")
    print(f"CommonsenseQA 搜索: {len(commonsenseqa_search)}，不搜: {len(commonsenseqa_no_search)}")
    
    # 采样并保存验证集和未使用数据
    sample_val_and_save(
        hotpotqa_search, hotpotqa_no_search,
        strategyqa_search, strategyqa_no_search,
        commonsenseqa_search, commonsenseqa_no_search,
        output_dir
    )
    
    # 采样batch
    batches = batch_sampler(
        hotpotqa_search, hotpotqa_no_search,
        strategyqa_search, strategyqa_no_search,
        commonsenseqa_search, commonsenseqa_no_search,
        batch_size=batch_size
    )
    
    # 合并所有batch
    all_data = sum(batches, [])
    
    # 全量分布统计
    total = len(all_data)
    search = sum(1 for x in all_data if '<tool_call>' in x['output'])
    nosearch = total - search
    hotpot = sum(1 for x in all_data if x.get('data_source', '') == 'hotpotqa')
    strategy = sum(1 for x in all_data if x.get('data_source', '') == 'strategyqa')
    commonsense = sum(1 for x in all_data if x.get('data_source', '') == 'commonsenseqa')
    
    print(f"\n=== 全量数据统计 ===")
    print(f"Total: {total}")
    print(f"HotpotQA: {hotpot}, StrategyQA: {strategy}, CommonsenseQA: {commonsense}")
    print(f"Used Search: {search} ({search/total:.1%}), No Search: {nosearch} ({nosearch/total:.1%})")
    
    # 转为DataFrame并保存
    df = convert_to_parquet_format(all_data, "mixed")
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, 'train.parquet')
    df.to_parquet(output_path, index=False)
    print(f"\n混合后数据量: {len(df)}，保存路径: {output_path}")
    
    verify_batch_distribution_batches(batches)

if __name__ == "__main__":
    hotpot_path = "/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train/hotpotqa_processed_records.json"
    strategy_path = "/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train/strategyqa_processed_records.json"
    commonsenseqa_path = "/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train/commonsenseqa_processed_records.json"
    output_dir = "/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train"
    mix_datasets_v2(hotpot_path, strategy_path, commonsenseqa_path, output_dir) 