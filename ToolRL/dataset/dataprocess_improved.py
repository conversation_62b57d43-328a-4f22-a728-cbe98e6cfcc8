import pandas as pd
import os
from typing import List, Dict, Optional, Union
import json
import requests
import argparse
import time
from tqdm import tqdm
import random
import re

def load_json_data(file_path: str) -> List[Dict]:
    """Load JSON file data"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def fix_output_format(output: str) -> str:
    """Fix issues with nested JSON and improper escaping in the output

    Args:
        output: The raw output string from DeepSeek

    Returns:
        Properly formatted output string
    """
    # Check if the output is a JSON string containing another JSON
    if output.startswith('{') and output.endswith('}'):
        try:
            # Try to parse as JSON
            parsed = json.loads(output)

            # Check if this is a nested structure with 'output' field
            if isinstance(parsed, dict) and 'output' in parsed:
                # Extract the actual output
                inner_output = parsed['output']

                # If the inner output is still a string that looks like JSON
                if isinstance(inner_output, str) and inner_output.startswith('{') and inner_output.endswith('}'):
                    try:
                        # Try to parse the inner JSON
                        inner_parsed = json.loads(inner_output)
                        if isinstance(inner_parsed, dict) and 'output' in inner_parsed:
                            # Extract the actual output from the inner JSON
                            actual_output = inner_parsed['output']

                            # Unescape special characters
                            actual_output = actual_output.replace('\\n', '\n')
                            actual_output = actual_output.replace('\\"', '"')
                            actual_output = actual_output.replace('\\\\', '\\')

                            return actual_output
                    except:
                        pass

                # If we couldn't parse the inner JSON, just unescape the inner output
                inner_output = inner_output.replace('\\n', '\n')
                inner_output = inner_output.replace('\\"', '"')
                inner_output = inner_output.replace('\\\\', '\\')

                return inner_output
        except:
            pass

    # Check if the output contains think/tool_call/obs/response tags
    if '<think>' in output or '<tool_call>' in output or '<obs>' in output or '<response>' in output:
        return output

    # If the output doesn't have the expected tags, try to extract them using regex
    think_match = re.search(r'<think>(.*?)</think>', output, re.DOTALL)
    tool_call_matches = re.findall(r'<tool_call>(.*?)</tool_call>', output, re.DOTALL)
    obs_matches = re.findall(r'<obs>(.*?)</obs>', output, re.DOTALL)
    response_match = re.search(r'<response>(.*?)</response>', output, re.DOTALL)

    # Also check for old format tags and convert them to new format
    search_matches = re.findall(r'<search>(.*?)</search>', output, re.DOTALL)
    search_response_matches = re.findall(r'<search_response>(.*?)</search_response>', output, re.DOTALL)

    if think_match or tool_call_matches or obs_matches or response_match or search_matches or search_response_matches:
        # Reconstruct the output with proper tags
        fixed_output = ""
        if think_match:
            fixed_output += f"<think>{think_match.group(1)}</think>\n"

        # Handle new format
        for i in range(min(len(tool_call_matches), len(obs_matches))):
            fixed_output += f"<tool_call>{tool_call_matches[i]}</tool_call>\n"
            fixed_output += f"<obs>{obs_matches[i]}</obs>\n"

        # Handle old format by converting to new format
        for i in range(min(len(search_matches), len(search_response_matches))):
            fixed_output += f"<tool_call>{search_matches[i]}</tool_call>\n"
            fixed_output += f"<obs>{search_response_matches[i]}</obs>\n"

        if response_match:
            fixed_output += f"<response>{response_match.group(1)}</response>"

        return fixed_output

    # If all else fails, return the original output
    return output

def get_hotpotqa_prompt(question, answer, context, supporting_facts=""):
    """Generate a prompt specifically for HotpotQA dataset"""
    return f"""Please analyze the following question and answer and generate output in the ToolRL format:
Question: {question}

Answer: {answer}

[The following context should ONLY be used to create realistic search results for your tool calls.
Extract relevant pieces from this context to simulate what a search engine might return.
DO NOT use this context directly in your thinking process - simulate what a model would do
if it didn't have this information and needed to decide whether to search.]
Context:
{context}

{supporting_facts}

You are a smart assistant capable of solving user tasks using your own reasoning or by calling an external search tool — **but only when necessary**.

Your task is to determine: **Can I answer the question using my own knowledge and reasoning, or do I need to search for information first?**

Tool Available:
- search (for retrieving real-world knowledge you cannot infer internally)

Steps for Each Turn
    1. Think: Recall relevant context and analyze the current user goal.
    2. Decide on Tool Usage: If a tool is needed, specify the tool and its parameters.
Output Format
    <think> Your thoughts and reasoning about whether you need to use tools </think>
    <tool_call>{{"name": "search", "parameters": {{"query": "your query here"}}}}</tool_call>(Omit this section if no tool is needed.)
    <obs> {{"name": "search", "results": "The output of the tool call"}} </obs>(Omit this section if no tool is needed.)
    <think> Conclude the information of search and what you have known.Final answer:... </think>(Omit this section if no tool is needed.)
    <response> AI's final response </response>

=== IMPORTANT GUIDELINES ===
1. Think first: Decide whether you need to search.
2. Use <tool_call> such as search only if you truly need external information.
3. Simulate realistic search responses using the provided context.
4. Ensure your final <response> matches the provided answer exactly.
5. Be concise, focused, and avoid hallucination.

=== Example: using search because you GENUINELY DON'T KNOW the answer, and needs search with strategy ===
<think> This question aks which magazine was started first. This requires specific historical information about when each magazine was founded. I don't have this information in my knowledge, so I'll need to search for it. </think>
<tool_call>{{"name": "search", "parameters": {{"query": "When was Arthur's Magazine founded?"}}}}</tool_call>
<obs> {{"name": "search", "results": "Arthur's Magazine was an American literary periodical published in Philadelphia from 1844 to 1846."}} </obs>
<think> Now I know Arthur's Magazine was founded in 1844. Let me search for First for Women. </think>
<tool_call>{{"name": "search", "parameters": {{"query": "When was First for Women magazine founded?"}}}}</tool_call>
<obs> {{"name": "search", "results": "First for Women is a woman's magazine published by Bauer Media Group in the USA. It was started in 1989."}} </obs>
<think> Now I have the information for both magazines. Arthur's Magazine was founded in 1844, while First for Women was founded in 1989. Therefore, Arthur's Magazine was started first. Final answer: Arthur's Magazine </think>
<response> Arthur's Magazine </response>
"""

def get_strategyqa_prompt(question, answer, context):
    """Generate a prompt specifically for StrategyQA with explicit search strategy reasoning"""
    return f"""Please analyze the following question and answer and generate output in the ToolRL format.

Question: {question}

Answer: {answer}

[The following context should ONLY be used to create realistic search results for your tool calls.
Extract relevant pieces from this context to simulate what a search engine might return.
DO NOT use this context directly in your thinking process - simulate what a model would do
if it didn't have this information and needed to decide whether to search.]
Context:
{context}

You are a smart assistant capable of solving user tasks using your own reasoning or by calling an external search tool — **but only when necessary**.

Available Tools
In your response, you can use the following tools when needed:
- search: Use this to simulate what an AI agent would search for when it lacks sufficient knowledge

Steps for Each Turn
    1. Think: Recall relevant context and analyze the current user goal.
    2. Decide on Tool Usage: If a tool is needed, specify the tool and its parameters.
    3. Respond Appropriately: If a response is needed, generate one while maintaining consistency across user queries.
Output Format
    <think> Your thoughts and reasoning about whether you need to use tools </think>
    <tool_call>{{"name": "search", "parameters": {{"query": "your query here"}}}}</tool_call>(Omit this section if no tool is needed.)
    <obs> {{"name": "search", "results": "The output of the tool call"}} </obs>(Omit this section if no tool is needed.)
    <think> Conclude the information of search and what you have known </think>(Omit this section if no tool is needed.)
    <response> Final answer based on your reasoning above. Only output "Yes" or "No", but ensure it reflects your thought process. </response>

=== IMPORTANT GUIDELINES ===
1.Try to use your knowledge and reasoning first. 
2. ONLY use tools if you GENUINELY cannot determine the answer through reasoning. 
3. Be HONEST about your knowledge - if you truly don't know a fact needed to answer the question, use a tool. 
4. If you have sufficient knowledge to reason through the answer, DO NOT use tools.
5. Be concise, focused, and avoid hallucination.

=== Example: you KNOW the answer, so you can give a confident answer without search ===
<think> This question asks if a moose can outrun a car.I know moose can run around 35 mph, while cars easily go over 60 mph. So a moose cannot outrun a car. </think>
<response> No </response>

=== Example: using search because you GENUINELY DON'T KNOW the answer, and needs search with strategy ===
<think> This question asks if the inventor of the telephone could communicate with the inventor of the radio. I know Alexander Graham Bell invented the telephone. I'm unsure who invented the radio and when they lived, so I need to search for that. </think>
<tool_call>{{"name": "search", "parameters": {{"query": "Who invented the radio and when did they live?"}}}}</tool_call>
<obs> {{"name": "search", "results": "Guglielmo Marconi invented the radio and lived from 1874 to 1937. Bell lived from 1847 to 1922."}} </obs>
<think> Marconi lived from 1874 to 1937, and Bell lived from 1847 to 1922. Their lifespans overlapped, so they could have communicated. </think>
<response> Yes </response>
"""
def get_commonsenseqa_prompt(question, choices, answer):
    """Generate a prompt specifically for CommonsenseQA dataset"""
    # 获取选项文本和标签
    choices_text = choices.get("text", [])
    choices_label = choices.get("label", [])
    
    # 格式化选项，确保每个选项都有对应的标签
    formatted_choices = []
    for label, text in zip(choices_label, choices_text):
        formatted_choices.append(f"{label}. {text}")
    choices_str = "\n".join(formatted_choices)

    return f"""Please analyze the following question and answer and generate output in the ToolRL format.
Question: {question}
Choices:
{choices_str}
[The following answer should ONLY be used to generate the correct answer. DO NOT use it in your thinking process.]
Answer: {answer}

You are a smart assistant capable of solving user tasks using your own reasoning.

Steps for Each Turn
    1. Think: Recall relevant context and analyze the current user goal.
    2. Decide on Tool Usage: If a tool is needed, specify the tool and its parameters.
Output Format
    <think> Your thoughts and reasoning. Final answer:... </think>
    <response> Final answer based on your reasoning above. Only output "A" or "B" or "C" or "D" or "E", but ensure it reflects your thought process. </response>

=== IMPORTANT GUIDELINES ===
1.Try to use your knowledge and reasoning first. 
2. ONLY use tools if you GENUINELY cannot determine the answer through reasoning. 
3. Be HONEST about your knowledge - if you truly don't know a fact needed to answer the question, use a tool. Format: 
    <think> Your thoughts and reasoning about whether you need to use tools </think>
    <tool_call>{{"name": "search", "parameters": {{"query": "your query here"}}}}</tool_call>(Omit this section if no tool is needed.)
    <obs> {{"name": "search", "results": "The output of the tool call"}} </obs>(Omit this section if no tool is needed.)
    <think> Conclude the information of search and what you have known.Final answer:... </think>(Omit this section if no tool is needed.)
4. If you have sufficient knowledge to reason through the answer, DO NOT use tools.
5. Be concise, focused, and avoid hallucination.

=== Example: you KNOW the answer, so you can give a confident answer without search ===
<think> The question states that sanctions were a punishing blow to the school. If something is punishing, it is harsh and does not recognize or support improvement. Therefore, it likely disregards the efforts made to change. Among the choices, A "ignore" best fits the idea of dismissing those efforts. Final answer: A </think>
<response> A </response>
"""

def process_with_deepseek(question: str,
                          context: Union[List, str, None],
                          answer: str,
                          api_key: str,
                          dataset_type: str = "hotpotqa",
                          supporting_facts: Optional[List] = None,
                          choices: Optional[List[str]] = None,
                          model: str = "deepseek-chat",
                          temperature: float = 0.3) -> Dict:
    """Process data using Deepseek API and generate labels

    Args:
        question: The question from the dataset
        context: The context (for HotpotQA: list of [title, sentences], for StrategyQA: string, for CommonsenseQA: None)
        answer: The answer from the dataset
        api_key: DeepSeek API key
        dataset_type: Type of dataset ("hotpotqa", "strategyqa", or "commonsenseqa")
        supporting_facts: Optional supporting facts for HotpotQA
        choices: Optional choices for CommonsenseQA
        model: Model to use
        temperature: Temperature for generation
    """
    # API endpoint
    url = "https://api.deepseek.com/v1/chat/completions"

    # Headers
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Format context for the prompt
    formatted_context = ""
    if dataset_type == "hotpotqa":
        for i, (title, sentences) in enumerate(context):
            formatted_context += f"Document {i+1}: {title}\n"
            for j, sentence in enumerate(sentences):
                formatted_context += f"  Sentence {j+1}: {sentence}\n"
    elif dataset_type == "strategyqa":
        formatted_context = str(context)  # For StrategyQA, context might be different

    # Format supporting facts if provided
    supporting_facts_text = ""
    if supporting_facts:
        supporting_facts_text = "Supporting facts:\n"
        for fact in supporting_facts:
            if isinstance(fact, list) and len(fact) == 2:
                title, sentence = fact
                supporting_facts_text += f"- {title}: {sentence}\n"

    # Choose the appropriate prompt based on dataset type
    if dataset_type == "hotpotqa":
        prompt = get_hotpotqa_prompt(question, answer, formatted_context, supporting_facts_text)
    elif dataset_type == "strategyqa":
        prompt = get_strategyqa_prompt(question, answer, formatted_context)
    else:  # commonsenseqa
        prompt = get_commonsenseqa_prompt(question, choices, answer)

    # Prepare the request payload
    payload = {
        "model": model,  # Use the model specified in the parameters
        "messages": [
            {"role": "system", "content": "You are a helpful AI assistant that generates high-quality training data for teaching smaller models when to use tools. Focus on demonstrating clear decision-making about when to search vs. when to answer directly."},
            {"role": "user", "content": prompt}
        ],
        "temperature": temperature  # Use the temperature specified in the parameters
    }

    # Initialize variables
    content = ""
    result = {}
    verification_match = False
    model_response = ""
    search_count = 0
    has_think = False

    # Add retry mechanism with exponential backoff
    max_retries = 3
    base_delay = 5  # seconds
    max_delay = 60  # maximum delay in seconds

    try:
        for retry in range(max_retries):
            try:
                # Make the API call with timeout
                response = requests.post(url, headers=headers, json=payload, timeout=30)
                response.raise_for_status()  # Raise an exception for bad status codes

                # Parse the response
                result = response.json()
                content = result['choices'][0]['message']['content']

                # If successful, break the retry loop
                break

            except requests.exceptions.Timeout:
                print(f"Request timed out (attempt {retry+1}/{max_retries}). Retrying...")
                if retry < max_retries - 1:
                    delay = min(base_delay * (2 ** retry), max_delay)
                    time.sleep(delay)
                else:
                    raise Exception("Maximum retries exceeded for timeout")
            except requests.exceptions.ConnectionError:
                print(f"Connection error (attempt {retry+1}/{max_retries}). Retrying...")
                if retry < max_retries - 1:
                    delay = min(base_delay * (2 ** retry), max_delay)
                    time.sleep(delay)
                else:
                    raise Exception("Maximum retries exceeded for connection error")
            except requests.exceptions.RequestException as e:
                print(f"Request failed (attempt {retry+1}/{max_retries}): {str(e)}. Retrying...")
                if retry < max_retries - 1:
                    delay = min(base_delay * (2 ** retry), max_delay)
                    time.sleep(delay)
                else:
                    raise Exception(f"Maximum retries exceeded: {str(e)}")
            except Exception as e:
                print(f"Unexpected error (attempt {retry+1}/{max_retries}): {str(e)}. Retrying...")
                if retry < max_retries - 1:
                    delay = min(base_delay * (2 ** retry), max_delay)
                    time.sleep(delay)
                else:
                    raise Exception(f"Maximum retries exceeded: {str(e)}")

        # Fix any formatting issues in the content
        content = fix_output_format(content)

        # Extract model's response to check if it matches the expected answer
        if "<response>" in content and "</response>" in content:
            model_response = content.split("<response>")[1].split("</response>")[0].strip()

        # Count the number of tool calls (only count tool_call tags)
        search_count = content.count("<tool_call>")

        # Check if the response contains think tag
        has_think = "<think>" in content
        if not has_think:
            print(f"WARNING: Response for question '{question}' does not contain <think> tag. This may affect training quality.")

        # Print tool call count for debugging
        if search_count > 0:
            print(f"INFO: Response for question '{question}' contains {search_count} tool call(s).")

        # Verification logic for comparing answers
        if dataset_type == "strategyqa":
            # For StrategyQA, we expect exact "Yes" or "No" answers
            # Normalize both expected and actual responses
            norm_expected = answer.lower().strip()
            norm_response = model_response.lower().strip()

            # Check for exact match (yes/no)
            verification_match = (
                (norm_expected == "yes" and (norm_response == "yes" or norm_response == "true")) or
                (norm_expected == "no" and (norm_response == "no" or norm_response == "false"))
            )

            # If response is not a simple yes/no, try to extract it
            if not verification_match and len(norm_response) > 10:  # If response is longer than expected
                if "yes" in norm_response[:20] or "true" in norm_response[:20]:
                    verification_match = (norm_expected == "yes")
                elif "no" in norm_response[:20] or "false" in norm_response[:20]:
                    verification_match = (norm_expected == "no")
        elif dataset_type == "commonsenseqa":
            # For CommonsenseQA, we expect A/B/C/D/E answers
            norm_expected = answer.upper().strip()
            norm_response = model_response.upper().strip()
            
            # Check for exact match of letter choice
            verification_match = norm_expected == norm_response
            
            # If response contains explanation, try to extract just the letter
            if not verification_match and len(norm_response) > 1:
                # Try to find a single letter A/B/C/D/E
                import re
                letter_match = re.search(r'\b[A-E]\b', norm_response)
                if letter_match:
                    verification_match = norm_expected == letter_match.group(0)
        else:
            # For other datasets like HotpotQA, use more sophisticated verification
            def normalize_for_comparison(text):
                # Convert to lowercase
                text = text.lower().strip()
                # Remove punctuation except for numbers and units
                for char in ".,;:!?()[]{}\"'":
                    text = text.replace(char, "")
                # Remove extra spaces
                text = " ".join(text.split())
                return text

            # Extract key information (numbers and units) from both answers
            import re

            # Function to extract numbers and units
            def extract_numbers_and_units(text):
                # Find all numbers with potential units
                matches = re.findall(r'(\d+(?:\.\d+)?(?:\s*[a-zA-Z]+)?)', text.lower())
                return matches

            # Get normalized versions for basic comparison
            norm_expected = normalize_for_comparison(answer)
            norm_response = normalize_for_comparison(model_response)

            # Extract key information
            expected_key_info = extract_numbers_and_units(answer)
            response_key_info = extract_numbers_and_units(model_response)

            # Check if the response contains the expected answer (simple check)
            basic_match = norm_expected in norm_response

            # Check if all key information from expected answer is in the response
            key_info_match = all(any(expected_item in response_item or response_item in expected_item
                                for response_item in response_key_info)
                            for expected_item in expected_key_info) if expected_key_info and response_key_info else False

            # Combine checks - consider it a match if either basic match or key info match is true
            verification_match = basic_match or key_info_match
        if dataset_type == "commonsenseqa":
            # 获取选项文本和标签
            choices_text = choices.get("text", [])
            choices_label = choices.get("label", [])
            
            # 格式化选项，确保每个选项都有对应的标签
            formatted_choices = []
            for label, text in zip(choices_label, choices_text):
                formatted_choices.append(f"{label}. {text}")
            choices_str = "\n".join(formatted_choices)
            return {
            "instruction": f"You are a helpful multi-turn assistant that can use tools to solve tasks and format responses clearly.\n\n**Available Tools**\nIn your response, you can use the following tools:\n Name: search\nDescription: Returns the search response for a specified query.\nParameters: {{\"query\": {{\"description\": \"The user query or question to search for\", \"type\": \"string\"}}}}\n**Response Structure**\nUse this format:\n\n<think> Your thoughts and reasoning </think>\n<tool_call>\n{{\"name\": \"Tool name\", \"parameters\": {{\"Parameter name\": \"Parameter content\", \"... ...\": \"... ...\"}}}}\n</tool_call>\n<obs>\n{{\"name\": \"Tool name\", \"results\": \"Tool results\"}}\n</obs>\n\"... ...\"\n<response> Final response to user </response>\n**Rules**1. Always include `<think>` and `<response>`2. Use `<tool_call>` only if needed, followed by matching `<obs>`3. If there is a `<tool_call>`, it must be followed by a single `<obs>` with the same `name` value.",
            "input": f"{question}\nChoices: {choices_str}",
            "output": content,
            "_meta": {
                "expected_answer": answer,
                "model_response": model_response,
                "verification_result": verification_match,
                "search_count": search_count,
                "has_think": has_think,
                "dataset_type": dataset_type,
                "format_fixed": content != result['choices'][0]['message']['content']  # Track if format was fixed
            }
        }
        else:
            return {
            "instruction": f"You are a helpful multi-turn assistant that can use tools to solve tasks and format responses clearly.\n\n**Available Tools**\nIn your response, you can use the following tools:\n Name: search\nDescription: Returns the search response for a specified query.\nParameters: {{\"query\": {{\"description\": \"The user query or question to search for\", \"type\": \"string\"}}}}\n**Response Structure**\nUse this format:\n\n<think> Your thoughts and reasoning </think>\n<tool_call>\n{{\"name\": \"Tool name\", \"parameters\": {{\"Parameter name\": \"Parameter content\", \"... ...\": \"... ...\"}}}}\n</tool_call>\n<obs>\n{{\"name\": \"Tool name\", \"results\": \"Tool results\"}}\n</obs>\n\"... ...\"\n<response> Final response to user </response>\n**Rules**1. Always include `<think>` and `<response>`2. Use `<tool_call>` only if needed, followed by matching `<obs>`3. If there is a `<tool_call>`, it must be followed by a single `<obs>` with the same `name` value.",
            "input": f"{question}",
            "output": content,
            "_meta": {
                "expected_answer": answer,
                "model_response": model_response,
                "verification_result": verification_match,
                "search_count": search_count,
                "has_think": has_think,
                "dataset_type": dataset_type,
                "format_fixed": content != result['choices'][0]['message']['content']  # Track if format was fixed
            }
        }
    except Exception as e:
        print(f"Error calling API: {str(e)}")
        # Return default values if API call fails
        default_output = f"<think>Processing question: {question}</think>\n<response>{answer}</response>"

        return {
            "instruction": f"You are a helpful multi-turn assistant that can use tools to solve tasks and format responses clearly.\n\n**Available Tools**\nIn your response, you can use the following tools:\n Name: search\nDescription: Returns the search response for a specified query.\nParameters: {{\"query\": {{\"description\": \"The user query or question to search for\", \"type\": \"string\"}}}}\n**Response Structure**\nUse this format:\n\n<think> Your thoughts and reasoning </think>\n<tool_call>\n{{\"name\": \"Tool name\", \"parameters\": {{\"Parameter name\": \"Parameter content\", \"... ...\": \"... ...\"}}}}\n</tool_call>\n<obs>\n{{\"name\": \"Tool name\", \"results\": \"Tool results\"}}\n</obs>\n\"... ...\"\n<response> Final response to user </response>\n**Rules**1. Always include `<think>` and `<response>`2. Use `<tool_call>` only if needed, followed by matching `<obs>`3. If there is a `<tool_call>`, it must be followed by a single `<obs>` with the same `name` value.",
            "input": f"{question}",
            "output": default_output,
            "_meta": {
                "expected_answer": answer,
                "model_response": answer,
                "verification_result": True,  # Default to True for fallback responses
                "search_count": 0,  # Default output has no tool calls
                "has_think": True,  # Default output includes think tag
                "dataset_type": dataset_type,
                "format_fixed": False,  # No format fixing needed for default output
                "error": str(e)
            }
        }

def save_checkpoint(output_dir: str, dataset_type: str, processed_records: List[Dict], 
                   metadata: List[Dict], current_idx: int, batch_size: int):
    """Save checkpoint data to resume processing later"""
    checkpoint_dir = os.path.join(output_dir, "checkpoints")
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    checkpoint_file = os.path.join(checkpoint_dir, f"{dataset_type}_checkpoint.json")
    checkpoint_data = {
        "processed_records": processed_records,
        "metadata": metadata,
        "current_idx": current_idx,
        "batch_size": batch_size,
        "timestamp": time.time()
    }
    
    # Save checkpoint
    with open(checkpoint_file, 'w', encoding='utf-8') as f:
        json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
    
    # Also save the main files
    records_file = os.path.join(output_dir, f"{dataset_type}_processed_records.json")
    metadata_file = os.path.join(output_dir, f"{dataset_type}_metadata.json")
    
    with open(records_file, 'w', encoding='utf-8') as f:
        json.dump(processed_records, f, ensure_ascii=False, indent=2)
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)

def load_checkpoint(output_dir: str, dataset_type: str) -> Optional[Dict]:
    """Load checkpoint data to resume processing"""
    checkpoint_file = os.path.join(output_dir, "checkpoints", f"{dataset_type}_checkpoint.json")
    
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            return checkpoint_data
        except Exception as e:
            print(f"Error loading checkpoint: {str(e)}")
            return None
    return None

def process_hotpotqa_data(data: List[Dict], api_key: str, limit: int = 10,
                         start_idx: int = 0, batch_size: int = 10,
                         output_dir: str = "./processed",
                         model: str = "deepseek-chat",
                         temperature: float = 0.3,
                         random_sample: bool = True,
                         random_seed: int = 42) -> Dict:
    """Process HotpotQA data and return processed records with verification statistics

    Args:
        data: List of HotpotQA data items
        api_key: DeepSeek API key
        limit: Maximum number of items to process (default: 10)
        start_idx: Starting index for processing (for resuming)
        batch_size: Number of items to process in each batch
        output_dir: Directory to save intermediate results
        model: DeepSeek model to use
        temperature: Temperature for generation
        random_sample: Whether to randomly sample data (default: True)
        random_seed: Random seed for reproducible sampling (default: 42)
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    processed_records = []
    metadata = []
    verification_stats = {
        "total": 0,
        "matched": 0,
        "mismatched": 0,
        "tool_usage": 0,
        "total_searchs": 0,
        "avg_searchs": 0.0
    }

    # Try to load checkpoint first
    checkpoint_data = load_checkpoint(output_dir, "hotpotqa")
    if checkpoint_data and start_idx == 0:  # Only use checkpoint if no specific start_idx is provided
        processed_records = checkpoint_data["processed_records"]
        metadata = checkpoint_data["metadata"]
        start_idx = checkpoint_data["current_idx"]
        batch_size = checkpoint_data["batch_size"]
        print(f"Loaded checkpoint: {len(processed_records)} records processed, resuming from index {start_idx}")

    # If random sampling is enabled, create a random index list with fixed seed
    if random_sample:
        # Set random seed for reproducibility
        random.seed(random_seed)
        indices = list(range(len(data)))
        random.shuffle(indices)
        indices = indices[start_idx:start_idx + limit]
        print(f"Using random sampling with seed {random_seed}")
    else:
        indices = list(range(start_idx, min(start_idx + limit, len(data))))
        print("Using sequential processing")

    # Process each item in batches
    try:
        for batch_start in range(0, len(indices), batch_size):
            batch_end = min(batch_start + batch_size, len(indices))
            batch_indices = indices[batch_start:batch_end]
            print(f"\nProcessing batch {batch_start}-{batch_end-1} of {len(indices)} items...")

            batch_records = []
            batch_metadata = []

            for idx in tqdm(batch_indices, desc="Processing items", leave=False):
                try:
                    # Get question, context, and answer from the HotpotQA data
                    item = data[idx]
                    question = item['question']
                    context = item['context']
                    answer = item['answer']

                    # Extract supporting facts if available
                    supporting_facts = item.get('supporting_facts', [])

                    # Process text using Deepseek API
                    result = process_with_deepseek(
                        question=question,
                        context=context,
                        answer=answer,
                        api_key=api_key,
                        dataset_type="hotpotqa",
                        supporting_facts=supporting_facts,
                        model=model,
                        temperature=temperature
                    )

                    # Extract metadata
                    meta = result.get("_meta", {})

                    # Update verification statistics
                    verification_stats["total"] += 1
                    if meta.get("verification_result", False):
                        verification_stats["matched"] += 1
                    else:
                        verification_stats["mismatched"] += 1

                    # Count tool usage and tool calls
                    search_count = meta.get("search_count", 0)
                    if search_count > 0:
                        verification_stats["tool_usage"] += 1
                        verification_stats["total_searchs"] += search_count

                    # Add the record to processed records (without metadata)
                    clean_record = {
                        "instruction": result.get("instruction", "You are a helpful dialogue assistant capable of leveraging tool calls to solve user tasks and provide structured chat responses."),
                        "input": result.get("input", f"{question}"),
                        "output": result.get("output", f"<response>{answer}</response>")
                    }
                    batch_records.append(clean_record)

                    # Save metadata separately
                    meta_record = {
                        "index": idx,
                        "question": question,
                        "expected_answer": answer,
                        "model_response": meta.get("model_response", ""),
                        "verification_result": meta.get("verification_result", False),
                        "search_count": meta.get("search_count", 0),
                        "has_think": meta.get("has_think", False),
                        "format_fixed": meta.get("format_fixed", False),
                        "dataset_type": "hotpotqa"
                    }
                    batch_metadata.append(meta_record)

                    # Print progress with verification result
                    verification_status = "✓" if meta.get("verification_result", False) else "✗"
                    tool_count = meta.get("search_count", 0)
                    tool_usage = f"🔧x{tool_count}" if tool_count > 0 else "📝"
                    print(f"\nProcessed {len(processed_records) + len(batch_records)}/{len(indices)} items [{verification_status}] [{tool_usage}]")

                except Exception as e:
                    print(f"\nError processing item {idx}: {str(e)}")
                    # Save checkpoint before raising the exception
                    save_checkpoint(output_dir, "hotpotqa", processed_records, metadata, idx, batch_size)
                    raise

            print()  # New line after batch completion

            # Add batch results to overall results
            processed_records.extend(batch_records)
            metadata.extend(batch_metadata)

            # Save checkpoint after each batch
            save_checkpoint(output_dir, "hotpotqa", processed_records, metadata, batch_end, batch_size)

            print(f"Saved checkpoint after batch {batch_start}-{batch_end-1}.")

    except Exception as e:
        print(f"Error during processing: {str(e)}")
        # Save checkpoint before exiting
        save_checkpoint(output_dir, "hotpotqa", processed_records, metadata, batch_start, batch_size)
        raise

    # Calculate accuracy percentage and tool usage statistics
    if verification_stats["total"] > 0:
        verification_stats["accuracy"] = (verification_stats["matched"] / verification_stats["total"]) * 100
        verification_stats["tool_usage_percentage"] = (verification_stats["tool_usage"] / verification_stats["total"]) * 100
        verification_stats["avg_searchs"] = verification_stats["total_searchs"] / verification_stats["total"]
        if verification_stats["tool_usage"] > 0:
            verification_stats["avg_searchs_when_used"] = verification_stats["total_searchs"] / verification_stats["tool_usage"]
        else:
            verification_stats["avg_searchs_when_used"] = 0
    else:
        verification_stats["accuracy"] = 0
        verification_stats["tool_usage_percentage"] = 0
        verification_stats["avg_searchs"] = 0
        verification_stats["avg_searchs_when_used"] = 0

    return {
        "records": processed_records,
        "metadata": metadata,
        "stats": verification_stats
    }

def recalculate_stats_from_metadata(metadata: List[Dict]) -> Dict:
    """从元数据重新计算统计信息"""
    stats = {
        "total": len(metadata),
        "matched": sum(1 for item in metadata if item.get("verification_result", False)),
        "mismatched": sum(1 for item in metadata if not item.get("verification_result", False)),
        "tool_usage": sum(1 for item in metadata if item.get("search_count", 0) > 0),
        "total_searchs": sum(item.get("search_count", 0) for item in metadata),
        "avg_searchs": 0.0,
        "avg_searchs_when_used": 0.0,
        "accuracy": 0.0,
        "tool_usage_percentage": 0.0
    }
    
    # 计算百分比和平均值
    if stats["total"] > 0:
        stats["accuracy"] = (stats["matched"] / stats["total"]) * 100
        stats["tool_usage_percentage"] = (stats["tool_usage"] / stats["total"]) * 100
        stats["avg_searchs"] = stats["total_searchs"] / stats["total"]
        if stats["tool_usage"] > 0:
            stats["avg_searchs_when_used"] = stats["total_searchs"] / stats["tool_usage"]
    
    return stats

def process_strategyqa_data(data: List[Dict], api_key: str, limit: int = 10,
                           start_idx: int = 0, batch_size: int = 10,
                           output_dir: str = "./processed",
                           model: str = "deepseek-chat",
                           temperature: float = 0.3,
                           random_sample: bool = True,
                           random_seed: int = 42) -> Dict:
    """Process StrategyQA data and return processed records with verification statistics"""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    processed_records = []
    metadata = []
    verification_stats = {
        "total": 0,
        "matched": 0,
        "mismatched": 0,
        "tool_usage": 0,
        "total_searchs": 0,
        "avg_searchs": 0.0
    }

    # Try to load checkpoint first
    checkpoint_data = load_checkpoint(output_dir, "strategyqa")
    if checkpoint_data and start_idx == 0:  # Only use checkpoint if no specific start_idx is provided
        processed_records = checkpoint_data["processed_records"]
        metadata = checkpoint_data["metadata"]
        start_idx = checkpoint_data["current_idx"]
        batch_size = checkpoint_data["batch_size"]
        print(f"Loaded checkpoint: {len(processed_records)} records processed, resuming from index {start_idx}")

    # If random sampling is enabled, create a random index list with fixed seed
    if random_sample:
        # Set random seed for reproducibility
        random.seed(random_seed)
        indices = list(range(len(data)))
        random.shuffle(indices)
        indices = indices[start_idx:start_idx + limit]
        print(f"Using random sampling with seed {random_seed}")
    else:
        indices = list(range(start_idx, min(start_idx + limit, len(data))))
        print("Using sequential processing")

    # Process each item in batches
    try:
        for batch_start in range(0, len(indices), batch_size):
            batch_end = min(batch_start + batch_size, len(indices))
            batch_indices = indices[batch_start:batch_end]
            print(f"Processing batch {batch_start}-{batch_end-1}...")

            batch_records = []
            batch_metadata = []

            for idx in tqdm(batch_indices, desc="Processing items", leave=False):
                try:
                    # Get question and answer from the StrategyQA data
                    item = data[idx]
                    question = item['question']

                    # StrategyQA answers are boolean (true/false)
                    answer_bool = item.get('answer', False)
                    answer = "Yes" if answer_bool else "No"

                    # Get term, description, facts and decomposition if available (for context)
                    term = item.get('term', '')
                    description = item.get('description', '')
                    facts = item.get('facts', [])
                    decomposition = item.get('decomposition', [])

                    # Combine term, description, facts and decomposition for context
                    context_parts = []
                    if term:
                        context_parts.append(f"Term: {term}")
                    if description:
                        context_parts.append(f"Description: {description}")
                    if facts:
                        context_parts.append("\nFacts:")
                        for i, fact in enumerate(facts):
                            context_parts.append(f"  Fact {i+1}: {fact}")

                    if decomposition:
                        context_parts.append("\nDecomposition:")
                        for i, step in enumerate(decomposition):
                            context_parts.append(f"  Step {i+1}: {step}")

                    context = "\n".join(context_parts)

                    # Process text using Deepseek API
                    result = process_with_deepseek(
                        question=question,
                        context=context,
                        answer=answer,
                        api_key=api_key,
                        dataset_type="strategyqa",
                        model=model,
                        temperature=temperature
                    )

                    # Extract metadata
                    meta = result.get("_meta", {})

                    # Add the record to processed records (without metadata)
                    clean_record = {
                        "instruction": result.get("instruction", "You are a helpful dialogue assistant capable of leveraging tool calls to solve user tasks and provide structured chat responses."),
                        "input": result.get("input", f"{question}"),
                        "output": result.get("output", f"<response>{answer}</response>")
                    }
                    batch_records.append(clean_record)

                    # Save metadata separately
                    meta_record = {
                        "index": idx,
                        "question": question,
                        "expected_answer": answer,
                        "model_response": meta.get("model_response", ""),
                        "verification_result": meta.get("verification_result", False),
                        "search_count": meta.get("search_count", 0),
                        "has_think": meta.get("has_think", False),
                        "format_fixed": meta.get("format_fixed", False),
                        "dataset_type": "strategyqa"
                    }
                    batch_metadata.append(meta_record)

                    # Print progress with verification result
                    verification_status = "✓" if meta.get("verification_result", False) else "✗"
                    tool_count = meta.get("search_count", 0)
                    tool_usage = f"🔧x{tool_count}" if tool_count > 0 else "📝"
                    print(f"Processed {idx+1}/{len(indices)} items [{verification_status}] [{tool_usage}]")

                except Exception as e:
                    print(f"Error processing item {idx}: {str(e)}")
                    save_checkpoint(output_dir, "strategyqa", processed_records, metadata, idx, batch_size)
                    raise

            # Add batch results to overall results
            processed_records.extend(batch_records)
            metadata.extend(batch_metadata)

            # Save checkpoint after each batch
            save_checkpoint(output_dir, "strategyqa", processed_records, metadata, batch_end, batch_size)
            print(f"Saved checkpoint after batch {batch_start}-{batch_end-1}.")

    except Exception as e:
        print(f"Error during processing: {str(e)}")
        save_checkpoint(output_dir, "strategyqa", processed_records, metadata, batch_start, batch_size)
        raise

    # 最终重新计算统计信息
    verification_stats = recalculate_stats_from_metadata(metadata)

    # 验证统计信息的准确性
    assert verification_stats["total"] == len(metadata), "Total count mismatch"
    assert verification_stats["matched"] + verification_stats["mismatched"] == verification_stats["total"], "Match count mismatch"
    assert verification_stats["tool_usage"] <= verification_stats["total"], "Tool usage count exceeds total"

    return {
        "records": processed_records,
        "metadata": metadata,
        "stats": verification_stats
    }

def create_mixed_dataset(hotpotqa_records: List[Dict], strategyqa_records: List[Dict],
                        output_file: str, ratio: float = 0.5, balance_tool_usage: bool = True):
    """Create a mixed dataset from HotpotQA and StrategyQA records

    Args:
        hotpotqa_records: List of processed HotpotQA records
        strategyqa_records: List of processed StrategyQA records
        output_file: Path to save the mixed dataset
        ratio: Ratio of HotpotQA to StrategyQA records (default: 0.5, equal mix)
        balance_tool_usage: Whether to balance tool usage in the dataset
    """
    # If balance_tool_usage is True, try to balance the number of records with and without tool usage
    if balance_tool_usage:
        # Separate records with and without tool usage
        hotpotqa_with_tools = []
        hotpotqa_without_tools = []
        strategyqa_with_tools = []
        strategyqa_without_tools = []

        # Process HotpotQA records
        for record in hotpotqa_records:
            if "<tool_call>" in record["output"]:
                hotpotqa_with_tools.append(record)
            else:
                hotpotqa_without_tools.append(record)

        # Process StrategyQA records
        for record in strategyqa_records:
            if "<tool_call>" in record["output"]:
                strategyqa_with_tools.append(record)
            else:
                strategyqa_without_tools.append(record)

        print(f"HotpotQA: {len(hotpotqa_with_tools)} with tools, {len(hotpotqa_without_tools)} without tools")
        print(f"StrategyQA: {len(strategyqa_with_tools)} with tools, {len(strategyqa_without_tools)} without tools")

        # Calculate the number of records to include from each category
        total_records = len(hotpotqa_records) + len(strategyqa_records)
        target_hotpotqa = int(total_records * ratio)
        target_strategyqa = total_records - target_hotpotqa

        # Try to have 50% of records with tool usage and 50% without
        target_hotpotqa_with_tools = min(len(hotpotqa_with_tools), target_hotpotqa // 2)
        target_hotpotqa_without_tools = min(len(hotpotqa_without_tools), target_hotpotqa - target_hotpotqa_with_tools)

        target_strategyqa_with_tools = min(len(strategyqa_with_tools), target_strategyqa // 2)
        target_strategyqa_without_tools = min(len(strategyqa_without_tools), target_strategyqa - target_strategyqa_with_tools)

        # Adjust if we couldn't get enough records in any category
        if target_hotpotqa_with_tools + target_hotpotqa_without_tools < target_hotpotqa:
            if len(hotpotqa_with_tools) > target_hotpotqa_with_tools:
                target_hotpotqa_with_tools = min(len(hotpotqa_with_tools), target_hotpotqa - target_hotpotqa_without_tools)
            elif len(hotpotqa_without_tools) > target_hotpotqa_without_tools:
                target_hotpotqa_without_tools = min(len(hotpotqa_without_tools), target_hotpotqa - target_hotpotqa_with_tools)

        if target_strategyqa_with_tools + target_strategyqa_without_tools < target_strategyqa:
            if len(strategyqa_with_tools) > target_strategyqa_with_tools:
                target_strategyqa_with_tools = min(len(strategyqa_with_tools), target_strategyqa - target_strategyqa_without_tools)
            elif len(strategyqa_without_tools) > target_strategyqa_without_tools:
                target_strategyqa_without_tools = min(len(strategyqa_without_tools), target_strategyqa - target_strategyqa_with_tools)

        # Sample records from each category
        sampled_hotpotqa_with_tools = random.sample(hotpotqa_with_tools, target_hotpotqa_with_tools)
        sampled_hotpotqa_without_tools = random.sample(hotpotqa_without_tools, target_hotpotqa_without_tools)

        sampled_strategyqa_with_tools = random.sample(strategyqa_with_tools, target_strategyqa_with_tools)
        sampled_strategyqa_without_tools = random.sample(strategyqa_without_tools, target_strategyqa_without_tools)

        # Combine all sampled records
        mixed_records = sampled_hotpotqa_with_tools + sampled_hotpotqa_without_tools + sampled_strategyqa_with_tools + sampled_strategyqa_without_tools
    else:
        # Simple mixing based on ratio
        target_hotpotqa = int((len(hotpotqa_records) + len(strategyqa_records)) * ratio)
        target_strategyqa = (len(hotpotqa_records) + len(strategyqa_records)) - target_hotpotqa

        sampled_hotpotqa = random.sample(hotpotqa_records, min(len(hotpotqa_records), target_hotpotqa))
        sampled_strategyqa = random.sample(strategyqa_records, min(len(strategyqa_records), target_strategyqa))

        mixed_records = sampled_hotpotqa + sampled_strategyqa

    # Shuffle the mixed records
    random.shuffle(mixed_records)

    # Save the mixed dataset
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(mixed_records, f, ensure_ascii=False, indent=2)

    print(f"Created mixed dataset with {len(mixed_records)} records and saved to {output_file}")

    # Print statistics
    tool_usage_count = sum(1 for record in mixed_records if "<tool_call>" in record["output"])
    print(f"Tool usage: {tool_usage_count} records ({tool_usage_count/len(mixed_records)*100:.2f}%)")

    hotpotqa_count = sum(1 for record in mixed_records if record.get("_meta", {}).get("dataset_type") == "hotpotqa")
    strategyqa_count = sum(1 for record in mixed_records if record.get("_meta", {}).get("dataset_type") == "strategyqa")
    print(f"Dataset composition: {hotpotqa_count} HotpotQA ({hotpotqa_count/len(mixed_records)*100:.2f}%), {strategyqa_count} StrategyQA ({strategyqa_count/len(mixed_records)*100:.2f}%)")

def process_commonsenseqa_data(data: List[Dict], api_key: str, limit: int = 10,
                              start_idx: int = 0, batch_size: int = 10,
                              output_dir: str = "./processed",
                              model: str = "deepseek-chat",
                              temperature: float = 0.3,
                              random_sample: bool = True,
                              random_seed: int = 42) -> Dict:
    """Process CommonsenseQA data and return processed records with verification statistics"""
    os.makedirs(output_dir, exist_ok=True)

    processed_records = []
    metadata = []
    verification_stats = {
        "total": 0,
        "matched": 0,
        "mismatched": 0,
        "tool_usage": 0,
        "total_searchs": 0,
        "avg_searchs": 0.0
    }

    # Try to load checkpoint first
    checkpoint_data = load_checkpoint(output_dir, "commonsenseqa")
    if checkpoint_data and start_idx == 0:
        processed_records = checkpoint_data["processed_records"]
        metadata = checkpoint_data["metadata"]
        start_idx = checkpoint_data["current_idx"]
        batch_size = checkpoint_data["batch_size"]
        print(f"Loaded checkpoint: {len(processed_records)} records processed, resuming from index {start_idx}")

    # If random sampling is enabled, create a random index list with fixed seed
    if random_sample:
        random.seed(random_seed)
        indices = list(range(len(data)))
        random.shuffle(indices)
        indices = indices[start_idx:start_idx + limit]
        print(f"Using random sampling with seed {random_seed}")
    else:
        indices = list(range(start_idx, min(start_idx + limit, len(data))))
        print("Using sequential processing")

    try:
        for batch_start in range(0, len(indices), batch_size):
            batch_end = min(batch_start + batch_size, len(indices))
            batch_indices = indices[batch_start:batch_end]
            print(f"Processing batch {batch_start}-{batch_end-1}...")

            batch_records = []
            batch_metadata = []

            for idx in tqdm(batch_indices, desc="Processing items", leave=False):
                try:
                    item = data[idx]
                    question = item.get("question", "")
                    choices = {
                        "text": item.get("choices", {}).get("text", []),
                        "label": item.get("choices", {}).get("label", ["A", "B", "C", "D", "E"])
                    }
                    answer = item.get("answerKey", "")

                    # Process with DeepSeek
                    result = process_with_deepseek(
                        question=question,
                        context=None,  # CommonsenseQA doesn't have context
                        answer=answer,
                        choices=choices,  # 传递完整的choices结构
                        api_key=api_key,
                        dataset_type="commonsenseqa",
                        model=model,
                        temperature=temperature
                    )

                    # Extract metadata
                    meta = result.get("_meta", {})

                    # Add the record to processed records
                    clean_record = {
                        "instruction": result.get("instruction", "You are a helpful assistant for commonsense QA."),
                        "input": result.get("input", f"{question}"),
                        "output": result.get("output", f"<response>{answer}</response>")
                    }
                    batch_records.append(clean_record)

                    # Save metadata separately
                    meta_record = {
                        "index": idx,
                        "question": question,
                        "choices": choices,  # 保存完整的choices信息到metadata
                        "expected_answer": answer,
                        "model_response": meta.get("model_response", ""),
                        "verification_result": meta.get("verification_result", False),
                        "search_count": meta.get("search_count", 0),
                        "has_think": meta.get("has_think", False),
                        "format_fixed": meta.get("format_fixed", False),
                        "dataset_type": "commonsenseqa"
                    }
                    batch_metadata.append(meta_record)

                    # Print progress with verification result
                    verification_status = "✓" if meta.get("verification_result", False) else "✗"
                    tool_count = meta.get("search_count", 0)
                    tool_usage = f"🔧x{tool_count}" if tool_count > 0 else "📝"
                    print(f"Processed {idx+1}/{len(indices)} items [{verification_status}] [{tool_usage}]")

                except Exception as e:
                    print(f"Error processing item {idx}: {str(e)}")
                    save_checkpoint(output_dir, "commonsenseqa", processed_records, metadata, idx, batch_size)
                    raise

            processed_records.extend(batch_records)
            metadata.extend(batch_metadata)
            save_checkpoint(output_dir, "commonsenseqa", processed_records, metadata, batch_end, batch_size)
            print(f"Saved checkpoint after batch {batch_start}-{batch_end-1}.")

    except Exception as e:
        print(f"Error during processing: {str(e)}")
        save_checkpoint(output_dir, "commonsenseqa", processed_records, metadata, batch_start, batch_size)
        raise

    # 重新计算统计信息
    verification_stats = recalculate_stats_from_metadata(metadata)

    # 验证统计信息的准确性
    assert verification_stats["total"] == len(metadata), "Total count mismatch"
    assert verification_stats["matched"] + verification_stats["mismatched"] == verification_stats["total"], "Match count mismatch"
    assert verification_stats["tool_usage"] <= verification_stats["total"], "Tool usage count exceeds total"

    return {
        "records": processed_records,
        "metadata": metadata,
        "stats": verification_stats
    }

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Process HotpotQA and StrategyQA CommonsenseQA datasets for ToolRL")
    parser.add_argument("--hotpotqa_file", type=str, default="/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/hotpotqa/hotpot_train_v1.1.json", help="Path to HotpotQA dataset file")
    parser.add_argument("--strategyqa_file", type=str, default="", help="Path to StrategyQA dataset file")
    parser.add_argument("--commonsenseqa_file", type=str, default="/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/commonsense/commonsenseqa_train.jsonl", help="Path to CommonsenseQA dataset file")
    parser.add_argument("--output_dir", type=str, default="/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train", help="Directory to save processed data")
    parser.add_argument("--api_key", type=str, default="", help="DeepSeek API key")
    parser.add_argument("--limit", type=int, default=10, help="Maximum number of items to process")
    parser.add_argument("--start_idx", type=int, default=0, help="Starting index for processing (for resuming)")
    parser.add_argument("--batch_size", type=int, default=10, help="Number of items to process in each batch")
    parser.add_argument("--mix_ratio", type=float, default=0.5, help="Ratio of HotpotQA to StrategyQA records in mixed dataset")
    parser.add_argument("--balance_tool_usage", action="store_true", help="Balance tool usage in the mixed dataset")
    parser.add_argument("--process_hotpotqa", action="store_true", help="Process HotpotQA dataset")
    parser.add_argument("--process_strategyqa", action="store_true", help="Process StrategyQA dataset")
    parser.add_argument("--process_commonsenseqa", action="store_true", help="Process CommonsenseQA dataset")
    parser.add_argument("--create_mixed", action="store_true", help="Create mixed dataset")
    parser.add_argument("--model", type=str, default="deepseek-chat", choices=["deepseek-chat", "deepseek-coder", "deepseek-reasoner"], help="DeepSeek model to use")
    parser.add_argument("--temperature", type=float, default=0.3, help="Temperature for generation (lower for more consistent outputs)")
    parser.add_argument("--strategyqa_temperature", type=float, default=0.2, help="Temperature for StrategyQA generation (lower for more consistent outputs)")
    parser.add_argument("--test_mode", action="store_true", help="Process just a few examples to test the setup")
    parser.add_argument("--verify_outputs", action="store_true", help="Verify outputs after processing (prints samples)")

    args = parser.parse_args()

    # Get DeepSeek API key
    api_key = args.api_key or os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        raise ValueError("Please provide a DeepSeek API key using --api_key or set the DEEPSEEK_API_KEY environment variable")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Process HotpotQA dataset
    if args.process_hotpotqa:
        print(f"Loading HotpotQA data from {args.hotpotqa_file}...")
        hotpotqa_data = load_json_data(args.hotpotqa_file)
        print(f"Loaded {len(hotpotqa_data)} items from HotpotQA dataset")

        # If test mode is enabled, process only a few examples
        if args.test_mode:
            print(f"Test mode enabled: processing only 3 examples...")
            limit = 3
            batch_size = 3
        else:
            limit = args.limit
            batch_size = args.batch_size

        print(f"Processing HotpotQA data with DeepSeek (model: {args.model}, temperature: {args.temperature})...")
        hotpotqa_result = process_hotpotqa_data(
            data=hotpotqa_data,
            api_key=api_key,
            limit=limit,
            start_idx=args.start_idx,
            batch_size=batch_size,
            output_dir=args.output_dir,
            model=args.model,
            temperature=args.temperature
        )

        # Print verification statistics
        verification_stats = hotpotqa_result["stats"]
        print("\nHotpotQA Verification Statistics:")
        print(f"Total processed: {verification_stats['total']}")
        print(f"Matched answers: {verification_stats['matched']} ({verification_stats['accuracy']:.2f}%)")
        print(f"Mismatched answers: {verification_stats['mismatched']}")
        print(f"Tool usage: {verification_stats['tool_usage']} samples ({verification_stats['tool_usage_percentage']:.2f}%)")
        print(f"Total tool calls: {verification_stats['total_searchs']}")
        print(f"Average tool calls per sample: {verification_stats['avg_searchs']:.2f}")
        print(f"Average tool calls when tools are used: {verification_stats['avg_searchs_when_used']:.2f}")

    # Process StrategyQA dataset
    if args.process_strategyqa and args.strategyqa_file:
        print(f"Loading StrategyQA data from {args.strategyqa_file}...")
        strategyqa_data = load_json_data(args.strategyqa_file)
        print(f"Loaded {len(strategyqa_data)} items from StrategyQA dataset")

        # If test mode is enabled, process only a few examples
        if args.test_mode:
            print(f"Test mode enabled: processing only 3 examples...")
            limit = 3
            batch_size = 3
        else:
            limit = args.limit
            batch_size = args.batch_size

        # Use a lower temperature for StrategyQA to encourage more consistent outputs
        strategyqa_temp = args.strategyqa_temperature if hasattr(args, 'strategyqa_temperature') else args.temperature

        print(f"Processing StrategyQA data with DeepSeek (model: {args.model}, temperature: {strategyqa_temp})...")
        strategyqa_result = process_strategyqa_data(
            data=strategyqa_data,
            api_key=api_key,
            limit=limit,
            start_idx=args.start_idx,
            batch_size=batch_size,
            output_dir=args.output_dir,
            model=args.model,
            temperature=strategyqa_temp
        )

        # Print verification statistics
        verification_stats = strategyqa_result["stats"]
        print("\nStrategyQA Verification Statistics:")
        print(f"Total processed: {verification_stats['total']}")
        print(f"Matched answers: {verification_stats['matched']} ({verification_stats['accuracy']:.2f}%)")
        print(f"Mismatched answers: {verification_stats['mismatched']}")
        print(f"Tool usage: {verification_stats['tool_usage']} samples ({verification_stats['tool_usage_percentage']:.2f}%)")
        print(f"Total tool calls: {verification_stats['total_searchs']}")
        print(f"Average tool calls per sample: {verification_stats['avg_searchs']:.2f}")
        print(f"Average tool calls when tools are used: {verification_stats['avg_searchs_when_used']:.2f}")

    # Process CommonsenseQA dataset
    if args.process_commonsenseqa:
        print(f"Loading CommonsenseQA data from {args.commonsenseqa_file}...")
        with open(args.commonsenseqa_file, "r", encoding="utf-8") as f:
            commonsenseqa_data = [json.loads(line) for line in f]
        print(f"Loaded {len(commonsenseqa_data)} items from CommonsenseQA dataset")

        # If test mode is enabled, process only a few examples
        if args.test_mode:
            print(f"Test mode enabled: processing only 3 examples...")
            limit = 3
            batch_size = 3
        else:
            limit = args.limit
            batch_size = args.batch_size

        print(f"Processing CommonsenseQA data with DeepSeek (model: {args.model}, temperature: {args.temperature})...")
        commonsenseqa_result = process_commonsenseqa_data(
            data=commonsenseqa_data,
            api_key=api_key,
            limit=limit,
            start_idx=args.start_idx,
            batch_size=batch_size,
            output_dir=args.output_dir,
            model=args.model,
            temperature=args.temperature
        )

        # Print verification statistics
        verification_stats = commonsenseqa_result["stats"]
        print("\nCommonsenseQA Verification Statistics:")
        print(f"Total processed: {verification_stats['total']}")
        print(f"Matched answers: {verification_stats['matched']} ({verification_stats['accuracy']:.2f}%)")
        print(f"Mismatched answers: {verification_stats['mismatched']}")
        print(f"Tool usage: {verification_stats['tool_usage']} samples ({verification_stats['tool_usage_percentage']:.2f}%)")
        print(f"Total tool calls: {verification_stats['total_searchs']}")
        print(f"Average tool calls per sample: {verification_stats['avg_searchs']:.2f}")
        print(f"Average tool calls when tools are used: {verification_stats['avg_searchs_when_used']:.2f}")

    # Create mixed dataset
    if args.create_mixed:
        # Load processed records
        hotpotqa_records_file = os.path.join(args.output_dir, "hotpotqa_processed_records.json")
        strategyqa_records_file = os.path.join(args.output_dir, "strategyqa_processed_records.json")

        if os.path.exists(hotpotqa_records_file) and os.path.exists(strategyqa_records_file):
            with open(hotpotqa_records_file, 'r', encoding='utf-8') as f:
                hotpotqa_records = json.load(f)

            with open(strategyqa_records_file, 'r', encoding='utf-8') as f:
                strategyqa_records = json.load(f)

            print(f"Loaded {len(hotpotqa_records)} HotpotQA records and {len(strategyqa_records)} StrategyQA records")

            # Create mixed dataset
            mixed_dataset_file = os.path.join(args.output_dir, "mixed_dataset.json")
            create_mixed_dataset(
                hotpotqa_records=hotpotqa_records,
                strategyqa_records=strategyqa_records,
                output_file=mixed_dataset_file,
                ratio=args.mix_ratio,
                balance_tool_usage=args.balance_tool_usage
            )
        else:
            print("Cannot create mixed dataset: processed records files not found")

    print("Processing complete!")

    # Verify outputs if requested
    if args.verify_outputs:
        print("\n=== VERIFYING OUTPUTS ===")

        # Check HotpotQA outputs
        hotpotqa_records_file = os.path.join(args.output_dir, "hotpotqa_processed_records.json")
        if os.path.exists(hotpotqa_records_file):
            with open(hotpotqa_records_file, 'r', encoding='utf-8') as f:
                hotpotqa_records = json.load(f)

            print(f"\n=== SAMPLE HOTPOTQA OUTPUT ({len(hotpotqa_records)} records total) ===")
            if hotpotqa_records:
                sample = random.choice(hotpotqa_records)
                print(f"Input: {sample['input']}")

                # Print a truncated version of the output for readability
                output = sample['output']
                if len(output) > 500:
                    print(f"Output (truncated): {output[:500]}...\n...{output[-200:]}")
                else:
                    print(f"Output: {output}")

                # Count tool calls and check for think tag
                tool_calls = output.count("<tool_call>")
                has_think = "<think>" in output

                # Get metadata if available
                metadata_file = os.path.join(args.output_dir, "hotpotqa_metadata.json")
                format_fixed = False
                search_count = tool_calls  # Default to count from output
                if os.path.exists(metadata_file):
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        for item in metadata:
                            if item.get("question") in sample['input']:
                                format_fixed = item.get("format_fixed", False)
                                search_count = item.get("search_count", tool_calls)
                                break

                print(f"Number of tool calls: {search_count}")
                print(f"Has <think> tag: {has_think}")
                print(f"Format was fixed: {format_fixed}")

        # Check StrategyQA outputs
        strategyqa_records_file = os.path.join(args.output_dir, "strategyqa_processed_records.json")
        if os.path.exists(strategyqa_records_file):
            with open(strategyqa_records_file, 'r', encoding='utf-8') as f:
                strategyqa_records = json.load(f)

            print(f"\n=== SAMPLE STRATEGYQA OUTPUT ({len(strategyqa_records)} records total) ===")
            if strategyqa_records:
                sample = random.choice(strategyqa_records)
                print(f"Input: {sample['input']}")

                # Print a truncated version of the output for readability
                output = sample['output']
                if len(output) > 500:
                    print(f"Output (truncated): {output[:500]}...\n...{output[-200:]}")
                else:
                    print(f"Output: {output}")

                # Count tool calls and check for think tag
                tool_calls = output.count("<tool_call>")
                has_think = "<think>" in output

                # Get metadata if available
                metadata_file = os.path.join(args.output_dir, "strategyqa_metadata.json")
                format_fixed = False
                search_count = tool_calls  # Default to count from output
                if os.path.exists(metadata_file):
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        for item in metadata:
                            if item.get("question") in sample['input']:
                                format_fixed = item.get("format_fixed", False)
                                search_count = item.get("search_count", tool_calls)
                                break

                print(f"Number of tool calls: {search_count}")
                print(f"Has <think> tag: {has_think}")
                print(f"Format was fixed: {format_fixed}")

        # Check CommonsenseQA outputs
        commonsenseqa_records_file = os.path.join(args.output_dir, "commonsenseqa_processed_records.json")
        if os.path.exists(commonsenseqa_records_file):
            with open(commonsenseqa_records_file, 'r', encoding='utf-8') as f:
                commonsenseqa_records = json.load(f)

            print(f"\n=== SAMPLE COMMONSENSEQA OUTPUT ({len(commonsenseqa_records)} records total) ===")
            if commonsenseqa_records:
                sample = random.choice(commonsenseqa_records)
                print(f"Input: {sample['input']}")

                # Print a truncated version of the output for readability
                output = sample['output']
                if len(output) > 500:
                    print(f"Output (truncated): {output[:500]}...\n...{output[-200:]}")
                else:
                    print(f"Output: {output}")

                # Count tool calls and check for think tag
                tool_calls = output.count("<tool_call>")
                has_think = "<think>" in output

                # Get metadata if available
                metadata_file = os.path.join(args.output_dir, "commonsenseqa_metadata.json")
                format_fixed = False
                search_count = tool_calls  # Default to count from output
                if os.path.exists(metadata_file):
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        for item in metadata:
                            if item.get("question") in sample['input']:
                                format_fixed = item.get("format_fixed", False)
                                search_count = item.get("search_count", tool_calls)
                                break

                print(f"Number of tool calls: {search_count}")
                print(f"Has <think> tag: {has_think}")
                print(f"Format was fixed: {format_fixed}")
                print(f"Answer format: {output.split('<response>')[1].split('</response>')[0].strip()}")  # 显示答案格式（A/B/C/D/E）

    print("\nYou can now use the processed data to train your model.")

if __name__ == "__main__":
    main()