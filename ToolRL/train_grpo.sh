#!/bin/bash

# GPU Configuration
export CUDA_VISIBLE_DEVICES=0,1,2,3
export N_GPUS=4
export ROLLOUT_TP_SIZE=1
export VLLM_ATTENTION_BACKEND=XFORMERS

# All the env variables below are set to 0 by default
export WITHLENGTH=0
export REFINEDREWARD=0
export COARSEREWARD=0
export STRICTMATCH=0
export CORRECTMAX1=0
export MAX1STEP30MAX3=0
export SCHEDULEREWARD=0
export SCHEDULELENGTH=0

# Paths and experiment configuration
export DATA_DIR="/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/dataset/train"
export BASE_MODEL="/user/xiaohuan.bing/u15359/.project/dir.project/ToolRL/base_model/Qwen2.5-3B-Instruct"
export EXPERIMENT_NAME="grpo-smartsearch-qwen2.5-3b"
export REWARD_VIS_DIR="./reward_plots/grpo"


# Run GRPO training
bash ./examples/grpo_trainer/run_grpo.sh 